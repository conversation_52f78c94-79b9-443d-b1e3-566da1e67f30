---
description: 
globs: *.tsx, *.ts
alwaysApply: false
---
# Rule content
- Generate unit testing for golang. Use template the same with previous .test.tsx/.test.ts file
- I need unit test cover all logic in func. I want create a testcase for each condition/logic.
- With each genrate unit test successfully, I need you run unit test by command jest and check & fix error(if have). Only run the newly created test file. Do not run all tests
- If a function already has a unit test, check for any logic changes in that function and update the existing unit test file accordingly
- Summary generation result after completed unit test
- Do not modify the functions in the origin file.
- If I need to fix or update a test, it should automatically run the test and fix any errors if they occur after the update.

# Unit Testing Rules

## Test Structure
- Follow table-driven test pattern if fisible
- Update existed test file instead of create new file
- Test file should be in the same folder as file to be tested with name should be like file_to_be_tested.test.tsx or file_to_be_tested.test.ts (if test pure logic code function without rendering component)


## Running Tests
1. Commands:
   ```bash
   # Run all tests
   cd packages/bnpl-shared && jest
   
   # Run specific test
   cd packages/bnpl-shared && jest TestName


2. Verification:
   - Check test coverage
   - Verify all test cases pass
   - Check for race conditions
   - Verify cleanup of resources



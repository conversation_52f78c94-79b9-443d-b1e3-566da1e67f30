#!/usr/bin/env bash
root=$(git rev-parse --show-toplevel)
cd "$root" || exit 1

#check debug code annotation (@debug) to prevent debug code committed
check_debug_code(){
  result=$(grep -r -w -n "$1" --exclude-dir={.husky,node_modules})
  if [ -n  "$result" ] ; then
      printf 'Remove @debug code before commit:\n%s' "$result"
      exit 1
  fi
}

ensure_typescript_compile(){
  cd "$root/packages/bnpl-shared"
  yarn run tsc
}

check_debug_code '@debug'
ensure_typescript_compile

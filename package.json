{"name": "bnpl-apps", "version": "2.4.0", "description": "Monorepo for bnpl-apps", "author": "bnpl-team", "private": true, "workspaces": {"packages": ["packages/*"], "nohoist": ["**/react", "**/react/**", "**/react-dom", "**/react-native", "**/victory-native", "**/react-native-elements", "**/react-native-svg", "**/react-native-vector-icons", "**/react-native-swiper", "**/react-native-vector-icons/**", "**/react-native/**"]}, "scripts": {"postinstall": "lerna run postinstall", "bootstrap": "lerna bootstrap --use-workspaces", "build": "yarn bnpl-zpi:build", "reset": "find . -type dir -name node_modules | xargs rm -rf", "jest:unit": "jest", "jest:coverage": "jest coverage", "test": "yarn bnpl-app:test && cp -R packages/bnpl-app/coverage/ coverage", "bnpl-zpi:ma-start": "yarn workspace bnpl-zpi ma-start", "bnpl-zpi:wp-build": "yarn workspace bnpl-zpi wp-build", "bnpl-zpi:start": "yarn workspace bnpl-zpi start", "bnpl-zpi:build": "yarn workspace bnpl-zpi build", "bnpl-zpi:serve": "yarn workspace bnpl-zpi serve", "bnpl-app:start": "yarn workspace bnpl-app start", "bnpl-app:lint": "yarn workspace bnpl-app lint", "bnpl-app:test": "yarn workspace bnpl-app test", "bnpl-app:test:ci": "yarn workspace bnpl-app test:ci", "bnpl-app:test:watch": "yarn workspace bnpl-app test:watch", "bnpl-app:test:coverage": "yarn workspace bnpl-app test:coverage", "bnpl-app:build:sandbox": "yarn workspace bnpl-app build:sandbox", "bnpl-app:build:production": "yarn workspace bnpl-app build:production", "prepare": "husky install", "sync_image_res": "yarn workspace bnpl-shared sync_image_res"}, "devDependencies": {"@jest/fake-timers": "^27.5.1", "@types/jest": "^27.4.1", "@types/node": "^17.0.9", "@types/react": "^17.0.14", "@types/react-dom": "^17.0.14", "@types/react-native": "^0.64.12", "@typescript-eslint/eslint-plugin": "^5.9.1", "@typescript-eslint/parser": "^5.9.1", "eslint": "^7.30.0", "eslint-config-prettier": "^8.3.0", "eslint-plugin-prettier": "^4.0.0", "eslint-plugin-react-app": "^6.2.2", "eslint-plugin-react-native": "^4.0.0", "husky": "^8.0.0", "jest-junit": "^14.0.1", "lerna": "^4.0.0", "patch-package": "^6.4.7", "prettier": "^2.3.2", "react-native-monorepo-tools": "^1.1.4", "typescript": "^4.4.3", "typescript-plugin-css-modules": "^3.4.0"}, "dependencies": {"react": "16.8.3"}}
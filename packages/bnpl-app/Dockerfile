# pull the official base image
FROM nikolaik/python-nodejs:python3.8-nodejs14-alpine
# set working direction
WORKDIR /app
# add `/app/node_modules/.bin` to $PATH
# install application dependencies
RUN apk update \
	 && apk add rsync openssh zip

COPY package.json ./
COPY yarn.lock ./
RUN yarn install
# add app
COPY . ./
# start app
RUN mkdir -p /app/ZALOPAY_SVN
# RUN mkdir -p /app/ZALOPAY_SVN/sandbox/ps_res/ios/js
# RUN mkdir -p /app/ZALOPAY_SVN/sandbox/ps_res/android/js
# RUN mkdir -p /app/ZALOPAY_SVN/sandbox/ps_res/android/images/
# RUN mkdir -p /app/ZALOPAY_SVN/sandbox/ps_res/ios/images/

# RUN export ZALOPAY_SVN=/app/ZALOPAY_SVN
ENV ZALOPAY_SVN=/app/ZALOPAY_SVN

RUN pip3 install requests
RUN pip3 install pick
RUN cd script && python3 build.py sandbox
CMD ["npm", "start"]
module.exports = {
  preset: 'react-native',
  roots: ['<rootDir>'],
  moduleFileExtensions: ['ts', 'tsx', 'js', 'jsx', 'json', 'node'],
  setupFiles: ['<rootDir>/jest/setup.js'],
  setupFilesAfterEnv: ['<rootDir>/jest/setupFilesAfterEnv.js'],
  coverageReporters: ['lcov', 'text', 'json-summary', 'cobertura'],
  moduleNameMapper: {
    '^src/(.*)': '<rootDir>/src/$1',
  },
  reporters: [
    'default',
    [
      'jest-sonar',
      {
        outputName: 'test-reporter.xml',
      },
    ],
  ],
  collectCoverageFrom: ['src/**/*.{js,jsx,ts,tsx}'],
  transformIgnorePatterns: ['node_modules/react-native-swiper'],
};

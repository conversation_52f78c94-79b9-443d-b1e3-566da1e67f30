/**
 * This file stores sample data to be used in unit tests.
 */

import { Transaction, TransactionStatus, TransactionType } from 'bnpl-shared/src/store/types';

export const FIXTURE_DEPOSIT_TRANSACTION: Transaction = {
  id: 0,
  type: TransactionType.deposit,
  status: TransactionStatus.succeeded,
  amount: 100001,
  description: 'Deposit money',
  interest_rate: 5,
  interest_date: '2021-01-01T01:01:01.000Z',
  interest_starting_date: '2021-01-01T01:01:01.000Z',
  transaction_time: '2021-01-01T01:01:01.000Z',
  fee: 0,
  zp_trans_id: 0,
  meta_data: {},
};

export const FIXTURE_ADJUST_TRANSACTION: Transaction = {
  id: 0,
  type: TransactionType.adjustment,
  status: TransactionStatus.succeeded,
  amount: -100001,
  description: 'Đi<PERSON>u chỉnh số dư',
  interest_date: '2021-01-01T01:01:01.000Z',
  transaction_time: '2021-01-01T01:01:01.000Z',
  fee: 0,
  zp_trans_id: 0,
  meta_data: {},
};

export const FIXTURE_PENDING_TRANSACTION: Transaction = {
  id: 0,
  type: TransactionType.deposit,
  status: TransactionStatus.pending,
  amount: 100001,
  description: 'Deposit money',
  interest_date: '2021-01-01T01:01:01.000Z',
  transaction_time: '2021-01-01T01:01:01.000Z',
  fee: 0,
  zp_trans_id: 0,
  meta_data: {},
};

export const FIXTURE_FAILED_TRANSACTION: Transaction = {
  id: 0,
  type: TransactionType.deposit,
  status: TransactionStatus.failed,
  amount: 100001,
  description: 'Deposit money',
  status_description: 'Hello this is a failed transaction',
  interest_date: '2021-01-01T01:01:01.000Z',
  transaction_time: '2021-01-01T01:01:01.000Z',
  fee: 0,
  zp_trans_id: 0,
  meta_data: {},
};

export const FIXTURE_INTEREST_TRANSACTION: Transaction = {
  id: 0,
  type: TransactionType.interest,
  status: TransactionStatus.succeeded,
  amount: 500,
  description: 'Interest amount',
  interest_date: '2021-01-01T01:01:01.000Z',
  transaction_time: '2021-01-01T01:01:01.000Z',
  fee: 0,
  zp_trans_id: 0,
  meta_data: {
    '0.05': 500,
  },
};

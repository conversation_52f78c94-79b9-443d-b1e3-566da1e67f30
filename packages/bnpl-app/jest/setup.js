import mockAsyncStorage from '@react-native-community/async-storage/jest/async-storage-mock';
jest.mock('@react-native-community/async-storage', () => mockAsyncStorage);

global.__DEV__ = true;

global.window = global;

global.flushPromises = () => new Promise(setImmediate);

window.addEventListener = () => {};

jest.mock('src/utils/ZaloPayModules', () => ({
  __esModule: true,
  default: {
    getAppUserInfo: jest
      .fn()
      .mockResolvedValue({ data: { userid: 123456789, maccesstoken: 'mAccessToken', muid: 'mUid' } }),
    uniqueId: 'deviceId',
    launchDeepLinkRN: jest.fn(),
    showLoadingRN: jest.fn(),
    hideLoadingRN: jest.fn(),
    showLoading: jest.fn(),
    hideLoading: jest.fn(),
    payOrderRN: jest.fn(),
    promptPINRN: jest.fn().mockReturnValue({ code: 1 }),
    getBalance: () => ({ code: 1, data: { balance: '500000' } }),
    trackEvent: jest.fn(),
    showDialogRN: jest.fn(),
  },
  getUserId: () => 123456789,
}));

jest.mock('bnpl-shared/src/shared/adapters/sentryAdapter', () => ({
  init: jest.fn(),
  captureMessage: jest.fn(),
  captureException: jest.fn(),
  setUser: jest.fn(),
}));

jest.mock('react-native-linear-gradient', () => 'LinearGradient');

jest.mock('react-native-swiper', () => 'Swiper');

jest.mock('bnpl-shared/src/shared/adapters/netinfoAdapter', () => ({
  isConnected: {
    fetch: jest.fn().mockResolvedValue(true),
  },
}));

jest
  .mock('src/api/getTransaction')
  .mock('src/api/getTransactions')
  .mock('src/api/getZaloPayBalance')
  .mock('src/api/submitUserToBinding')
  .mock('src/api/getCurrentUserInformation')
  .mock('src/api/getCurrentOffer')
  .mock('src/api/getUserContract')
  .mock('src/api/getSavingChart')
  .mock('src/api/createRedemptionRequest')
  .mock('src/screens/account/AccountDetailScreen/useFocusEffect')
  .mock('src/screens/Transfer/getCurrentNativeVersion')
  .mock('src/api/getMaintenanceStatus');

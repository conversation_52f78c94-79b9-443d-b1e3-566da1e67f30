import 'react-native';
import 'jest-enzyme';
import Adapter from 'enzyme-adapter-react-16';
import Enzyme from 'enzyme';

/**
 * Set up DOM in node.js environment for Enzyme to mount to
 */
const { JSDOM } = require('jsdom');

const jsdom = new JSDOM('<!doctype html><html><body></body></html>');
const { window } = jsdom;

function copyProps(src, target) {
  Object.defineProperties(target, {
    ...Object.getOwnPropertyDescriptors(src),
    ...Object.getOwnPropertyDescriptors(target),
  });
}

global.window = window;
global.document = window.document;
global.navigator = {
  userAgent: 'node.js',
};
copyProps(window, global);

/**
 * Set up Enzyme to mount to DOM, simulate events,
 * and inspect the DOM in tests.
 */
Enzyme.configure({ adapter: new Adapter() });

function suppressDomErrors() {
  const suppressedErrors =
    /(React does not recognize the.*prop on a DOM element|Unknown event handler property|is using uppercase HTML|Received .*for a non-boolean attribute|The tag.*is unrecognized in this browser|PascalCase|This ensures that you're testing the behavior the user would see in the browser)/;
  // eslint-disable-next-line no-console
  const realConsoleError = console.error;
  // eslint-disable-next-line no-console
  console.error = (error, ...args) => {
    let errorMessage = typeof error === 'string' ? error : error.message;
    if (errorMessage.match(suppressedErrors)) {
      return;
    }
    args.forEach(argument => {
      errorMessage = errorMessage.replace(/%(s|d|i|o|O)/, argument);
    });
    realConsoleError(errorMessage);
  };
}
suppressDomErrors();

const { getDefaultConfig } = require('metro-config');
const blacklist = require('metro-config/src/defaults/blacklist');
const { getMetroTools, getMetroAndroidAssetsResolutionFix } = require('react-native-monorepo-tools');

const androidAssetsResolutionFix = getMetroAndroidAssetsResolutionFix();

const monorepoMetroTools = getMetroTools();
const [workspaceName] = __dirname.split('/').pop();
const blacklistRE = monorepoMetroTools.blockList.map(value => {
  value += '';
  const workspaceNameIndex = value.indexOf(workspaceName);
  const start = value.slice(1, workspaceNameIndex);
  const end = value.slice(workspaceNameIndex, value.length - 1);
  return new RegExp(start + /packages\//.source + end);
});

module.exports = (async () => {
  const {
    resolver: { sourceExts, assetExts },
  } = await getDefaultConfig();

  const publicPathsConfig = process.env.IS_DEVELOPMENT ? {} : { publicPath: androidAssetsResolutionFix.publicPath };

  return {
    transformer: {
      ...publicPathsConfig,
      getTransformOptions: async () => ({
        transform: {
          experimentalImportSupport: false,
          inlineRequires: true,
        },
      }),
    },
    server: {
      // ...and to the server middleware.
      enhanceMiddleware: middleware => {
        if (process.env.IS_DEVELOPMENT) {
          return middleware;
        }
        return androidAssetsResolutionFix.applyMiddleware(middleware);
      },
    },
    // Add additional Yarn workspace package roots to the module map.
    // This allows importing importing from all the project's packages.
    watchFolders: monorepoMetroTools.watchFolders,
    resolver: {
      assetExts,
      sourceExts: [...sourceExts, 'jsx'],
      blacklistRE: blacklist(blacklistRE),
      extraNodeModules: monorepoMetroTools.extraNodeModules,
    },
  };
})();

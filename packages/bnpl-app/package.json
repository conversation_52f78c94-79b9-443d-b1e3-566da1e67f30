{"name": "bnpl-app", "version": "2.4.0", "build": "1", "private": true, "app-id": "1841", "zalopay-version": "6.4.1", "description": "ZaloPay Internal Functionalities", "scripts": {"start": "NODE_OPTIONS=--openssl-legacy-provider IS_DEVELOPMENT=1 node node_modules/react-native/local-cli/cli.js start --reset-cache", "test": "jest --env=jsdom", "test:ci": "jest --env=jsdom --updateSnapshot --coverage --watchAll=false --verbose=false --testTimeout=20000 --maxWorker=4 && jest-coverage-badges", "test:watch": "jest --env=jsdom --watch --watchAll=true --collectCoverageFrom=[] --verbose=true --testTimeout=20000", "test:coverage": "jest --coverage --watchAll=false --verbose=true --testTimeout=20000 --env=jsdom", "view:coverage": "open coverage/lcov-report/index.html", "lint": "eslint . && tsc --build ./tsconfig.json", "build:sandbox": "cd script && python3 build.py sandbox", "build:production": "cd script && python3 build.py staging", "type-check": "tsc --build ./tsconfig.json", "postinstall": "patch-package"}, "dependencies": {"@react-native-community/async-storage": "^1.5.0", "@react-native-community/netinfo": "^2.0.10", "@reduxjs/toolkit": "^1.7.1", "@sentry/react-native": "2.0.2", "base64url": "^3.0.1", "buffer": "^6.0.3", "date-fns": "^2.24.0", "lodash": "^4.17.15", "lodash.memoize": "^4.1.2", "patch-package": "^6.4.7", "postinstall-postinstall": "^2.1.0", "prop-types": "^15.6.0", "query-string": "^6.11.0", "react": "16.8.3", "react-hook-form": "^7.15.4", "react-native": "0.59.10", "react-native-autoheight-webview": "1.5.2", "react-native-device-info": "^8.4.8", "react-native-elements": "^3.4.2", "react-native-linear-gradient": "^2.5.6", "react-native-safe-area-context": "^3.2.0", "react-native-svg": "9.3.7", "react-native-swiper": "^1.5.14", "react-native-vector-icons": "^8.1.0", "react-native-webview": "5", "react-navigation": "^2.18.3", "react-redux": "^7.2.6", "redux": "^4.0.5", "redux-logger": "^3.0.6", "redux-thunk": "^2.3.0", "reselect": "^4.0.0", "victory-native": "^35.5.5", "bnpl-shared": "*"}, "devDependencies": {"@babel/core": "^7.4.5", "@react-native-community/eslint-config": "^3.0.0", "@testing-library/react-native": "^7.2.0", "@types/enzyme": "^3.10.9", "@types/enzyme-adapter-react-16": "^1.0.6", "@types/jest": "^26.0.24", "@types/lodash": "^4.14.171", "@types/react": "*", "@types/react-native": "*", "@types/react-native-base64": "^0.2.0", "@types/react-navigation": "2.13.10", "@types/react-test-renderer": "^17.0.1", "babel-jest": "23.6.0", "babel-plugin-module-resolver": "^4.1.0", "babel-plugin-transform-remove-console": "^6.9.4", "babel-preset-react-native": "^4.0.1", "c8": "^7.9.0", "enzyme": "^3.11.0", "enzyme-adapter-react-16": "^1.15.6", "enzyme-to-json": "^3.6.2", "jest": "24.8.0", "jest-coverage-badges": "^1.1.2", "jest-enzyme": "^7.1.2", "jest-sonar": "^0.2.12", "metro-react-native-babel-preset": "^0.54.1", "react-dom": "16.8.6", "react-native-typescript-transformer": "^1.2.13", "react-test-renderer": "16.8.6", "redux-logger": "^3.0.6"}}
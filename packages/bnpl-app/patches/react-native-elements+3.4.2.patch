diff --git a/node_modules/react-native-elements/dist/tooltip/Tooltip.js b/node_modules/react-native-elements/dist/tooltip/Tooltip.js
index b9fae92..0b709f9 100644
--- a/node_modules/react-native-elements/dist/tooltip/Tooltip.js
+++ b/node_modules/react-native-elements/dist/tooltip/Tooltip.js
@@ -198,7 +198,7 @@ class Tooltip extends React.Component {
             this.renderedElement = e;
         }}>
         {this.renderContent(false)}
-        <ModalComponent animationType="fade" visible={isVisible} transparent onShow={onOpen}>
+        <ModalComponent visible={isVisible} transparent onShow={onOpen}>
           {this.renderModalContent()}
         </ModalComponent>
       </View>);

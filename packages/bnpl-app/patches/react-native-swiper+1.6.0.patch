diff --git a/node_modules/react-native-swiper/index.d.ts b/node_modules/react-native-swiper/index.d.ts
index 24905e3..1797678 100644
--- a/node_modules/react-native-swiper/index.d.ts
+++ b/node_modules/react-native-swiper/index.d.ts
@@ -35,6 +35,8 @@ declare module 'react-native-swiper' {
     loop?: boolean
     // Index number of initial slide.
     index?: number
+    // (Customized by FS team) Suppress warning about using title props (see node_modules/react-native-swiper/src/index.js, method "renderTitle" for more information).
+    suppressTitleWarning?: boolean;
     // Set to true make control buttons visible.
     showsButtons?: boolean
     // Set to false to disable continuous loop mode.
diff --git a/node_modules/react-native-swiper/src/index.js b/node_modules/react-native-swiper/src/index.js
index 3e63ca7..1fbea3b 100644
--- a/node_modules/react-native-swiper/src/index.js
+++ b/node_modules/react-native-swiper/src/index.js
@@ -347,15 +347,15 @@ export default class extends Component {
           } else if (this.state.index === this.state.total - 1) {
             this.props.horizontal === false
               ? this.scrollView.scrollTo({
-                  x: 0,
-                  y: this.state.height * this.state.total,
-                  animated: false
-                })
+                x: 0,
+                y: this.state.height * this.state.total,
+                animated: false
+              })
               : this.scrollView.scrollTo({
-                  x: this.state.width * this.state.total,
-                  y: 0,
-                  animated: false
-                })
+                x: this.state.width * this.state.total,
+                y: 0,
+                animated: false
+              })
           }
         }
       },
@@ -689,12 +689,19 @@ export default class extends Component {
 
   renderTitle = () => {
     const child = this.state.children[this.state.index]
+    const _title = child && child.props && child.props._title
     const title = child && child.props && child.props.title
-    return title ? (
-      <View style={styles.title}>
-        {this.state.children[this.state.index].props.title}
+    const suppressTitleWarning = this.props.suppressTitleWarning
+    if (_title) {
+      return <View style={styles.title}>
+        {this.state.children[this.state.index].props._title}
       </View>
-    ) : null
+    }
+    if (title) {
+      if (!suppressTitleWarning) {
+        console.warn("react-native-swiper uses child's `title` prop to render a title component. We changed it to `_title`, because we sometimes use `title` and don't want this behavior. If you need to use the original behavior, use `_title`. To suppress this warning, pass `true` via prop `suppressTitleWarning`.")
+      }
+    }
   }
 
   renderNextButton = () => {

#!/bin/sh

output_ios=./output/ios
output_android=./output/android

rm -rf ./output

mkdir -p $output_ios
mkdir -p $output_android

npx react-native-cli bundle --dev false --entry-file index.js --bundle-output $output_ios/main.jsbundle --sourcemap-output $output_ios/main.jsbundle.map --platform ios --assets-dest $output_ios --verbose
echo ">> Build ios done"

npx react-native-cli bundle --dev false --entry-file index.js --bundle-output $output_android/main.jsbundle --sourcemap-output $output_android/index.android.bundle.map --platform android --assets-dest $output_android --verbose
echo ">> Build android done"

# cp -R fonts $output_ios/
# cp -R fonts $output_android/

echo "Done building resources"

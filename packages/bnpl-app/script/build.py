from pick import pick
from os import walk
import os
from os.path import join, exists
from buildoneapp import build_app, copyToSvn
import sys
import json
import shutil
import time
from common import yes_no
import result
import subprocess


def load_build_file():
  with open('./build.json') as data_file:    
     return json.loads(data_file.read())['apps']

def load_package_file():
  with open('../package.json') as data_file:    
     return json.loads(data_file.read())

def choose_app():

  options = []
  for f in apps_info:
      options.append(f['name'])
  selected = [(apps_info[0]['name'], 0)]
  # title = 'Please choose apps (press SPACE to mark, ENTER to continue): '
  # selected = pick(options, title, multi_select=True, min_selection_count=1)
  print('✅ =>>> APP BUILD:', selected)
  return selected

apps_info = load_build_file()
package_app_info = load_package_file()

os.chdir('../')
root_dir = os.getcwd()
apps = choose_app()

sandbox = False
staging = False
for param in sys.argv:
  if param == 'sandbox':
    sandbox = True
  if param == 'staging':
    staging = True
if not (sandbox or staging):
  exit()

print ('>>> Environment: ')
if sandbox:
  print ('>>> SANDBOX')
if staging:
  print ('>>> STAGING')
# version =  input("Version: ")
version = package_app_info['version']
print("==> APP version", package_app_info['version'])
appJson  = []
resultJson = []

for app_name,index in apps:
  
  app = apps_info[index]
  app_id = app['appid']
  copyOnly = False
  print('>>>>> APP: %s|appid: %s' %(app_name, app_id))

  if 'copyonly' in app:
    copyOnly = app['copyonly']

  zip_name = ''
  if not copyOnly:
    # build_number = input("Build number: ")
    build_number = print("('>>>>> BUILD Number: ", package_app_info['build'])
    build_number = package_app_info['build']
    zip_name = "%s.%s.%s_%s.zip" %(str(app_id).zfill(3), version, time.strftime("%Y%m%d"), build_number.zfill(3))
  else:
    if 'zip' in app:
      zip_name = app['zip']
  print (zip_name)
  if zip_name == '':
    print('Skip APP: %s|appid: %s' %(app_name, app_id))
    continue
  app_info = {
    key:app[key] for key in app
  }
  app_info['zip'] = zip_name
  app_info['copyonly'] = copyOnly
  print (app_info)
  appJson.append(app_info)

result = []

def build_app_react_native(root_dir, sandbox, staging, zip_name):
  print('Start build app react native...')
  build_react_native_dir =  join(root_dir, 'script/build-react-native.sh')
  # os.system('bash %s' %build_react_native_dir)
  subprocess.run(build_react_native_dir)
  print('END Build react native')

  ios_mainjs = 'output/ios/main.jsbundle'
  android_mainjs = 'output/android/main.jsbundle'
  if not exists(ios_mainjs) or not exists(android_mainjs): 
    print ('Do not found main.jsbundle appid: %s ' %(app_id))
    sys.exit()
  build_app(sandbox, staging, zip_name)

for app in appJson:
  os.chdir(root_dir)
  app_id = app['appid']
  zip_name = app['zip']
  app_dir = app['path']
  copyOnly = app['copyonly']
  app.pop('copyonly', 0)
  app.pop('path', 0)
  print('>>>>> BUILD APP: |name: %s|id: %s|%s' %(zip_name,app_id, app_dir))
  print(os.getcwd())
  print (app_dir)
  os.chdir(app_dir)
  root_app = os.getcwd()

  if not copyOnly:
    print('root_app DEBUG: ', root_app)
    print('zip_name DEBUG: ', zip_name)
    build_app_react_native(root_dir, sandbox, staging, zip_name)
    print('✅ Build App Successfully =================================')
    copyToSvn(sandbox, staging, zip_name, root_app)
    print('✅ copyToSvn Successfully =================================')
  else: 
    print('root_app: ELSE', root_app)
    print('zip_name: ELSE', zip_name)

    copyToSvn(sandbox, staging, zip_name, root_app)
    print('✅ Copy To Svn Successfully =================================')

for app in appJson:
  app_id = app['appid']
  zip_name = app['zip']
  print('>>>>> RESULT APP: |name: %s' %(zip_name))

os.chdir(root_dir)
os.chdir('./script')

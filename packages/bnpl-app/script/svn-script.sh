# How to run this script
# ./script.sh <parent directory>/svn_resources

# script.sh
# checkout the SVN repo and alas it with specified name
svn checkout --depth empty http://***********/svn/svn_MobilePaymentSDK/resource/zalopay-sdk $1
cd $1

checkOutEachEnvironment() {
  svn update --set-depth=immediates $1
  cd $1
  svn update --set-depth=immediates ps_res
  cd ps_res
  svn update --set-depth=immediates android ios
  cd android
  svn update --set-depth=immediates images
  cd ../ios
  svn update --set-depth=immediates images
  cd ../../..
}
checkOutEachEnvironment sandbox
checkOutEachEnvironment staging

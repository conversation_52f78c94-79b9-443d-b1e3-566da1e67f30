import os
import resource
from os.path import join, exists, isfile
import shutil
from common import yes_no

from renames import renameResourceServer, renameAllFolderAndFile
def cloneResource(src_path, target_path):
	resource1x = join(target_path, 'resource1x')
	resource2x = join(target_path, 'resource2x')
	resource3x = join(target_path, 'resource3x')
	if exists(target_path):
		shutil.rmtree(target_path)
	os.makedirs(resource1x)
	os.makedirs(resource2x)
	os.makedirs(resource3x)

	resource.clone_folder_structure(src_path, resource1x)
	resource.clone_folder_structure(src_path, resource2x)
	resource.clone_folder_structure(src_path, resource3x)
	images_dict = resource.svn_map_images(src_path)
	resource.svn_copy_resource(src_path, resource1x, images_dict, 1)
	resource.svn_copy_resource(src_path, resource2x, images_dict, 2)
	resource.svn_copy_resource(src_path, resource3x, images_dict, 3)

	renameResourceServer(target_path)
	
	return (resource1x, resource2x, resource3x)

def getImages(dirpath, filenames, resource_root):
	images = []
	for fn in filenames:
		full_file = join(dirpath, fn)
		if not isfile(full_file):
			continue
		if not fn.endswith('jpg') and not fn.endswith('png'):
			continue
		if ('@2x' in fn or '@3x' in fn):
			continue
		image = full_file.replace(resource_root, '')
		images.append(image)
	return images

def getAllImage(resource_root): 
	for (dirpath, dirnames, filenames) in os.walk(resource_root):
		if len(dirnames) != 0 or dirpath == './':
			continue
		images = getImages(dirpath, filenames, resource_root)
		print(' ===> %s' %(images))

input_path = '../images/input'
output_path  = '../images/output'

svn_sandbox_path = join(os.environ.get('ZALOPAY_SVN'), 'sandbox/cps_images')
svn_staging_path = join(os.environ.get('ZALOPAY_SVN'), 'staging/cps_images')

if yes_no("Rename input [Y/n]: "):
  print('Rename input')
  renameAllFolderAndFile(input_path)

(resource1x, resource2x, resource3x) = cloneResource(input_path, output_path)

getAllImage(resource1x)

if yes_no("Update Themes to sandbox [Y/n]: "):
	if exists(svn_sandbox_path):
		shutil.rmtree(svn_sandbox_path)
	shutil.copytree(output_path, svn_sandbox_path)

if yes_no("Update Themes to staging [Y/n]: "):
	if exists(svn_sandbox_path):
		shutil.rmtree(svn_staging_path)
	shutil.copytree(output_path, svn_staging_path)

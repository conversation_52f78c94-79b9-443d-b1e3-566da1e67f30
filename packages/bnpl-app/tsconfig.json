{"extends": "../../tsconfig.json", "compilerOptions": {"target": "es6", "module": "commonjs", "jsx": "react-native", "noEmit": true, "strict": true, "noUnusedLocals": false, "moduleResolution": "node", "allowSyntheticDefaultImports": true, "esModuleInterop": true, "skipLibCheck": true, "typeRoots": ["node_modules/@types"], "baseUrl": "./", "paths": {"src/*": ["./src/*"], "assets/*": ["./assets/*"]}}, "exclude": ["node_modules"]}
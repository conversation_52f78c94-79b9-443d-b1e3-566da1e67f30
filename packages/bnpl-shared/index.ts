// A fake export to make this file a module
export const _do_not_use_this_value_ = null;

export enum ZPI_ENV {
  SANDBOX_QC = 'sandboxqc',
  STAGING = 'staging',
  PRODUCTION = 'production',
}

declare global {
  interface Window {
    // add you custom properties and methods
    __BASE_NAME__: string;
    __USER_INFO__: string;
    __APP_INFO__: {
      env: string;
      ver: string;
    };
    UM_PIN: {
      openVerifyPinPopup: (params: {
        onSuccess: (data: any) => any | Promise<any>;
        onFailed: (error: any) => any;
        onClose?: () => any;
        title?: string;
        description?: string;
        source?: string;
        metaData?: object;
      }) => void;
    };
    ZPI_TRACKING_SDK: {
      logAction(eventID: string, metadata: string, callback?: (payload: any) => void): void;
    };
    ZPI_SPA_SDK: {
      navigateTo: (url: string, option?: any) => void;
      navigateWithClear: (url: string) => void;
    };
    zlpSdk: {
      Payment: {
        startCashier(obj: { orders: any; options?: any; callback?: (resp?: any) => any }): void;
        onPrePaymentComplete: (args?: any) => void;
        onPrePaymentCancel: (args?: any) => void;
        onPrePaymentFailed: (args?: any) => void;
      };
      Device: {
        appInfo(): Promise<{
          status: string;
          data: {
            environment: 'develop' | 'staging' | 'production';
            platform: 'ZPI' | 'ZPA';
            os?: 'IOS' | 'ANDROID' | 'WEB';
            appVersion: string;
            userAgent: string;
            isIframe: boolean;
          };
        }>;
        startAuthChallenge(obj: {
          source: number;
          authType: number;
          params?: object;
          options?: object;
          metadata?: object;
        }): Promise<any>;
        playHaptic(obj: {
          type: 'LightTap' | 'MediumTap' | 'HeavyTap' | 'Selection' | 'Success' | 'Error';
          milliseconds: number;
        }): Promise<any>;
        openSettings(): Promise<any>;
      };
      Navigator: {
        navigateTo(obj: { url: string; replace?: boolean }): void;
        openDeepLink(obj: { url: string }): void;
        openInternalBrowser(obj: { url: string }): void;
        openExternalBrowser(obj: { url: string }): void;
        openBottomSheetWebview(obj: { url: string; height?: number }): void;
        closeMiniApp(): Promise<any>;
        navigateTo: ({ url, replace }: { url: string; replace?: boolean }) => void;
      };
      UI: {
        quickpayBioPopup(): Promise<any>;
        closeWindow(): Promise<any>;
      };
      Tracking: {
        trackEvent: any;
      };
    };
    Sentry: {
      captureMessage: (message: string, context?: any) => void;
      captureException: (error: any, context?: any) => void;
      configureScope: (callback: (scope: any) => void) => void;
    };
    __ZPI_ZMP_SDK__: { getZlpToken(): { zlp_token: string } };
    ZaloPayJSBridge: any;
    UM_AUTH_CHALLENGE: {
      openAuthChallenge: (params: {
        source: number;
        authType: number;
        params?: object;
        options?: object;
        metadata?: object;
        onCompleted: (result: any) => void;
      }) => void;
    };
  }
}

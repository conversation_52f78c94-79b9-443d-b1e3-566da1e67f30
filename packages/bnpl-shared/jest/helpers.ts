import { Platform } from 'react-native';

/**
 * This Jest helper will ensure Platform.OS remains unchanged after this test suite is over.
 */
export const mockAndRestorePlatformOS = (expectedPlatform: typeof Platform.OS) => {
  let platform: typeof Platform.OS;

  beforeEach(() => {
    platform = Platform.OS;
    Platform.OS = expectedPlatform;
  });

  afterEach(() => {
    Platform.OS = platform;
  });
};

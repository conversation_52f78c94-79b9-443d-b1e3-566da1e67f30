{"name": "bnpl-shared", "version": "0.1.0", "private": true, "scripts": {"postinstall": "patch-package", "test:ci": "node --expose-gc $(yarn bin jest)  --env=jsdom --runInBand --logHeapUsage --config ./jest.config.js --collectCoverage --coverageDirectory=\\\"./coverage\\\" --ci --reporters=default --reporters=jest-junit --watchAll=false && yarn run tsc", "test:local": "node --expose-gc $(yarn bin jest)  --env=jsdom  --maxWorkers=50% --logHeapUsage --config ./jest.config.js --collectCoverage --coverageDirectory=\\\"./coverage\\\" --ci --reporters=default --reporters=jest-junit --watchAll=false && yarn run tsc", "test:watch": "jest --env=jsdom --coverage --verbose=false --watch", "test:report-slow": "jest --env=jsdom --maxWorkers=50% --reporters=jest-slow-test-reporter", "lint": "eslint . && tsc --build ./tsconfig.json", "sync_image_res": "node scripts/generate_image_res.js", "sync_cdn_image_res": "node scripts/generate_image_res_cdn.js"}, "devDependencies": {"@sentry/react-native": "2.0.2", "@testing-library/react": "12.1.2", "@testing-library/react-hooks": "^7.0.2", "@testing-library/react-native": "^9.0.0", "@types/dompurify": "^2.3.3", "@types/jest": "^29.0.1", "@types/node": "^18.7.16", "@types/pdfjs-dist": "^2.10.378", "@types/react": "^17.0.14", "@types/react-native": "^0.64.12", "@types/react-router-dom": "5.1.2", "@types/styled-components": "5.1.26", "eslint-plugin-react-hooks": "^4.3.0", "jest-slow-test-reporter": "^1.0.0", "jest-transform-stub": "^2.0.0", "react-dom": "16.8.6", "react-native-elements": "3.4.2", "typescript": "^4.4.3"}, "dependencies": {"@ce/survey-wrapper": "^1.0.6-alpha.0", "@module-federation/bridge-react": "^0.15.0", "@react-native-community/async-storage": "^1.5.0", "@react-native-community/netinfo": "^2.0.10", "@reduxjs/toolkit": "^1.7.1", "@rive-app/react-canvas": "^4.18.4", "@sentry/react-native": "2.0.2", "@types/crypto-js": "^4.1.1", "@types/deep-equal": "^1.0.1", "@types/react-lottie": "^1.2.6", "@zpi/js-bridge": "^2.0.27", "@zpi/looknfeel": "^0.40.26-test.5", "@zpi/looknfeel-icons": "^0.1.31", "buffer": "^6.0.3", "crypto-js": "^4.1.1", "date-fns": "^2.24.0", "deep-equal": "^2.2.0", "dompurify": "^2.3.6", "history": "^5.3.0", "lodash": "^4.17.15", "lodash.memoize": "^4.1.2", "patch-package": "^6.4.7", "pdfjs-dist": "^3.9.179", "postinstall-postinstall": "^2.1.0", "prop-types": "^15.6.0", "query-string": "^6.11.0", "react": "16.8.3", "react-hook-form": "^7.34.2", "react-lottie": "^1.2.3", "react-native": "0.59.10", "react-native-animated-number": "^1.2.0", "react-native-autoheight-webview": "1.5.2", "react-native-device-info": "^8.4.8", "react-native-elements": "^3.4.2", "react-native-linear-gradient": "^2.5.6", "react-native-network-logger": "^1.15.0", "react-native-safe-area-context": "^3.2.0", "react-native-snap-carousel": "4.0.0-beta.6", "react-native-svg": "9.3.7", "react-native-swiper": "^1.5.14", "react-native-vector-icons": "^8.1.0", "react-native-web-webview": "^1.0.2", "react-native-webview": "5", "react-navigation": "^2.18.3", "react-redux": "^7.2.6", "react-router-dom": "5.1.2", "react-transition-group": "4.4.1", "redux": "^4.0.5", "redux-logger": "^3.0.6", "redux-thunk": "^2.3.0", "reselect": "^4.0.0", "sonner": "^2.0.1", "styled-components": "^5.3.11", "swiper": "^8.0.6", "tinycolor2": "^1.6.0", "victory-native": "^35.5.5"}, "peerDependencies": {"@sentry/react-native": "*", "react": "*", "react-native": "*", "react-native-async-storage/async-storage": "*", "react-native-elements": "*"}}
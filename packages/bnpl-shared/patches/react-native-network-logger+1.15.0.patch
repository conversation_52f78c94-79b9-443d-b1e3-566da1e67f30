diff --git a/node_modules/react-native-network-logger/lib/module/index.web.js b/node_modules/react-native-network-logger/lib/module/index.web.js
index 675e142..f631c66 100644
--- a/node_modules/react-native-network-logger/lib/module/index.web.js
+++ b/node_modules/react-native-network-logger/lib/module/index.web.js
@@ -5,6 +5,6 @@ export const startNetworkLogging = options => {
 export const getRequests = () => [];
 export const clearRequests = () => {};
 export { getBackHandler } from './backHandler';
-export { ThemeName } from './theme';
+export * from './theme';
 export default (() => null);
 //# sourceMappingURL=index.web.js.map
\ No newline at end of file
diff --git a/node_modules/react-native-network-logger/src/index.web.tsx b/node_modules/react-native-network-logger/src/index.web.tsx
index af04354..ba8e926 100644
--- a/node_modules/react-native-network-logger/src/index.web.tsx
+++ b/node_modules/react-native-network-logger/src/index.web.tsx
@@ -11,6 +11,6 @@ export const clearRequests = () => {};
 
 export { getBackHandler } from './backHandler';
 
-export { ThemeName } from './theme';
+export * from './theme';
 
 export default () => null;

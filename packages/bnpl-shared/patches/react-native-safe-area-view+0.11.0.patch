diff --git a/node_modules/react-native-safe-area-view/index.js b/node_modules/react-native-safe-area-view/index.js
index ee91466..8453448 100644
--- a/node_modules/react-native-safe-area-view/index.js
+++ b/node_modules/react-native-safe-area-view/index.js
@@ -36,6 +36,26 @@ const isIPhoneX = (() => {
   );
 })();
 
+// Currently implemented to support ZaloPay FS app, which never has landscape mode
+// at the time of writing.
+const isIPhoneNotchLargerThanX = (() => {
+  if (Platform.OS !== 'ios') {
+    return false;
+  }
+
+  // iPhone 11.
+  if (D_HEIGHT === 896 && D_WIDTH === 414) {
+    return true;
+  }
+
+  // iPhone 12.
+  if (D_HEIGHT === 844 && D_WIDTH === 390) {
+    return true;
+  }
+
+  return false;
+})();
+
 const isIPad = (() => {
   if (Platform.OS !== 'ios' || isIPhoneX) return false;
 
@@ -80,7 +100,11 @@ const statusBarHeight = isLandscape => {
     return 20;
   }
 
-  return isLandscape ? 0 : 20;
+  if (isIPhoneNotchLargerThanX) {
+    return isLandscape ? 0 : 44;
+  }
+
+  return isLandscape ? 0 : 32;
 };
 
 const doubleFromPercentString = percent => {

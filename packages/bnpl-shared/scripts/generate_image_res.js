/* istanbul ignore file */
const fs = require('fs');

const imageFileNames = () => {
  const array = fs
    .readdirSync('src/res/images/local')
    .filter(file => {
      return file.endsWith('.png') || file.endsWith('.jpg') || file.endsWith('.svg');
    }).map(file => {
        if (file.endsWith('.svg')) {
            return {key: file.replace('.svg', ''), url: file};
        }
        return {key: file.replace('@2x.png', '').replace('@3x.png', '').replace('.png', ''), url: file};
    })

  return Array.from(new Set(array));
};

const generate_image_res = () => {
  let properties = imageFileNames()
    .map(item => {
      return `${item.key}: require('./${item.url}'),`;
    })
    .join('\n  ');

  const string = `//DO NOT EDIT
//this file should not be edited manually, instead run script sync_image_res to update new image in res/images folder
export const local_images: any = {
  ${properties}
};
`;

  fs.writeFileSync('src/res/images/local/index.ts', string, 'utf8');
};

generate_image_res();

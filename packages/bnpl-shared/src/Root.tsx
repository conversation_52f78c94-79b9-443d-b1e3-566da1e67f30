import React, { FC, useEffect, useLayoutEffect } from 'react';
import { Provider } from 'react-redux';
import { store } from './redux/store';
import * as RNElements from 'react-native-elements';
import { setEnvironment, setModuleId } from './shared/environment';
import { Platform, UIManager } from 'react-native';
import { HeightProvider } from './utils/HeightProvider';
import { App } from 'bnpl-shared/src/app/App';
import { InfoDialog, infoDialogRef } from 'bnpl-shared/src/shared/InfoDialog';
import RejectModal from 'bnpl-shared/src/components/RejectModal/RejectModal';
import { rejectModalRef } from 'bnpl-shared/src/components/RejectModal';
import InfoModal from 'bnpl-shared/src/components/InfoModal';
import { alertModalRef, infoModalRef, InfoModalService, loadingModalRef } from 'bnpl-shared/src/services';
import { AlertModal } from 'bnpl-shared/src/components/InfoModal/AlertModal';
import { LoadingIndicator } from 'bnpl-shared/src/components/InfoModal/LoadingIndicator';
import { invalidateRenewState } from 'bnpl-shared/src/redux/featureRenewOverDraftReducer';
import { DevTool } from 'bnpl-shared/src/components/DevTool';
import { useSentry } from 'bnpl-shared/src/shared/sentry/useSentry';
import { Toaster } from 'sonner';
import { setAppInfo } from './redux/appInfo';
import { getAppInfo } from './shared/ZaloPayModules';
import { createPortal } from 'react-dom';

type Props = {
  appid: number;
  environment: number;
  /**
   * @ios
   */
  moduleId?: number;
  utm_campaign?: string;
  utm_source?: string;
  utm_toast?: string;
  partner?: string;
};

if (Platform.OS === 'android') {
  if (UIManager.setLayoutAnimationEnabledExperimental) {
    UIManager.setLayoutAnimationEnabledExperimental(true);
  }
}

export const Root: FC<Props> = props => {
  const { initSentry } = useSentry();
  useLayoutEffect(() => {
    setEnvironment(props.environment);
    (async () => {
      await initSentry(props.environment);
    })();
  }, [props.environment]);

  useEffect(() => {
    setModuleId(props.moduleId);
  }, [props.moduleId]);
  RNElements; // TODO: This is a temporary hack to fix the no image bug on iOS.

  useEffect(() => {
    InfoModalService.init();
    (async () => {
      const appInfo = await getAppInfo();
      store.dispatch(setAppInfo(appInfo));
    })();
    return () => {
      store.dispatch(invalidateRenewState());
    };
  }, []);

  return (
    <HeightProvider>
      <Provider store={store}>
        <App {...props} />
        <DevTool env={props.environment} />
        {Platform.OS === 'web' && <InfoDialog ref={infoDialogRef} />}
        <RejectModal ref={rejectModalRef} />
        {Platform.OS !== 'web' && <InfoModal ref={infoModalRef} />}
        {Platform.OS !== 'web' && <AlertModal ref={alertModalRef} />}
        {Platform.OS !== 'web' && <LoadingIndicator ref={loadingModalRef} />}
        {createPortal(<Toaster
          position="top-center"
          richColors
          visibleToasts={1}
          duration={3000}
        />, document.body)}
      </Provider>
    </HeightProvider>
  );
};

//#region

//#endregion

import React from 'react';

export const Row = ({ children, 'data-testid': dataTestId }: LabelProps) => {
  return (
    <div style={{ flexDirection: 'row' }} data-testid={dataTestId}>
      {children}
    </div>
  );
};

export const Col = ({ children, 'data-testid': dataTestId }: LabelProps) => {
  return (
    <div style={{ flexDirection: 'column' }} data-testid={dataTestId}>
      {children}
    </div>
  );
};

export const Input = (props: InputProps) => {
  return (
    <input
      {...props}
      onChange={(event: any) => {
        props.onChange(event.target.value);
      }}
      value={props.value}
      ref={props.inputRef}
      data-testid={props['data-testid']}
    />
  );
};

export const BottomSheet = ({ children, open }: BottomSheetProps) => {
  return open ? <div>{children}</div> : null;
};

export const PopupContainer = ({ children, open }: PopupContainerProps) => {
  return open ? <div>{children}</div> : null;
};

export const Button = ({ children, onClick, 'data-testid': dataTestId }: ButtonProps) => {
  return (
    <button data-testid={dataTestId} onClick={onClick}>
      {children}
    </button>
  );
};

export const Label = ({ children, 'data-testid': dataTestId, onClick }: LabelProps) => {
  return (
    <p onClick={onClick} data-testid={dataTestId}>
      {children}
    </p>
  );
};

export const Heading = ({ children, 'data-testid': dataTestId }: LabelProps) => {
  return <h1 data-testid={dataTestId}>{children}</h1>;
};

export const Paragraph = ({ children, 'data-testid': dataTestId }: LabelProps) => {
  return <p data-testid={dataTestId}>{children}</p>;
};

export const Image = ({ src, 'data-testid': dataTestId }: ImageProps) => {
  return <img alt="" data-testid={dataTestId} src={src} />;
};

export const Searchbar = (props: SearchbarProps) => {
  return (
    <div>
      <Input {...props} />
      <div onClick={props.onClearClick} data-testid="clear-search">
        X
      </div>
    </div>
  );
};

export const StateView = ({ title, subTitle, ctaText, onCTAClick }: StateViewProps) => {
  return (
    <>
      <div>{title}</div>
      <div>{subTitle}</div>
      <div onClick={onCTAClick}>{ctaText}</div>
    </>
  );
};

export const Inform = ({ children, 'data-testid': dataTestId }: LabelProps) => {
  return (
    <div style={{ flexDirection: 'row' }} data-testid={dataTestId}>
      {children}
    </div>
  );
};

export const Checkbox = ({ children, checked = false, value, 'data-testid': dataTestId }: CheckboxProps) => {
  return (
    <div data-testid={dataTestId}>
      <input type="checkbox" value={value} checked={checked} />
      {children}
    </div>
  );
};

export const Skeleton = () => {
  return <div className="Skeleton" />;
};

export const FullScreenLoading = () => {
  return <div className="FullScreenLoading" />;
};

export const snackBar = {
  open: jest.fn(),
};

export const Chip = ({ 'data-testid': dataTestId, key, id, title, size }: ChipProps) => (
  <div id={id} key={key} data-testid={dataTestId} className={`chip ${size}`}>
    {title}
  </div>
);
export const ChipsSelect = ({ 'data-testid': dataTestId, children, className, value = [] }: ChipsSelectProps) => (
  <>
    <div data-testid={dataTestId} className={className}>
      {children}
    </div>
    {value?.length ? <div data-testid={`${dataTestId}_value`}> value[0]</div> : null}
  </>
);

export const Tooltip = ({ 'data-testid': dataTestId, id, open, children, content }: TooltipProps) => (
  <div id={id} data-testid={id || dataTestId} className={`tooltip ${open ? 'open' : 'hide'}`}>
    <div className="tooltip-children">{children}</div>
    {open ? <div className="tooltip-content">{content}</div> : <></>}
  </div>
);

export class Tabs extends React.Component {
  onChange = () => {};

  constructor(props: any) {
    super(props);
    // eslint-disable-next-line react/prop-types
    this.onChange = props.onChange;
  }

  Item = ({ id, name }: { id: string; name: string }) => (
    <div data-testid={`tab-item-${id}`} onClick={this.onChange}>
      {name}
    </div>
  );

  render() {
    return <></>;
  }
}

// Type
export type ImageProps = {
  'data-testid'?: string;
  src: string;
};

export type BottomSheetProps = {
  children: React.ReactNode;
  open: boolean;
};

export type PopupContainerProps = {
  children: React.ReactNode;
  open: boolean;
};

export type ButtonProps = {
  'data-testid'?: string;
  children: React.ReactNode;
  onClick?: React.MouseEventHandler<HTMLElement>;
};

export type LabelProps = {
  'data-testid'?: string;
  children: React.ReactNode;
  onClick?: React.MouseEventHandler<HTMLElement>;
};

export type InputProps = {
  'data-testid'?: string;
  children: React.ReactNode;
  onChange: (value: string, amount?: string) => void;
  inputRef?: React.RefObject<HTMLInputElement>;
  disabled?: boolean;
  value: string;
  placeholder?: string;
};

export type StateViewProps = {
  title: string;
  subTitle: string;
  ctaText: string;
  onCTAClick: React.MouseEventHandler<HTMLElement>;
};

export type CheckboxProps = {
  children: React.ReactNode;
  value: string;
  'data-testid'?: string;
  checked?: boolean;
};

export type SearchbarProps = InputProps & { onClearClick?: () => void };

export type ChipProps = {
  'data-testid'?: string;
  ripple?: boolean;
  key?: string;
  id?: string;
  title: string;
  size: 'xs' | 'sm';
};
export type ChipsSelectProps = {
  'data-testid'?: string;
  children: React.ReactNode;
  className: string;
  value?: string[];
};

export type TooltipProps = {
  'data-testid'?: string;
  id?: string;
  open?: boolean;
  children: React.ReactNode;
  content: string;
  space?: number;
  placement?: string;
  width: string;
};

export type TabsProps = {
  'data-testid'?: string;
  onChange: any;
  type: string;
  activeId: string;
  children: React.ReactNode;
};

import { getPayLaterDomain } from 'bnpl-shared/src/api/withBaseUrl';
import { Environment, environment } from 'bnpl-shared/src/shared/environment';

export interface UrlPathBuilder {
  build(): string;
  getDomain(env: Environment): string;
}

export class AppUrlPathBuilder implements UrlPathBuilder {
  version: string;
  partner: string;
  path: string;

  constructor(path: string, options?: { version?: string; partner?: string }) {
    this.path = path;
    this.partner = options?.partner || '';
    this.version = options?.version || 'v1';
  }

  getDomain(env: Environment): string {
    switch (env) {
      case Environment.STAGING:
        return 'https://stgbnpl-gateway.zalopay.vn';
      case Environment.DEV_SANDBOX:
      case Environment.QC_SANDBOX:
      case Environment.MC_SANDBOX:
        return 'https://qcbnpl-gateway.zalopay.vn';
      case Environment.PRODUCTION:
        return 'https://bnpl-gateway.zalopay.vn';
      default:
        return 'https://bnpl-gateway.zalopay.vn';
    }
  }

  build() {
    const versionPath = this.version ? `/${this.version}` : '';
    const partnerPath = this.partner ? `/${this.partner}` : '';
    return `${getPayLaterDomain(environment)}/api/app${versionPath}${partnerPath}${this.path}`;
  }
}

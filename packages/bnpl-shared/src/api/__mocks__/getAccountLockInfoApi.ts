import { TestSampleBuilder } from 'bnpl-shared/src/shared/jest/TestSampleBuilder';
import { LockAccountUseCase } from 'bnpl-shared/src/types';

export const getAccountLockInfoApiBuilder = new TestSampleBuilder({
  is_lock: true,
  locks: [
    {
      client_code: 'test_client',
      use_case_code: LockAccountUseCase.FS_ADMIN,
      reason: 'test_reason',
      updated_by: 'test_user',
    },
    {
      client_code: 'test_client',
      use_case_code: LockAccountUseCase.PARTNER_FRAUD_DETECTION,
      reason: 'test_reason',
      updated_by: 'test_user',
    },
    {
      client_code: 'test_client',
      use_case_code: LockAccountUseCase.PARTNER_DPD,
      reason: 'test_reason',
      updated_by: 'test_user',
    },
    {
      client_code: 'test_client',
      use_case_code: LockAccountUseCase.PARTNER_LOCK,
      reason: 'test_reason',
      updated_by: 'test_user',
    },
  ],
});

getAccountLockInfoApiBuilder.addFeature('duplicate_use_case', {
  is_lock: true,
  locks: [
    {
      client_code: 'test_client',
      use_case_code: LockAccountUseCase.FS_ADMIN,
      reason: 'test_reason',
      updated_by: 'test_user',
    },
    {
      client_code: 'test_client',
      use_case_code: LockAccountUseCase.UM,
      reason: 'test_reason',
      updated_by: 'test_user',
    },
    {
      client_code: 'test_client',
      use_case_code: LockAccountUseCase.PARTNER_LOCK,
      reason: 'test_reason',
      updated_by: 'test_user',
    },
  ],
});

getAccountLockInfoApiBuilder.addFeature('unlock_account', {
  is_lock: true,
  locks: [
    {
      client_code: 'test_client',
      use_case_code: LockAccountUseCase.ACCOUNT,
      reason: 'test_reason',
      updated_by: 'test_user',
    },
  ],
});

getAccountLockInfoApiBuilder.addFeature('unlocked', {
  is_lock: false,
  locks: [],
});

export const getAccountLockInfoApi = jest.fn().mockResolvedValue(getAccountLockInfoApiBuilder.build());

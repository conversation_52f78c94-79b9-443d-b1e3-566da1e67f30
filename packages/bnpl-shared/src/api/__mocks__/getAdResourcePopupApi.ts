import { TestSampleBuilder } from 'bnpl-shared/src/shared/jest/TestSampleBuilder';

export const getAdResourcePopupApiBuilder = new TestSampleBuilder({
  infos: [
    {
      data: {
        popupContentId: 17,
        title: 'Create template 1',
        description: 'Create template 1',
        background: 'https://s3dev.zalopay.com.vn/zppmerchantqc/banner/<EMAIL>',
        mainButton: {
          name: 'Nhận lì xì',
          zpiActionLink: 'https://amazon.com',
          zpaActionLink: 'zalopay://launch/app/1550',
          zpiActionType: 'OPEN_WEB',
          zpaActionType: 'OPEN_APP',
        },
        subButton: {
          name: '<PERSON><PERSON> xì nhận lộc',
          zpiActionLink: 'https://ant.design/',
          zpaActionLink: 'zalopay://launch/app/1550',
          zpiActionType: 'OPEN_WEB',
          zpaActionType: 'OPEN_APP',
        },
        hasCloseButton: false,
      },
      template: 'TEMPLATE1',
    },
  ],
});

getAdResourcePopupApiBuilder.addFeature('empty', {
  infos: [],
});

export const getAdResourcePopupApi = jest.fn().mockResolvedValue(getAdResourcePopupApiBuilder.build());

import { TestSampleBuilder } from 'bnpl-shared/src/shared/jest/TestSampleBuilder';
import { AdBannerTemplate } from 'bnpl-shared/src/types';

export const getAdResourceProductPageApiBuilder = new TestSampleBuilder({
  data: [
    {
      id: 1,
      banner_url: 'https://www.google.com',
      redirect_url: 'https://www.google.com',
      title: 'title',
    },
    {
      id: 2,
      banner_url: 'https://www.google.com',
      redirect_url: 'https://www.google.com',
      title: 'title',
    },
  ],
  template_info: {
    width: 100,
    height: 50,
  },
  template: AdBannerTemplate.PRODUCT_PAGE_MULTI_IMAGE_SLIDER,
});

getAdResourceProductPageApiBuilder.addFeature('empty', {
  data: [],
  template_info: {},
  template: '',
});

getAdResourceProductPageApiBuilder.addFeature('full_image_banner', {
  data: [
    {
      id: 1,
      banner_url: 'https://www.google.com',
      redirect_url: 'https://www.google.com',
      title: 'title',
    },
    {
      id: 2,
      banner_url: 'https://www.google.com',
      redirect_url: 'https://www.google.com',
      title: 'title',
    },
  ],
  template: AdBannerTemplate.PRODUCT_PAGE_FULL_IMAGE_SLIDER_41,
});

export const getAdResourceProductPageApi = jest.fn().mockResolvedValue(getAdResourceProductPageApiBuilder.build());

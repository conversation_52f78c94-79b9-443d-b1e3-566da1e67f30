import { TestSampleBuilder } from 'bnpl-shared/src/shared/jest/TestSampleBuilder';

export const getBindingContractApiBuilder = new TestSampleBuilder({
  contract_template: '<div>This is the contract</div>',
  signed_contract_url: 'https://google.com',
});

getBindingContractApiBuilder.addFeature('empty_contract_url', { signed_contract_url: '' });

export const getBindingContractApi = jest.fn().mockResolvedValue(getBindingContractApiBuilder.build());

import { TestSampleBuilder } from 'bnpl-shared/src/shared/jest/TestSampleBuilder';

export const getBindingInfoApiBuilder = new TestSampleBuilder({
  basic_profile: { full_name: '<PERSON><PERSON><PERSON>', phone_number: 'asdf', id_number: 'asdf' },
  detail_profile: {
    birthday: '01/01/1990',
    id_issued_date: '01/01/2020',
    id_issued_location: 'asdf',
    permanent_address: 'asdf',
    gender: 'MALE',
  },
  binding_step: {},
  request_id: '011231241231',
});

getBindingInfoApiBuilder.addFeature('withBindingStepBod1Success', {
  binding_step: {
    current_step: {
      int_value: 0,
    },
  },
});
getBindingInfoApiBuilder.addFeature('withEmptyBasicProfile', {
  basic_profile: { full_name: '', phone_number: '012312312' },
});
getBindingInfoApiBuilder.addFeature('noBirthday', {
  detail_profile: {
    birthday: null,
  },
});

export const getBindingInfoApi = jest.fn().mockResolvedValue(getBindingInfoApiBuilder.build());

const { isBasicProfileEmpty: isBasicProfileEmptyOriginal } = jest.requireActual('../getBindingInfoApi');
export const isBasicProfileEmpty = isBasicProfileEmptyOriginal;

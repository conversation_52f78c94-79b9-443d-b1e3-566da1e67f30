import { TestSampleBuilder } from 'bnpl-shared/src/shared/jest/TestSampleBuilder';
import { formatCurrency } from 'bnpl-shared/src/shared/utils/formatCurrency';

export const getDebtObligationApiBuilder = (minPay: number = 10000, maxPay: number = 100000) =>
  new TestSampleBuilder({
    zalopay_id: 123456789,
    partner_code: 'CIMB',
    repayment_options: [
      {
        selection_id: 'max_repayment',
        type: 'fixed',
        metadata: {
          repay_value: maxPay.toString(),
          display_amount: formatCurrency(maxPay),
          title: 'Tổng dư nợ',
        },
      },
      {
        selection_id: 'min_repayment',
        type: 'fixed',
        metadata: {
          repay_value: minPay.toString(),
          display_amount: formatCurrency(minPay),
          title: 'Dư nợ tối thiểu',
        },
      },
      {
        selection_id: 'input_amount',
        type: 'input',
        metadata: {
          message: 'Nhập số tiền',
        },
      },
    ],
    service_fee: 200000,
  });

export const getDebtObligationApi = jest.fn().mockResolvedValue(getDebtObligationApiBuilder().build());

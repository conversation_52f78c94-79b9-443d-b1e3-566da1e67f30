import { TestSampleBuilder } from 'bnpl-shared/src/shared/jest/TestSampleBuilder';
import { TransactionType } from 'bnpl-shared/src/types';

export const getFeeDetailApiBuilder = new TestSampleBuilder({
  id: 'test_fee_late_id',
  type: TransactionType.FEE_LATE,
  amount: 200000,
  status: 'SUCCEEDED',
  description: 'Phí dịch vụ phát sinh khi có giao dịch phát sinh trong kỳ hoặc còn dư nợ ở kỳ trước đó.',
  updated_at: '2023-08-06T12:25:07+07:00',
});

getFeeDetailApiBuilder.addFeature('fee_service', {
  id: 'test_fee_service_id',
  type: TransactionType.FEE_SERVICE,
  amount: 200000,
  status: 'SUCCEEDED',
  description: '<PERSON>í sử dụng phát sinh khi bạn không thanh toán hết dư nợ cuối kỳ của sao kê.',
  updated_at: '2023-08-06T12:25:07+07:00',
});

export const getFeeDetailApi = getFeeDetailApiBuilder.build();

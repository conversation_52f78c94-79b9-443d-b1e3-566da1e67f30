import { TestSampleBuilder } from 'bnpl-shared/src/shared/jest/TestSampleBuilder';

export const getHistoryPaymentApiBuilder = new TestSampleBuilder({
  monthly_fee: 0,
  late_balance_fee: 30000,
  transaction_groups: [
    {
      date: '2023-01-30',
      transactions: [
        {
          transaction_date: '2023-01-30',
          bank_trans_id: '20230130999001189519',
          transaction_amount: 166987,
          transaction_type: 'REPAYMENT',
          transaction_remark: 'Thanh toán dư nợ',
        },
        {
          transaction_date: '2023-01-30',
          bank_trans_id: '20230130999001189514',
          transaction_amount: 33013,
          transaction_type: 'REPAYMENT',
          transaction_remark: 'Thanh toán dư nợ',
        },
      ],
    },
    {
      date: '2023-01-28',
      transactions: [
        {
          transaction_date: '2023-01-28',
          bank_trans_id: '20230128999001148871',
          transaction_amount: 10000,
          transaction_type: 'REPAYMENT',
          transaction_remark: '<PERSON>h toán dư nợ',
        },
      ],
    },
  ],
});

export const getHistoryPaymentApi = jest.fn().mockResolvedValue(getHistoryPaymentApiBuilder.build());

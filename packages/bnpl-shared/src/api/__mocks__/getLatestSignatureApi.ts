import { TestSampleBuilder } from 'bnpl-shared/src/shared/jest/TestSampleBuilder';

export const getLatestSignatureApiBuilder = new TestSampleBuilder({
  has_valid_signature: true,
  signature_zpi_url: 'image_url',
  signature_zpa_url: 'image_url',
});

getLatestSignatureApiBuilder.addFeature('invalid_signature', {
  has_valid_signature: false,
  signature_zpi_url: '',
  signature_zpa_url: '',
});

export const getLatestSignatureApi = jest.fn();

import { BindingStatus, RiskAction, UserIdType } from 'bnpl-shared/src/types';
import { TestSampleBuilder } from 'bnpl-shared/src/shared/jest/TestSampleBuilder';

export const getPermissionApiBuilder = new TestSampleBuilder({
  is_fraud: false,
  is_valid: true,
  binding_status: BindingStatus.new,
  is_whitelisted: true,
  profile_level: 3,
  kyc_level: 3,
  id_type: UserIdType.CitizenID,
});

getPermissionApiBuilder.addFeature('fraud', { is_fraud: true });
getPermissionApiBuilder.addFeature('bindingNew', { binding_status: BindingStatus.new });
getPermissionApiBuilder.addFeature('bindingOpen', { binding_status: BindingStatus.open });
getPermissionApiBuilder.addFeature('bindingProcessing', { binding_status: BindingStatus.processing });
getPermissionApiBuilder.addFeature('bindingClose', { binding_status: BindingStatus.close });
getPermissionApiBuilder.addFeature('bindingRejectAllowResubmit', {
  binding_status: BindingStatus.reject,
  is_allow_resubmit: true,
});
getPermissionApiBuilder.addFeature('bindingReject', {
  binding_status: BindingStatus.reject,
  is_allow_resubmit: false,
});
getPermissionApiBuilder.addFeature('passport', { id_type: UserIdType.Passport });
getPermissionApiBuilder.addFeature('passport_open', {
  id_type: UserIdType.Passport,
  binding_status: BindingStatus.open,
});
getPermissionApiBuilder.addFeature('passport_locked', {
  id_type: UserIdType.Passport,
  binding_status: BindingStatus.locked,
});
getPermissionApiBuilder.addFeature('officeCert', { id_type: UserIdType.OfficerCert });
getPermissionApiBuilder.addFeature('kycLevel1', { kyc_level: 1 });
getPermissionApiBuilder.addFeature('kycLevel2', { kyc_level: 2 });
getPermissionApiBuilder.addFeature('risk_info_face_challenge', {
  binding_status: BindingStatus.open,
  risk_info: {
    action: RiskAction.FACE_CHALLENGE,
    action_url: 'face_challenge_url',
    is_risk: true,
  },
});
getPermissionApiBuilder.addFeature('risk_info_update_kyc', {
  risk_info: {
    action: RiskAction.UPDATE_KYC,
    action_url: 'update_kyc_url',
    is_risk: true,
  },
});
getPermissionApiBuilder.addFeature('risk_info_contact_cs', {
  risk_info: {
    action: RiskAction.CONTACT_CS,
    action_url: 'contact_cs_url',
    is_risk: true,
  },
});
getPermissionApiBuilder.addFeature('risk_info_reject', {
  risk_info: {
    action: RiskAction.REJECT,
    action_url: '',
    is_risk: true,
  },
});

export const getPermissionApi = jest.fn().mockResolvedValue(getPermissionApiBuilder.build());

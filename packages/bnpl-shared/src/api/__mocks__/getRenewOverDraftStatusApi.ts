import { TestSampleBuilder } from 'bnpl-shared/src/shared/jest/TestSampleBuilder';

export const getRenewOverDraftStatusApiBuilder = new TestSampleBuilder({
  is_renewable: true,
  renew_status: 'init',
  appendix_id: 12345,
  partner_request_id: '1234567',
  valid_at: '1234232',
  account_expired_date: '*********',
  is_expired: false,
});

getRenewOverDraftStatusApiBuilder.addFeature('not_renewable', { is_renewable: false });

export const getRenewOverDraftStatusApi = jest.fn().mockResolvedValue(getRenewOverDraftStatusApiBuilder.build());

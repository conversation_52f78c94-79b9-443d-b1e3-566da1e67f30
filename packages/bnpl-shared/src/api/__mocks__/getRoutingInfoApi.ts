import { TestSampleBuilder } from 'bnpl-shared/src/shared/jest/TestSampleBuilder';
import { FakePartnerCIMB, FakePartnerLotte } from 'bnpl-shared/src/jest/fakeData';

export const getRoutingInfoApiBuilder = new TestSampleBuilder([FakePartnerCIMB, FakePartnerLotte]);

getRoutingInfoApiBuilder.addFeature('2_submit_to_partner', {
  data: [
    { ...FakePartnerCIMB, has_submit_to_partner: true },
    { ...FakePartnerLotte, has_submit_to_partner: true },
  ],
});

getRoutingInfoApiBuilder.addFeature('1_submit_to_partner', {
  data: [
    { ...FakePartnerCIMB, has_submit_to_partner: true },
    { ...FakePartnerLotte, has_submit_to_partner: false },
  ],
});

getRoutingInfoApiBuilder.addFeature('not_eligible', {
  data: [
    { ...FakePartnerCIMB, has_submit_to_partner: false, is_eligible_for_onboarding: false },
    { ...FakePartnerLotte, has_submit_to_partner: false, is_eligible_for_onboarding: false },
  ],
});

getRoutingInfoApiBuilder.addFeature('2_eligible', {
  data: [
    { ...FakePartnerCIMB, has_submit_to_partner: false, is_eligible_for_onboarding: true },
    { ...FakePartnerLotte, has_submit_to_partner: false, is_eligible_for_onboarding: true },
  ],
});

getRoutingInfoApiBuilder.addFeature('1_eligible', {
  data: [
    { ...FakePartnerCIMB, has_submit_to_partner: false, is_eligible_for_onboarding: false },
    { ...FakePartnerLotte, has_submit_to_partner: false, is_eligible_for_onboarding: true },
  ],
});

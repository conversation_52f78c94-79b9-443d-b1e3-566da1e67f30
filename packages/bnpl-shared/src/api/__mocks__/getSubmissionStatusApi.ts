import { TestSampleBuilder } from 'bnpl-shared/src/shared/jest/TestSampleBuilder';
import { OnboardingStatus } from 'bnpl-shared/src/types';

export const getSubmissionStatusApiBuilder = new TestSampleBuilder({
  onboarding_status: OnboardingStatus.APPROVE,
  contract_template: '<div>This is the contract</div>',
  signed_contract_url: 'https://google.com',
});

getSubmissionStatusApiBuilder.addFeature('manualApprove', { onboarding_status: OnboardingStatus.MANUAL_APPROVE });
getSubmissionStatusApiBuilder.addFeature('empty_contract_url', { signed_contract_url: '' });

export const getSubmissionStatusApi = jest.fn().mockResolvedValue(getSubmissionStatusApiBuilder.build());

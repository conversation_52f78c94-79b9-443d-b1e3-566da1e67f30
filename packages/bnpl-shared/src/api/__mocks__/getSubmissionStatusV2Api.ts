import { TestSampleBuilder } from 'bnpl-shared/src/shared/jest/TestSampleBuilder';
import { OnboardingStatus } from 'bnpl-shared/src/types';

export const getSubmissionStatusV2ApiBuilder = new TestSampleBuilder({
  onboarding_status: OnboardingStatus.APPROVE,
});

getSubmissionStatusV2ApiBuilder.addFeature('manualApprove', { onboarding_status: OnboardingStatus.MANUAL_APPROVE });

export const getSubmissionStatusV2Api = jest.fn().mockResolvedValue(getSubmissionStatusV2ApiBuilder.build());

import { TestSampleBuilder } from 'bnpl-shared/src/shared/jest/TestSampleBuilder';
import { StatementSummaryType } from 'bnpl-shared/src/types';

const default_statement: StatementSummaryType = {
  approved_limit: 5000000,
  available_limit: 5000000,
  limit_expire_date: '2008-12-23',
  statement_date: '2008-12-23',
  repayment_grace_end_date: '2022-09-06',
  min_pay_amount: 5000000,
  total_outstanding_amount: 5000000,
  total_outstanding_debt_obligation_amount: 5000000,
  total_new_od_transaction_amount: 5000000,
  statement_status: 'STATEMENT_DEBT_AVAILABLE',
  days_to_maturity: 15,
  repaid_amount: 0,
  max_repayment_amount: 0,
  min_repayment_amount: 0,
  suggested_min_amount: 0,
};

export const getSummaryStatementApiBuilder = new TestSampleBuilder(default_statement);

getSummaryStatementApiBuilder.addFeature('statement_no_debt', {
  ...default_statement,
  statement_status: 'NO_SPEND',
  total_outstanding_amount: 0,
});
getSummaryStatementApiBuilder.addFeature('statement_already_paid', {
  ...default_statement,
  statement_status: 'NO_STATEMENT_DEBT',
});
getSummaryStatementApiBuilder.addFeature('before_due', default_statement);
getSummaryStatementApiBuilder.addFeature('due', {
  ...default_statement,
  total_outstanding_amount: 500000,
  repaid_amount: 10000,
  min_repayment_amount: 100000,
  max_repayment_amount: 300000,
  days_to_maturity: 0,
});
getSummaryStatementApiBuilder.addFeature('over_due', {
  ...default_statement,
  total_outstanding_amount: 500000,
  repaid_amount: 10000,
  min_repayment_amount: 100000,
  max_repayment_amount: 300000,
  days_to_maturity: -2,
});

getSummaryStatementApiBuilder.addFeature('suggest_min_pay', {
  ...default_statement,
  min_pay_amount: 0,
  suggested_min_amount: 15000,
});

export const getSummaryStatementApi = jest.fn().mockResolvedValue(getSummaryStatementApiBuilder.build('before_due'));

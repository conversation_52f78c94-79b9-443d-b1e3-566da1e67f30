import { TestSampleBuilder } from 'bnpl-shared/src/shared/jest/TestSampleBuilder';

export const getTransactionDetailApiBuilder = new TestSampleBuilder({
  id: 'test_trans_id',
  zp_trans_id: 'zp_test_id',
  type: 'PAYMENT',
  amount: 200000,
  trans_status: 'SUCCEEDED',
  description: 'Mua hàng',
  status_description: 'status_desc_payment',
  created_at: '2023-08-06T12:25:04+07:00',
  updated_at: '2023-08-06T12:25:07+07:00',
});

getTransactionDetailApiBuilder.addFeature('repayment', {
  id: 'test_trans_id',
  zp_trans_id: 'zp_test_id',
  type: 'REPAYMENT',
  amount: 200000,
  trans_status: 'SUCCEEDED',
  description: 'Thanh toán dư nợ Tài khoản trả sau',
  status_description: 'status_desc_repayment',
  stages: {
    '1': {
      status: 'SUCCEEDED',
      message: '<PERSON><PERSON><PERSON><PERSON> trừ tiền thành công',
    },
    '2': {
      status: 'SUCCEEDED',
      message: 'CIMB nhận tiền thành công',
    },
  },
  created_at: '2023-08-06T12:25:04+07:00',
  updated_at: '2023-08-06T12:25:07+07:00',
});

getTransactionDetailApiBuilder.addFeature('repayment_init', {
  id: 'test_trans_id',
  zp_trans_id: 'zp_test_id',
  type: 'REPAYMENT',
  amount: 200000,
  trans_status: 'INIT',
  description: 'Thanh toán dư nợ Tài khoản trả sau',
  status_description: 'status_desc_repayment',
  created_at: '2023-08-06T12:25:04+07:00',
  updated_at: '2023-08-06T12:25:07+07:00',
});

export const getTransactionDetailApi = getTransactionDetailApiBuilder.build();

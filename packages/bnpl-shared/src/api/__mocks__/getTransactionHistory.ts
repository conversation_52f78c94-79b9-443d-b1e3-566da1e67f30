import { TestSampleBuilder } from 'bnpl-shared/src/shared/jest/TestSampleBuilder';
import { _TransactionHistory } from 'bnpl-shared/src/jest/fakeData';

export const getTransactionHistoryApiBuilder = new TestSampleBuilder({
  code: 0,
  message: '',
});

getTransactionHistoryApiBuilder.addFeature('success', {
  code: 0,
  message: 'success message',
  data: _TransactionHistory,
});

getTransactionHistoryApiBuilder.addFeature('empty', {
  code: 0,
  message: 'success message',
  data: { next_cursor: null, transactions: [] },
});

getTransactionHistoryApiBuilder.addFeature('fail', {
  code: 1,
  message: 'fail message',
  data: null,
});

export const getTransactionHistoryApi = jest.fn();

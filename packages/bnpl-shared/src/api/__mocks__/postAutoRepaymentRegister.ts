import { TestSampleBuilder } from 'bnpl-shared/src/shared/jest/TestSampleBuilder';
import { RepayOptions } from 'bnpl-shared/src/types';

export const postAutoRepaymentRegisterApiBuilder = new TestSampleBuilder({
  status: 'active',
  repay_option: RepayOptions.MIN,
});

postAutoRepaymentRegisterApiBuilder.addFeature('full', { repay_option: RepayOptions.FULL });

export const postAutoRepaymentRegister = jest.fn();

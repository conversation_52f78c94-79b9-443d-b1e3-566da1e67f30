import { TestSampleBuilder } from 'bnpl-shared/src/shared/jest/TestSampleBuilder';

export const postBasicInfoApiBuilder = new TestSampleBuilder({
  permission: 'CAN_ONBOARD',
  request_id: '*********',
  reject_code: '',
  message: 'Bạn đủ điều kiện để đăng ký Tài Khoản Trả Sau',
});
postBasicInfoApiBuilder.addFeature('rejected', {
  permission: 'REJECTED',
  request_id: '*********',
  reject_code: 'ONBOARDING_PRODUCT_NOT_ALLOWED',
  message: 'Bạn chưa đủ điều kiện để đăng ký Tài Khoản Trả Sau',
  edge_case_info: {
    title: 'Rất tiếc',
    description: 'Bạn chưa đủ điều kiện để đăng ký Tài <PERSON>hoản Trả Sau',
    ctas: ['CALL_BANK', 'BACK_HOMEPAGE'],
  },
});

export const postBasicInfoApi = jest.fn().mockResolvedValue(postBasicInfoApiBuilder.build());

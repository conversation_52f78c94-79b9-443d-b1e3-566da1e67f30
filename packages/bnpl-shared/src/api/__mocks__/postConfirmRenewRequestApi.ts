import { TestSampleBuilder } from 'bnpl-shared/src/shared/jest/TestSampleBuilder';

export const postConfirmRenewRequestApiBuilder = new TestSampleBuilder({});

postConfirmRenewRequestApiBuilder.addFeature('edge_case', {
  code: 800,
  data: {
    title: '<PERSON><PERSON><PERSON> tiếc',
    description: 'Bạn chưa đủ điều kiện để đăng ký Tài Khoản Trả Sau',
    ctas: ['CALL_BANK', 'BACK_HOMEPAGE'],
  },
});

postConfirmRenewRequestApiBuilder.addFeature('reset_nfc', {
  code: 800,
  data: {
    title: 'Xác nhận đặt lại dữ liệu NFC',
    description: 'Bạn chưa đủ điều kiện để đăng ký Tài Khoản Trả Sau',
    ctas: ['RESET_NFC'],
  },
});

export const postConfirmRenewRequestApi = jest.fn();

import { PredefinedCTA, RenewStatusType } from 'bnpl-shared/src/types';
import { TestSampleBuilder } from 'bnpl-shared/src/shared/jest/TestSampleBuilder';

export const postCreateRenewRequestApiBuilder = new TestSampleBuilder({
  data: {
    appendix_id: 1234,
    renew_appendix_info: {
      full_name: '<PERSON><PERSON><PERSON>',
      birthday: '01/01/1990',
      id_number: '1234547353',
      id_issued_date: '01/01/2020',
    },
    renew_appendix_status: RenewStatusType.NOT_STARTED,
    notice: undefined,
  },
});

postCreateRenewRequestApiBuilder.addFeature('reset_nfc', {
  data: {
    appendix_id: 0,
    renew_appendix_info: {},
    renew_appendix_status: RenewStatusType.NOT_STARTED,
    notice: {
      title: 'reset nfc title',
      description: 'reset nfc desc',
      ctas: [PredefinedCTA.RESET_NFC],
    },
  },
});

export const postCreateRenewRequestApi = jest.fn().mockResolvedValue(postCreateRenewRequestApiBuilder.build());

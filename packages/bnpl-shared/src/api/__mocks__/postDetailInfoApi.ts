import { TestSampleBuilder } from 'bnpl-shared/src/shared/jest/TestSampleBuilder';
import { VerifyIssueCode } from 'bnpl-shared/src/types';
import { postVerifyProfileApiBuilder } from 'bnpl-shared/src/api/__mocks__/postVerifyProfile';

export const postDetailInfoApiBuilder = new TestSampleBuilder({});

postDetailInfoApiBuilder.addFeature('type3', {
  partner_account_exist: true,
});

postDetailInfoApiBuilder.addFeature('lending_rule_old_kyc', {
  partner_account_exist: false,
  issue_existed: true,
  issue_details: {
    code: VerifyIssueCode.OLD_KYC,
    name: 'OLD_KYC title',
    description: 'OLD_KYC description',
  },
});

postDetailInfoApiBuilder.addFeature('edge_case_info', {
  partner_account_exist: false,
  issue_existed: false,
  edge_case_info: {
    title: '<PERSON><PERSON><PERSON> tiế<PERSON>',
    description: '<PERSON>ạn chưa đủ điều kiện để đăng ký Tài Khoản Trả Sau',
    ctas: ['CALL_BANK', 'BACK_HOMEPAGE'],
  },
});

postDetailInfoApiBuilder.addFeature('lending_tone_accent_invalid', {
  partner_account_exist: false,
  issue_existed: true,
  issue_details: {
    code: VerifyIssueCode.TONE_ACCENT_INVALID,
    name: 'TONE_ACCENT_INVALID title',
    description: 'TONE_ACCENT_INVALID description',
  },
});

postDetailInfoApiBuilder.addFeature('not_liveness_and_zpa', {
  partner_account_exist: false,
  issue_existed: true,
  issue_details: {
    code: VerifyIssueCode.NOT_LIVENESS_AND_ZPA,
    name: 'NOT_LIVENESS_AND_ZPA title',
    description: 'NOT_LIVENESS_AND_ZPA description',
  },
});

postDetailInfoApiBuilder.addFeature('lack_of_nfc_info', {
  kyc_valid: true,
  issue_existed: true,
  issue_details: {
    code: VerifyIssueCode.LACK_OF_NFC_INFO,
    name: 'LACK_OF_NFC_INFO title',
    description: 'LACK_OF_NFC_INFO description',
  },
});

postDetailInfoApiBuilder.addFeature('invalid_nfc', {
  kyc_valid: true,
  issue_existed: true,
  issue_details: {
    code: VerifyIssueCode.INVALID_NFC,
    name: 'INVALID_NFC title',
    description: 'INVALID_NFC description',
  },
});

export const postDetailInfoApi = jest.fn().mockResolvedValue(postDetailInfoApiBuilder.build());

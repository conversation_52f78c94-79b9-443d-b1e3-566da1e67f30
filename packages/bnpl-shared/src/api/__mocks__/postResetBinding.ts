import { TestSampleBuilder } from 'bnpl-shared/src/shared/jest/TestSampleBuilder';
import { ServiceResponseSubcode } from 'bnpl-shared/src/types';

export const postResetBindingApiBuilder = new TestSampleBuilder({
  code: 0,
  message: '',
});
postResetBindingApiBuilder.addFeature('update_kyc', {
  code: ServiceResponseSubcode.INVALID_USER_PROFILE,
  message: '',
});
postResetBindingApiBuilder.addFeature('limit_update', {
  code: ServiceResponseSubcode.TO0_MANY_REQUESTS,
  message: '',
});

export const postResetBinding = jest.fn().mockResolvedValue(postResetBindingApiBuilder.build());

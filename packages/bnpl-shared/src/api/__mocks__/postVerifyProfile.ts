import { TestSampleBuilder } from 'bnpl-shared/src/shared/jest/TestSampleBuilder';
import { VerifyIssueCode } from 'bnpl-shared/src/types';

export const postVerifyProfileApiBuilder = new TestSampleBuilder({
  kyc_valid: true,
  issue_existed: false,
});

postVerifyProfileApiBuilder.addFeature(VerifyIssueCode.OLD_KYC, {
  kyc_valid: true,
  issue_existed: true,
  issue_details: {
    code: VerifyIssueCode.OLD_KYC,
    name: 'OLD_KYC',
    description: 'OLD_KYC',
  },
});
postVerifyProfileApiBuilder.addFeature(VerifyIssueCode.OLD_IC, {
  kyc_valid: true,
  issue_existed: true,
  issue_details: {
    code: VerifyIssueCode.OLD_IC,
    name: 'OLD_IC',
    description: 'OLD_IC',
  },
});
postVerifyProfileApiBuilder.addFeature(VerifyIssueCode.ID_USED, {
  kyc_valid: true,
  issue_existed: true,
  issue_details: {
    code: VerifyIssueCode.ID_USED,
    name: 'ID_USED',
    description: 'ID_USED',
  },
});
postVerifyProfileApiBuilder.addFeature(VerifyIssueCode.ID_EXPIRED, {
  kyc_valid: true,
  issue_existed: true,
  issue_details: {
    code: VerifyIssueCode.ID_EXPIRED,
    name: 'ID_EXPIRED',
    description: 'ID_EXPIRED',
  },
});
postVerifyProfileApiBuilder.addFeature(VerifyIssueCode.AGE_NOT_ALLOWED, {
  kyc_valid: true,
  issue_existed: true,
  issue_details: {
    code: VerifyIssueCode.AGE_NOT_ALLOWED,
    name: 'AGE_NOT_ALLOWED',
    description: 'AGE_NOT_ALLOWED',
  },
});
postVerifyProfileApiBuilder.addFeature(VerifyIssueCode.NAME_WITHOUT_ACCENT, {
  kyc_valid: true,
  issue_existed: true,
  issue_details: {
    code: VerifyIssueCode.NAME_WITHOUT_ACCENT,
    name: 'NAME_WITHOUT_ACCENT',
    description: 'NAME_WITHOUT_ACCENT',
  },
});
postVerifyProfileApiBuilder.addFeature(VerifyIssueCode.LACK_OF_EKYC_INFO, {
  kyc_valid: true,
  issue_existed: true,
  issue_details: {
    code: VerifyIssueCode.LACK_OF_EKYC_INFO,
    name: 'LACK_OF_EKYC_INFO',
    description: 'LACK_OF_EKYC_INFO',
  },
});
postVerifyProfileApiBuilder.addFeature(VerifyIssueCode.LACK_OF_IC_IMAGE, {
  kyc_valid: true,
  issue_existed: true,
  issue_details: {
    code: VerifyIssueCode.LACK_OF_IC_IMAGE,
    name: 'LACK_OF_IC_IMAGE',
    description: 'LACK_OF_IC_IMAGE',
  },
});

postVerifyProfileApiBuilder.addFeature('kyc_invalid', {
  kyc_valid: false,
  issue_existed: false,
});
postVerifyProfileApiBuilder.addFeature(VerifyIssueCode.INFO_USED, {
  kyc_valid: true,
  issue_existed: true,
  issue_details: {
    code: VerifyIssueCode.INFO_USED,
    name: 'INFO_USED',
    description: 'INFO_USED',
  },
});
postVerifyProfileApiBuilder.addFeature(VerifyIssueCode.NOT_LIVENESS_AND_ZPA, {
  kyc_valid: true,
  issue_existed: true,
  issue_details: {
    code: VerifyIssueCode.NOT_LIVENESS_AND_ZPA,
    name: 'NOT_LIVENESS_AND_ZPA',
    description: 'NOT_LIVENESS_AND_ZPA',
  },
});

postVerifyProfileApiBuilder.addFeature(VerifyIssueCode.WRONG_IMAGE_ORIENTATION, {
  kyc_valid: true,
  issue_existed: true,
  issue_details: {
    code: VerifyIssueCode.WRONG_IMAGE_ORIENTATION,
    name: 'WRONG_IMAGE_ORIENTATION',
    description: 'WRONG_IMAGE_ORIENTATION',
  },
});

postVerifyProfileApiBuilder.addFeature(VerifyIssueCode.INVALID_NFC, {
  kyc_valid: true,
  issue_existed: true,
  issue_details: {
    code: VerifyIssueCode.INVALID_NFC,
    name: 'INVALID_NFC title',
    description: 'INVALID_NFC',
  },
});

postVerifyProfileApiBuilder.addFeature(VerifyIssueCode.INVALID_NFC_VERIFY_SOURCE, {
  kyc_valid: true,
  issue_existed: true,
  issue_details: {
    code: VerifyIssueCode.INVALID_NFC_VERIFY_SOURCE,
    name: 'INVALID_NFC_VERIFY_SOURCE title',
    description: 'INVALID_NFC_VERIFY_SOURCE',
  },
});

export const postVerifyProfile = jest.fn().mockResolvedValue(postVerifyProfileApiBuilder.build());

import { buildFaqUrl } from 'bnpl-shared/src/api/buildFaqUrl';
import { Environment, setEnvironment } from 'bnpl-shared/src/shared/environment';

describe(buildFaqUrl.name, function () {
  it('buildFaqUrl with input environment', () => {
    expect(buildFaqUrl(Environment.DEV_SANDBOX)).toEqual('https://dev-support.zalopay.vn/faq');
    expect(buildFaqUrl(Environment.QC_SANDBOX)).toEqual('https://qc-support.zalopay.vn/faq');
    expect(buildFaqUrl(Environment.STAGING)).toEqual('https://stg-support.zalopay.vn/faq');
    expect(buildFaqUrl(Environment.PRODUCTION)).toEqual('https://support.zalopay.vn/faq');
  });
  it('buildFaqUrl with default environment', () => {
    setEnvironment(Environment.QC_SANDBOX);
    expect(buildFaqUrl()).toEqual('https://qc-support.zalopay.vn/faq');
  });
});

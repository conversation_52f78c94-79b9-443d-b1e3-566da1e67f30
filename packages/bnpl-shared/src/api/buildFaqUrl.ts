import { environment as defaultEnvironment, Environment } from 'bnpl-shared/src/shared/environment';

export type FaqRequest = {
  tag: string;
  issue_detail: string;
  issue_id: number;
  provider: string;
  more_info?: string;
};

export const buildFaqUrl = (environment: Environment = defaultEnvironment) => {
  let environmentPrefix = '';
  switch (environment) {
    case Environment.STAGING:
      environmentPrefix = 'stg-';
      break;
    case Environment.DEV_SANDBOX:
      environmentPrefix = 'dev-';
      break;
    case Environment.QC_SANDBOX:
      environmentPrefix = 'qc-';
      break;
    case Environment.PRODUCTION:
    default:
      environmentPrefix = '';
  }
  return `https://${environmentPrefix}support.zalopay.vn/faq`;
};

import { HttpRequestVerbs } from 'bnpl-shared/src/shared/api';
import RequestBuilder from 'bnpl-shared/src/api/requestBuilder';
import {
  zlpAuthorizeInterceptor,
  zlpResponseUnwrapInterceptor,
  maintenanceInterceptor,
  defaultHeaderInterceptor,
  sentryLoggingInterceptor,
} from 'bnpl-shared/src/api/interceptors';

export type SuppressError = {
  ignoreErrorCodes?: number[];
};

export type Options = {
  suppressError?: SuppressError;
  retryOptions?: RetryOptions;
};

export type RetryOptions = {
  retryCount: number;
  retryInterval: number;
};

const createZlpRequest = <ResponseType>(getUrl: () => string, method: HttpRequestVerbs, options?: Options) => {
  return new RequestBuilder<ResponseType>(getUrl(), method)
    .addRequestInterceptors([defaultHeaderInterceptor, zlpAuthorizeInterceptor])
    .addResponseInterceptors([
      sentryLoggingInterceptor(options?.suppressError),
      maintenanceInterceptor,
      zlpResponseUnwrapInterceptor,
    ]);
};

export default createZlpRequest;

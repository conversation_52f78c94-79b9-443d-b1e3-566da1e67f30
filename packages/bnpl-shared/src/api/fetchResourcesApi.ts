import { withBaseUrl } from './withBaseUrl';
import { HttpRequestVerbs } from 'bnpl-shared/src/shared/api';
import createZlpRequest from 'bnpl-shared/src/api/createZlpRequest';
import { ResourceResponse, ResourceType } from 'bnpl-shared/src/types';

export const fetchResourcesApi = (params: { request_id: string; resource_types: ResourceType[] }) =>
  createZlpRequest<ResourceResponse>(withBaseUrl('/users/resources'), HttpRequestVerbs.GET)
    .setQueryParams(params)
    .build();

import { UserKycInfo } from 'bnpl-shared/src/types';
import { buildZlpPublicBaseUrl } from 'bnpl-shared/src/api/withBaseUrl';
import { HttpRequestVerbs } from 'bnpl-shared/src/shared/api';
import { getZLPToken } from 'bnpl-shared/src/shared/ZaloPayModules';
import RequestBuilder from 'bnpl-shared/src/api/requestBuilder';

type ResponseData = {
  data: UserKycInfo;
};

export const fetchUserKycInfo = async () => {
  const zlp_token = await getZLPToken();
  return new RequestBuilder<ResponseData>(`${buildZlpPublicBaseUrl()}/v2/user/profile/kyc`, HttpRequestVerbs.GET)
    .setHeaders({
      Authorization: `Bearer ${zlp_token}`,
    })
    .build()
    .then(result => result.data);
};

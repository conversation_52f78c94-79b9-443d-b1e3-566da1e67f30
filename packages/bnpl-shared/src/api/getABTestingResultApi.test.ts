import { getABTestingResultApi } from 'bnpl-shared/src/api/getABTestingResultApi';
import { createSuccessMockResp, mockFetch } from 'bnpl-shared/src/jest/fakeData';

jest.mock('bnpl-shared/src/shared/ZaloPayModules', () => ({
  getZLPToken: jest.fn().mockReturnValue('1234'),
}));

describe(getABTestingResultApi.name, () => {
  it('execute as expected', async () => {
    mockFetch(
      createSuccessMockResp({
        code: 0,
        message: '',
        data: {
          group: 'Variation 1',
          metadata: {
            ab_info: {
              access: 'yes',
              experiment_id: 2423,
              group_name: 'abc',
            },
          },
        },
      }),
    );
    const result = await getABTestingResultApi('abdc');
    expect(result).toBeTruthy();
  });
});

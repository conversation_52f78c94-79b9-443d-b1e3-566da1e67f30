/* eslint-disable no-undef */
import RequestBuilder, { SuccessfulResponse } from 'bnpl-shared/src/api/requestBuilder';
import { buildZlpPublicBaseUrl } from 'bnpl-shared/src/api/withBaseUrl';
import { HttpRequestVerbs } from 'bnpl-shared/src/shared/api';
import { Platform } from 'react-native';
import { getZLPToken } from 'bnpl-shared/src/shared/ZaloPayModules';
import { ServiceResponseSubcode } from 'bnpl-shared/src/types';

type Data = {
  group: string;
  metadata: {
    ab_info: {
      access: string;
      experiment_id: string;
      group_name: string;
    };
  };
};

export const getABTestingResultApi = async (experimentKey: string) => {
  let headers = {};
  const host = `${buildZlpPublicBaseUrl()}/abpublic/api/group/${experimentKey}`;
  // const mockHost = `https://darling-mustang-moving.ngrok-free.app/abpublic/api/group/${experimentKey}`;
  if (Platform.OS !== 'web') {
    const zlp_token = await getZLPToken();
    headers = {
      Authorization: `Bearer ${zlp_token}`,
    };
  } else {
    headers = {
      'Content-Type': 'text/plain',
    };
  }
  return new RequestBuilder<string | undefined>(host, HttpRequestVerbs.GET)
    .setHeaders(headers)
    .addResponseInterceptors([
      async ({ response, RequestBuilder }: { response: Response; RequestBuilder: RequestBuilder<ResponseType> }) => {
        const rawResp = (await response.clone().json()) as SuccessfulResponse<Data>;
        if (response.ok && rawResp.code === ServiceResponseSubcode.SUCCESS) {
          const jsonHandler = async () => {
            return rawResp?.data?.group;
          };
          response.json = jsonHandler;
          response.clone = () => {
            return {
              ...response,
              json: jsonHandler,
            };
          };
        }
        return {
          response,
          RequestBuilder,
        };
      },
    ])
    .build();
};

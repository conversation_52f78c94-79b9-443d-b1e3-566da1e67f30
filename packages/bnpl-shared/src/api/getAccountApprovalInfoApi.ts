import { withBaseUrl } from 'bnpl-shared/src/api/withBaseUrl';
import createZlpRequest from 'bnpl-shared/src/api/createZlpRequest';
import { ApprovalAccountInfoResp } from 'bnpl-shared/src/types';
import { HttpRequestVerbs } from 'bnpl-shared/src/shared/api';

const REAL_HOST = withBaseUrl('/users/binding/approval', { version: 'v2' });
// const MOCK_HOST = () => 'https://darling-mustang-moving.ngrok-free.app/users/binding/approval';

export const getAccountApprovalInfoApi = (params: { account_id: string }) =>
  createZlpRequest<ApprovalAccountInfoResp>(REAL_HOST, HttpRequestVerbs.GET).setQueryParams(params).build();

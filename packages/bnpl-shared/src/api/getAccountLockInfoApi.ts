import createZlpRequest from 'bnpl-shared/src/api/createZlpRequest';
import { LockAccountInfo } from 'bnpl-shared/src/types';
import { withBaseUrl } from 'bnpl-shared/src/api/withBaseUrl';
import { HttpRequestVerbs } from 'bnpl-shared/src/shared/api';

const REAL_HOST = withBaseUrl('/users/lock-info');

export const getAccountLockInfoApi = (account_id: string) =>
  createZlpRequest<LockAccountInfo>(REAL_HOST, HttpRequestVerbs.GET).setQueryParams({ account_id }).build();

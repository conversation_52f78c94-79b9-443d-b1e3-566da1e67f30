import { getAdResourceUrlWithEnv } from 'bnpl-shared/src/api/withBaseUrl';
import { HttpRequestVerbs } from 'bnpl-shared/src/shared/api';
import { getZLPToken } from 'bnpl-shared/src/shared/ZaloPayModules';
import RequestBuilder, { ContentType } from 'bnpl-shared/src/api/requestBuilder';
import { AdPopupResource } from 'bnpl-shared/src/types';
import { Platform } from 'react-native';

// const MOCK_HOST = 'https://darling-mustang-moving.ngrok-free.app/ads-for-product-page/Product_TKTS';
const getRealHost = () => `${getAdResourceUrlWithEnv()}/popup/content`;

export const getAdResourcePopupApi = async (inventory_id: string) => {
  const zlp_token = await getZLPToken();
  return new RequestBuilder<AdPopupResource>(getRealHost(), HttpRequestVerbs.POST)
    .setDataBody(JSON.stringify({ popup_inventory_id: inventory_id }))
    .setHeaders(
      Platform.OS !== 'web'
        ? {
            Authorization: `Bearer ${zlp_token}`,
            'Content-Type': `${ContentType.JSON}`,
          }
        : {
            cookie: document.cookie,
            'Content-Type': `${ContentType.JSON}`,
          },
    )
    .build();
};

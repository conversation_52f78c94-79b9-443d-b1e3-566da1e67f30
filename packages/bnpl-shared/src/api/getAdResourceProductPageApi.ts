import { getAdResourceUrlWithEnv } from 'bnpl-shared/src/api/withBaseUrl';
import { HttpRequestVerbs } from 'bnpl-shared/src/shared/api';
import { getZLPToken } from 'bnpl-shared/src/shared/ZaloPayModules';
import RequestBuilder from 'bnpl-shared/src/api/requestBuilder';
import { AdBannerResource } from 'bnpl-shared/src/types';
import { Platform } from 'react-native';

// const MOCK_HOST = 'https://darling-mustang-moving.ngrok-free.app/ads-for-product-page/Product_TKTS';
const getRealHost = (inventoryId: string) => `${getAdResourceUrlWithEnv()}/ads-for-product-page/${inventoryId}`;

export const getAdResourceProductPageApi = async (inventoryId: string) => {
  const zlp_token = await getZLPToken();
  return new RequestBuilder<AdBannerResource>(getRealHost(inventoryId), HttpRequestVerbs.GET)
    .setHeaders(
      Platform.OS !== 'web'
        ? {
            Authorization: `Bearer ${zlp_token}`,
          }
        : {
            cookie: document.cookie,
          },
    )
    .build();
};

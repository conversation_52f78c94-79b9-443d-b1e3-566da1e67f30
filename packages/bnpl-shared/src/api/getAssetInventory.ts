import { Platform } from 'react-native';
import { HttpRequestVerbs } from '../shared/api';
import { getZLPToken } from '../shared/ZaloPayModules';
import RequestBuilder, { ContentType } from './requestBuilder';
import { getAdResourceUrlWithEnv } from './withBaseUrl';

const getRealHost = () => `${getAdResourceUrlWithEnv()}/asset-inventory`;

export const getAssetInventory = async (asset_inventory_id: string) => {
  const zlp_token = await getZLPToken();
  return new RequestBuilder<any>(getRealHost(), HttpRequestVerbs.POST)
    .setDataBody(JSON.stringify({ asset_inventory_id }))
    .setHeaders(
      Platform.OS !== 'web'
        ? {
            Authorization: `Bearer ${zlp_token}`,
            'Content-Type': `${ContentType.JSON}`,
          }
        : {
            cookie: document.cookie,
            'Content-Type': `${ContentType.JSON}`,
          },
    )
    .build();
};

import createZlpRequest from 'bnpl-shared/src/api/createZlpRequest';
import { HttpRequestVerbs } from 'bnpl-shared/src/shared/api';
import { withBaseUrl } from 'bnpl-shared/src/api/withBaseUrl';
import { AutoRepaymentBindingStatus } from 'bnpl-shared/src/types';

// const MOCK_HOST = () => 'https://darling-mustang-moving.ngrok-free.app/autorepayment/binding/status';
const REAL_HOST = withBaseUrl('/autorepayment/binding/status');

export const getAutoRepaymentBindingStatus = () =>
  createZlpRequest<AutoRepaymentBindingStatus>(REAL_HOST, HttpRequestVerbs.GET).build();

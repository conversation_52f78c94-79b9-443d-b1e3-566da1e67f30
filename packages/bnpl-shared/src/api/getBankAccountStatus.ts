import createZlpRequest from 'bnpl-shared/src/api/createZlpRequest';
import { HttpRequestVerbs } from 'bnpl-shared/src/shared/api';
import { ContentType } from 'bnpl-shared/src/api/requestBuilder';
import { withBaseUrl } from './withBaseUrl';
import { BankAccountData } from '../types/BankAccountTypes';

const REAL_HOST = withBaseUrl('/users/bank-account-status');

export const getBankAccountStatusApi = () =>
  createZlpRequest<BankAccountData>(REAL_HOST, HttpRequestVerbs.GET)
    .setHeaders({ 'Content-Type': `${ContentType.JSON}` })
    .build();

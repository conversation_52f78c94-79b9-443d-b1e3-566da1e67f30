import { HttpRequestVerbs } from '../shared/api';
import { withBaseUrl } from './withBaseUrl';
import createZlpRequest from 'bnpl-shared/src/api/createZlpRequest';

const REAL_HOST = withBaseUrl('/users/binding/contract');
// const MOCK_HOST = () => 'http://0.0.0.0:8082/users/binding/contract';

type BindingContract = {
  contract_template?: string;
  signed_contract_url?: string;
};

export const getBindingContractApi = (params: { request_id: string }) =>
  createZlpRequest<BindingContract>(REAL_HOST, HttpRequestVerbs.GET).setQueryParams(params).build();

import { withBaseUrl } from './withBaseUrl';
import { HttpRequestVerbs } from 'bnpl-shared/src/shared/api';
import createZlpRequest from 'bnpl-shared/src/api/createZlpRequest';
import { BindingInfo } from '../types/BindingTypes';
import { transformCIMBBindingDataInterceptor } from 'bnpl-shared/src/api/interceptors/transformCIMBBindingDataInterceptor';

export const getBindingInfoApi = (persistData: boolean = true) =>
  createZlpRequest<BindingInfo>(withBaseUrl('/users/binding/info'), HttpRequestVerbs.GET)
    .addResponseInterceptors([transformCIMBBindingDataInterceptor(persistData)])
    .build();

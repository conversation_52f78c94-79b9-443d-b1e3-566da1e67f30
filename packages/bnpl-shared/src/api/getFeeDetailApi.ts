import { HttpRequestVerbs } from 'bnpl-shared/src/shared/api';
import { FeeDetail } from 'bnpl-shared/src/types/TransactionHistoryTypes';
import { withBaseUrl } from 'bnpl-shared/src/api/withBaseUrl';
import createZlpRequest from 'bnpl-shared/src/api/createZlpRequest';

// const MOCK_HOST = () => 'https://darling-mustang-moving.ngrok-free.app/fees';
const REAL_HOST = withBaseUrl('/fees');

export const getFeeDetailApi = (id: string) =>
  createZlpRequest<FeeDetail>(() => `${REAL_HOST()}/${id}`, HttpRequestVerbs.GET).build();

import { StatementHistoryType } from '../types/TransactionStatementTypes';
import { withBaseUrl } from './withBaseUrl';
import { HttpRequestVerbs } from 'bnpl-shared/src/shared/api';
import createZlpRequest from 'bnpl-shared/src/api/createZlpRequest';

export const getHistoryPaymentApi = () =>
  createZlpRequest<StatementHistoryType>(withBaseUrl('/statement/transactions'), HttpRequestVerbs.GET).build();

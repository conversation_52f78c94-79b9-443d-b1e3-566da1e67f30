import createZlpRequest from 'bnpl-shared/src/api/createZlpRequest';
import { LatestSignature } from 'bnpl-shared/src/types';
import { withBaseUrl } from 'bnpl-shared/src/api/withBaseUrl';
import { HttpRequestVerbs } from 'bnpl-shared/src/shared/api';

const REAL_HOST = withBaseUrl('/users/latest-signature');
// const MOCK_HOST = () => 'https://darling-mustang-moving.ngrok-free.app/users/lastest-signature';

export const getLatestSignatureApi = () => createZlpRequest<LatestSignature>(REAL_HOST, HttpRequestVerbs.GET).build();

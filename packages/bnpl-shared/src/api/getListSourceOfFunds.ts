import createZlpRequest from 'bnpl-shared/src/api/createZlpRequest';
import { withBaseUrl } from 'bnpl-shared/src/api/withBaseUrl';
import { HttpRequestVerbs } from 'bnpl-shared/src/shared/api';
import { SourceOfFundsResponse } from 'bnpl-shared/src/types';

const REAL_HOST = withBaseUrl('/autorepayment/sofs');
// const MOCK_HOST = () => 'https://darling-mustang-moving.ngrok-free.app/autorepayment/sofs';

export const getListSourceOfFunds = () =>
  createZlpRequest<SourceOfFundsResponse>(REAL_HOST, HttpRequestVerbs.GET).build();

import { HttpRequestVerbs } from '../shared/api';
import { withBaseUrl } from './withBaseUrl';
import createZlpRequest from 'bnpl-shared/src/api/createZlpRequest';

// const MOCK_HOST = () => 'https://darling-mustang-moving.ngrok-free.app/users/outstanding-balance';
const REAL_HOST = withBaseUrl('/users/outstanding-balance');

/**
 * @deprecated use getUserBalanceApi instead
 * @param force: boolean flag which indicates whether to force to fetch balance from bank
 */
export const getOutstandingBalanceApi = (force: boolean = false) =>
  createZlpRequest<{ outstanding_balance: number }>(REAL_HOST, HttpRequestVerbs.GET).setQueryParams({ force }).build();

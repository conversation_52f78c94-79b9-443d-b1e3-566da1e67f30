/* eslint-disable no-undef */
import {
  setBindingStatus,
  setKycLevel,
  setProfileLevel,
  setRejectDetail,
  setRiskInfo,
} from 'bnpl-shared/src/redux/bindingReducer';
import { store } from '../redux/store';
import { HttpRequestVerbs } from '../shared/api';
import { BindingStatus, RiskAction, UserIdType } from '../types';
import { withBaseUrl } from './withBaseUrl';
import createZlpRequest from 'bnpl-shared/src/api/createZlpRequest';
import RequestBuilder from 'bnpl-shared/src/api/requestBuilder';

// const MOCK_HOST = () => 'https://darling-mustang-moving.ngrok-free.app/users/permission';
const REAL_HOST = withBaseUrl('/users/permission');

type PermissionInfo = {
  is_fraud: boolean;
  is_valid: boolean;
  is_whitelisted: boolean;
  binding_status: BindingStatus;
  profile_level: number;
  kyc_level: number;
  id_type: UserIdType;
  is_allow_resubmit: boolean;
  ctas?: string[];
  description?: string;
  risk_info?: {
    is_risk: boolean;
    action: RiskAction;
    action_url: string;
  };
};

/**
 * Get the permission of current user (is fraud? and is valid?)
 * @returns response object of permission. See type Response above.
 */
export const getPermissionApi = () =>
  createZlpRequest<PermissionInfo>(REAL_HOST, HttpRequestVerbs.GET)
    .addResponseInterceptors([
      async ({ response, RequestBuilder }: { response: Response; RequestBuilder: RequestBuilder<ResponseType> }) => {
        if (response.ok) {
          try {
            const data = await response.clone().json();
            store.dispatch(setProfileLevel(data.profile_level));
            store.dispatch(setKycLevel(data.kyc_level));
            store.dispatch(setBindingStatus(data.binding_status));
            store.dispatch(setRejectDetail({ ctas: data.ctas, description: data.description }));
            store.dispatch(setRiskInfo(data.risk_info));
          } catch (e) {}
        }
        return {
          response,
          RequestBuilder,
        };
      },
    ])
    .build();

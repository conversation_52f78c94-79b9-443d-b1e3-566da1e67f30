import createZlpRequest from 'bnpl-shared/src/api/createZlpRequest';
import { HttpRequestVerbs } from 'bnpl-shared/src/shared/api';
import { AppUrlPathBuilder } from 'bnpl-shared/src/api/UrlPathBuilder';
import { RoutingInfo } from 'bnpl-shared/src/types';

const REAL_HOST = () => new AppUrlPathBuilder('/onboardings/routing-infos', { version: 'v2' }).build();
// const MOCK_HOST = () => 'https://darling-mustang-moving.ngrok-free.app/onboardings/routing-infos';
export const getRoutingInfoApi = () => createZlpRequest<RoutingInfo[]>(REAL_HOST, HttpRequestVerbs.GET).build();

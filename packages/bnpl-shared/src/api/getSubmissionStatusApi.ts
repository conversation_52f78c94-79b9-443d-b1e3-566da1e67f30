import { HttpRequestVerbs } from '../shared/api';
import { OnboardingStatus } from '../types';
import { withBaseUrl } from './withBaseUrl';
import createZlpRequest from 'bnpl-shared/src/api/createZlpRequest';

const REAL_HOST = withBaseUrl('/users/onboarding/status');
// const MOCK_HOST = () => 'https://darling-mustang-moving.ngrok-free.app/users/onboarding/status';

type SubmissionStatus = {
  onboarding_status: OnboardingStatus;
  contract_template?: string;
  signed_contract_url?: string;
};

/**
 * @deprecated: use getSubmissionStatusV2Api instead
 */
export const getSubmissionStatusApi = (params: { request_id: string }) =>
  createZlpRequest<SubmissionStatus>(REAL_HOST, HttpRequestVerbs.GET).setQueryParams(params).build();

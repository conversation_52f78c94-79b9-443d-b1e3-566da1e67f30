/* eslint-disable no-undef */
import { HttpRequestVerbs } from '../shared/api';
import { OnboardingStatus } from '../types';
import { withBaseUrl } from './withBaseUrl';
import createZlpRequest from 'bnpl-shared/src/api/createZlpRequest';
import { store } from 'bnpl-shared/src/redux/store';
import { setOnboardingStatus as setOnboardingStatusAction } from 'bnpl-shared/src/redux/bindingReducer';
import RequestBuilder from 'bnpl-shared/src/api/requestBuilder';

const REAL_HOST = withBaseUrl('/users/onboarding/status', { version: 'v2' });
// const MOCK_HOST = () => 'https://darling-mustang-moving.ngrok-free.app/users/onboarding/status';

type SubmissionStatusV2 = {
  onboarding_status: OnboardingStatus;
};

export const getSubmissionStatusV2Api = (params: { request_id: string }) =>
  createZlpRequest<SubmissionStatusV2>(REAL_HOST, HttpRequestVerbs.GET)
    .setQueryParams(params)
    .addResponseInterceptors([
      async ({ response, RequestBuilder }: { response: Response; RequestBuilder: RequestBuilder<ResponseType> }) => {
        if (response.ok) {
          try {
            const data = await response.clone().json();
            store.dispatch(setOnboardingStatusAction(data.onboarding_status));
          } catch (e) {}
        }
        return {
          response,
          RequestBuilder,
        };
      },
    ])
    .build();

import { StatementSummaryType } from 'bnpl-shared/src/types';
import { HttpRequestVerbs } from '../shared/api';
import createZlpRequest from './createZlpRequest';
import { withBaseUrl } from 'bnpl-shared/src/api/withBaseUrl';

// const MOCK_HOST = () => 'https://darling-mustang-moving.ngrok-free.app/statement';
const REAL_HOST = withBaseUrl('/statement');

export const getSummaryStatementApi = () =>
  createZlpRequest<StatementSummaryType | null>(REAL_HOST, HttpRequestVerbs.GET).build();

import { withBaseUrl } from 'bnpl-shared/src/api/withBaseUrl';
import { HttpRequestVerbs } from 'bnpl-shared/src/shared/api';
import { TransactionFilterType } from 'bnpl-shared/src/types';
import { TransactionHistory } from 'bnpl-shared/src/types/TransactionHistoryTypes';
import createZlpRequest from 'bnpl-shared/src/api/createZlpRequest';

type TransHisByAccountIdRequest = {
  limit: number;
  cursor?: string;
  start_time?: string;
  end_time?: string;
  trans_type?: TransactionFilterType[] | undefined;
  account_id?: string;
};

// const MOCK_HOST = () => 'https://darling-mustang-moving.ngrok-free.app/transactions-by-account-id';
const REAL_HOST = withBaseUrl('/transactions-by-account-id');

export const getTransHisByAccountIdApi = (params: TransHisByAccountIdRequest) =>
  createZlpRequest<TransactionHistory>(REAL_HOST, HttpRequestVerbs.GET)
    .setQueryParams(params)
    .setStringifyOptions({ encode: false })
    .build();

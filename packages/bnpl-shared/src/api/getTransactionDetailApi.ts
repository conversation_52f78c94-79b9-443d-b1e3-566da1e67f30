import { HttpRequestVerbs } from 'bnpl-shared/src/shared/api';
import { TransactionHistoryDetail, TransactionStage } from 'bnpl-shared/src/types/TransactionHistoryTypes';
import { withBaseUrl } from 'bnpl-shared/src/api/withBaseUrl';
import createZlpRequest from 'bnpl-shared/src/api/createZlpRequest';

type Response = TransactionHistoryDetail & { stages?: { [key: string]: TransactionStage } };

export const getTransactionDetailApi = (id: string) =>
  createZlpRequest<Response>(withBaseUrl(`/transactions/${id}`), HttpRequestVerbs.GET)
    .setRetryOptions({ retryCount: 20, retryInterval: 2000 })
    .addResponseInterceptors([
      async ({ response, RequestBuilder }) => {
        if (response.ok) {
          const data = await response.clone().json();
          if (data && data.stages) {
            const modifyResponse = {
              ...data,
              stages: data.stages
                ? Object.keys(data.stages)
                    .map(key => [Number(key), data.stages[key]] as const)
                    .sort(([key1], [key2]) => key1 - key2)
                    .map(([, stage]) => stage)
                : data.stages,
            };
            response.json = async () => modifyResponse;
          }
        }
        return { response, RequestBuilder };
      },
    ])
    .build();

import { withBaseUrl } from 'bnpl-shared/src/api/withBaseUrl';
import { HttpRequestVerbs } from 'bnpl-shared/src/shared/api';
import { TransactionFilterType } from 'bnpl-shared/src/types';
import { TransactionHistory } from 'bnpl-shared/src/types/TransactionHistoryTypes';
import createZlpRequest from 'bnpl-shared/src/api/createZlpRequest';

type TransactionHistoryRequest = {
  limit: number;
  cursor?: string;
  start_time?: string;
  end_time?: string;
  trans_type?: TransactionFilterType[] | undefined;
};

// const MOCK_HOST = () => 'https://darling-mustang-moving.ngrok-free.app/transactions';
const REAL_HOST = withBaseUrl('/transactions');

export const getTransactionHistoryApi = (params: TransactionHistoryRequest) =>
  createZlpRequest<TransactionHistory>(REAL_HOST, HttpRequestVerbs.GET).setQueryParams(params).build();

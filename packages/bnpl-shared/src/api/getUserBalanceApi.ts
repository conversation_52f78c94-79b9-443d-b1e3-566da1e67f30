import { HttpRequestVerbs } from '../shared/api';
import { UserBalanceResp } from '../types';
import { withBaseUrl } from './withBaseUrl';
import createZlpRequest from 'bnpl-shared/src/api/createZlpRequest';

// const MOCK_HOST = () => 'https://darling-mustang-moving.ngrok-free.app/users/balance';
const REAL_HOST = withBaseUrl('/users/balance');

export const getUserBalanceApi = (params?: { account_id?: string }) =>
  createZlpRequest<UserBalanceResp>(REAL_HOST, HttpRequestVerbs.GET).setQueryParams(params).build();

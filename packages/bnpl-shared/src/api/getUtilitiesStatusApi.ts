import createZlpRequest from 'bnpl-shared/src/api/createZlpRequest';
import { HttpRequestVerbs } from 'bnpl-shared/src/shared/api';
import { withBaseUrl } from 'bnpl-shared/src/api/withBaseUrl';
import { UtilityConfig } from 'bnpl-shared/src/constants';
import { UtilityStatusType } from 'bnpl-shared/src/types';

// const MOCK_HOST = () => 'https://darling-mustang-moving.ngrok-free.app/utilities/status';
const REAL_HOST = withBaseUrl('/utilities/status');

export const getUtilitiesStatusApi = (params: { utilities: UtilityConfig[] }) =>
  createZlpRequest<UtilityStatusType[]>(REAL_HOST, HttpRequestVerbs.GET).setQueryParams(params).build();

/* eslint-disable no-undef */
import { zlpResponseUnwrapInterceptor } from './zlpResponseUnwrapInterceptor';
import RequestBuilder from 'bnpl-shared/src/api/requestBuilder';
import { HttpRequestVerbs } from 'bnpl-shared/src/shared/api';
import {
  createFailMockResp,
  createSuccessMockResp,
  fakeBindingInfo,
  mockFetch,
} from 'bnpl-shared/src/jest/fakeData';
import { zlpAuthorizeInterceptor } from 'bnpl-shared/src/api/interceptors/zlpAuthorizeInterceptor';
import { Platform } from 'react-native';
import { getUserInfo, getZLPToken } from 'bnpl-shared/src/shared/ZaloPayModules';
import { maintenanceInterceptor } from 'bnpl-shared/src/api/interceptors/maintenanceInterceptor';
import { store } from 'bnpl-shared/src/redux/store';
import { setIsMaintenance } from 'bnpl-shared/src/redux/globalReducer';
import { sentryLoggingInterceptor } from 'bnpl-shared/src/api/interceptors/sentryLoggingInterceptor';
import { captureMessage } from 'bnpl-shared/src/shared/sentry';
import { Severity } from '@sentry/types';
import { transformCIMBBindingDataInterceptor } from 'bnpl-shared/src/api/interceptors/transformCIMBBindingDataInterceptor';
import { BindingStep } from 'bnpl-shared/src/types';
import { postInitOnboardingSignContractApi } from 'bnpl-shared/src/api/postInitOnboardingSignContractApi';

jest.mock('bnpl-shared/src/api/postInitOnboardingSignContractApi', () => ({
  postInitOnboardingSignContractApi: jest.fn(),
}));
jest.mock('bnpl-shared/src/shared/ZaloPayModules', () => ({ getUserInfo: jest.fn(), getZLPToken: jest.fn() }));
jest.mock('bnpl-shared/src/shared/sentry', () => ({ captureMessage: jest.fn() }));

const spyOnDispatch = jest.spyOn(store, 'dispatch');

beforeEach(() => {
  (global.fetch as jest.Mock).mockClear();
  spyOnDispatch.mockClear();
});

const jsonCloneInterceptor = async ({
  response,
  RequestBuilder,
}: {
  response: Response;
  RequestBuilder: RequestBuilder<ResponseType>;
}) => {
  const jsonHandler = response.clone().json;
  return {
    response: {
      ...response,
      json: jsonHandler,
    },
    RequestBuilder,
  };
};

describe(zlpResponseUnwrapInterceptor.name, () => {
  it('verify executed as expected', async () => {
    mockFetch(createSuccessMockResp({ code: 0, message: '', data: { success: true } }));
    const response = await new RequestBuilder<any>('test_url', HttpRequestVerbs.GET)
      .addResponseInterceptors([zlpResponseUnwrapInterceptor, jsonCloneInterceptor])
      .build();
    expect(response).toEqual({ success: true });
  });
});

describe(sentryLoggingInterceptor.name, () => {
  beforeEach(() => {
    (captureMessage as jest.Mock).mockClear();
  });

  it('verify executed as expected', async () => {
    mockFetch({ ...createFailMockResp({ message: 'fail' }, 400), url: 'test_url' });
    try {
      await new RequestBuilder<any>('test_url', HttpRequestVerbs.GET)
        .addResponseInterceptors([sentryLoggingInterceptor()])
        .build();
    } catch (err: any) {}
    expect(captureMessage as jest.Mock).toHaveBeenCalledWith('Request fail: 400 - ..test_url', {
      level: Severity.Error,
      tags: {
        api_error: 'test_url',
        scope: 'network_request',
      },
      extra: {
        errorBody: { message: 'fail' },
      },
    });
  });
  it('verify suppressError option handle as expected', async () => {
    mockFetch({ ...createFailMockResp({ message: 'fail' }, 400), url: 'test_url' });
    try {
      await new RequestBuilder<any>('test_url', HttpRequestVerbs.GET)
        .addResponseInterceptors([sentryLoggingInterceptor({ ignoreErrorCodes: [400] })])
        .build();
    } catch (err: any) {}
    expect(captureMessage as jest.Mock).not.toHaveBeenCalled();
  });
});

describe(zlpAuthorizeInterceptor.name, () => {
  it('verify on zpi executed as expected', async () => {
    Platform.OS = 'web';
    mockFetch(createSuccessMockResp({ code: 0, message: '', data: { success: true } }));
    await new RequestBuilder<any>('test_url', HttpRequestVerbs.GET)
      .addRequestInterceptors([zlpAuthorizeInterceptor])
      .setHeaders({ 'test-key': 'test-value' })
      .build();
    expect(global.fetch as jest.Mock).toHaveBeenCalledWith('test_url', {
      body: undefined,
      credentials: 'include',
      headers: { 'test-key': 'test-value' },
      method: 'GET',
      mode: 'cors',
    });
  });
  it('verify on zpa executed as expected', async () => {
    Platform.OS = 'android';
    (getUserInfo as jest.Mock).mockResolvedValue({ mUid: 'mUid', mAccessToken: 'mAccessToken' });
    (getZLPToken as jest.Mock).mockResolvedValue('zlp_token');
    mockFetch(createSuccessMockResp({ code: 0, message: '', data: { success: true } }));
    await new RequestBuilder<any>('test_url', HttpRequestVerbs.GET)
      .addRequestInterceptors([zlpAuthorizeInterceptor])
      .build();
    expect(global.fetch as jest.Mock).toHaveBeenCalledWith('test_url', {
      body: undefined,
      credentials: 'include',
      headers: {
        'm-access-token': 'mAccessToken',
        'm-uid': 'mUid',
        sessionid: 'zlp_token',
        Authorization: `Bearer zlp_token`,
      },
      method: 'GET',
      mode: 'cors',
    });
  });
});

describe(maintenanceInterceptor.name, () => {
  it('verify maintenance is enabled as expected', async () => {
    mockFetch(createFailMockResp({ code: 14 }, 503));
    try {
      await new RequestBuilder<any>('test_url', HttpRequestVerbs.GET)
        .addResponseInterceptors([maintenanceInterceptor])
        .build();
    } catch (err: any) {}
    expect(store.getState().globalReducer.is_maintenance).toBeTruthy();
  });
  it('verify maintenance is disabled as expected', async () => {
    store.dispatch(setIsMaintenance(true));
    mockFetch(createSuccessMockResp({ message: 'success' }));
    await new RequestBuilder<any>('test_url', HttpRequestVerbs.GET)
      .addResponseInterceptors([maintenanceInterceptor])
      .build();
    expect(store.getState().globalReducer.is_maintenance).toBeFalsy();
  });
});

describe(transformCIMBBindingDataInterceptor.name, () => {
  it('persist data as expected', async () => {
    mockFetch(
      createSuccessMockResp({
        code: 0,
        message: '',
        data: {
          ...fakeBindingInfo,
          binding_step: {
            current_step: {
              int_value: BindingStep.BOD1_SUCCEEDED,
              string_value: 'bod1_succeeded',
            },
          },
        },
      }),
    );
    await new RequestBuilder<any>('test_url', HttpRequestVerbs.GET)
      .addResponseInterceptors([
        zlpResponseUnwrapInterceptor,
        jsonCloneInterceptor,
        transformCIMBBindingDataInterceptor(true),
      ])
      .build();
    expect(spyOnDispatch).toHaveBeenNthCalledWith(2, {
      type: 'binding/setOnboardingId',
      payload: '169919166789189632',
    });
    expect(spyOnDispatch).toHaveBeenNthCalledWith(3, {
      type: 'binding/setBindingInfo',
      payload: { basic_profile: fakeBindingInfo.basic_profile, detail_profile: fakeBindingInfo.detail_profile },
    });
    expect(spyOnDispatch).toHaveBeenNthCalledWith(4, {
      type: 'binding/setBindingStep',
      payload: BindingStep.BOD1_SUCCEEDED,
    });
  });
  it('not persist data as expected', async () => {
    mockFetch(
      createSuccessMockResp({
        code: 0,
        message: '',
        data: fakeBindingInfo,
      }),
    );
    await new RequestBuilder<any>('test_url', HttpRequestVerbs.GET)
      .addResponseInterceptors([
        zlpResponseUnwrapInterceptor,
        jsonCloneInterceptor,
        transformCIMBBindingDataInterceptor(false),
      ])
      .build();
    expect(spyOnDispatch).not.toHaveBeenCalled();
  });
  it('chaining request check face challenge when current_step = BOD2_SUCCEEDED', async () => {
    (postInitOnboardingSignContractApi as jest.Mock).mockResolvedValue({ authentication_type: 'PARTNER_SELFIE' });
    mockFetch(
      createSuccessMockResp({
        code: 0,
        message: '',
        data: fakeBindingInfo,
      }),
    );
    await new RequestBuilder<any>('test_url', HttpRequestVerbs.GET)
      .addResponseInterceptors([
        zlpResponseUnwrapInterceptor,
        jsonCloneInterceptor,
        transformCIMBBindingDataInterceptor(true),
      ])
      .build();
    expect(spyOnDispatch).toHaveBeenNthCalledWith(4, {
      type: 'binding/setBindingStep',
      payload: BindingStep._BOD2_FACE_CHALLENGE,
    });
  });
});

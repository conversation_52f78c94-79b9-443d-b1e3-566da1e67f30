/* eslint-disable no-undef */
import RequestBuilder from 'bnpl-shared/src/api/requestBuilder';
import { HttpCode } from 'bnpl-shared/src/shared/api';
import get from 'lodash/get';
import { store } from 'bnpl-shared/src/redux/store';
import { setIsMaintenance } from 'bnpl-shared/src/redux/globalReducer';

export const maintenanceInterceptor = async ({
  response,
  RequestBuilder,
}: {
  response: Response;
  RequestBuilder: RequestBuilder<ResponseType>;
}) => {
  if (response.status === HttpCode.SERVICE_UNAVAILABLE) {
    const errorBody = await response.clone()?.json();
    const errorCode = get(errorBody, 'code');
    if (errorCode === 14) {
      store.dispatch(setIsMaintenance(true));
    }
  } else if (response.status !== HttpCode.SERVICE_UNAVAILABLE && store.getState()?.globalReducer?.is_maintenance) {
    store.dispatch(setIsMaintenance(false));
  }
  return { response, RequestBuilder };
};

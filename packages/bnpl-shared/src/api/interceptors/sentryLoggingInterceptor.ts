/* eslint-disable no-undef */
import { buildBaseUrl, getPayLaterDomain } from 'bnpl-shared/src/api/withBaseUrl';
import { captureMessage } from 'bnpl-shared/src/shared/sentry';
import { Severity } from '@sentry/types';
import RequestBuilder from 'bnpl-shared/src/api/requestBuilder';
import { HttpCode } from 'bnpl-shared/src/shared/api';
import { SuppressError } from 'bnpl-shared/src/api/createZlpRequest';
import { environment } from 'bnpl-shared/src/shared/environment';

export const sentryLoggingInterceptor =
  (suppressError?: SuppressError) =>
  async ({ response, RequestBuilder }: { response: Response; RequestBuilder: RequestBuilder<ResponseType> }) => {
    if (!response.ok) {
      if (
        response.status !== HttpCode.TOO_MANY_REQUEST &&
        !suppressError?.ignoreErrorCodes?.includes(response.status)
      ) {
        const apiPath = `${response.url?.replace?.(`${getPayLaterDomain(environment)}/api`, '')}`;
        let errorBody = {};
        try {
          errorBody = await response.clone().json();
        } catch (err: any) {
          errorBody = {};
        }
        captureMessage(`Request fail: ${response.status} - ..${apiPath}`, {
          level: Severity.Error,
          tags: {
            api_error: apiPath,
            scope: 'network_request',
          },
          extra: {
            errorBody,
          },
        });
      }
    }
    return { response, RequestBuilder };
  };

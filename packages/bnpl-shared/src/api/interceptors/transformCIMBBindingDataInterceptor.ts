/* eslint-disable no-undef */
import { store } from 'bnpl-shared/src/redux/store';
import {
  setBindingInfo,
  setBindingStep,
  setBindingStepString,
  setOnboardingId,
} from 'bnpl-shared/src/redux/bindingReducer';
import { BindingStep, SignMethod } from 'bnpl-shared/src/types';
import RequestBuilder from 'bnpl-shared/src/api/requestBuilder';
import { postInitOnboardingSignContractApi } from 'bnpl-shared/src/api/postInitOnboardingSignContractApi';

const shouldCheckFaceChallenge = async (requestId: string): Promise<boolean> => {
  try {
    const { authentication_type } = await postInitOnboardingSignContractApi(requestId);
    return authentication_type === SignMethod.FACE;
  } catch (error: any) {
    return false;
  }
};

export const transformCIMBBindingDataInterceptor =
  (persistData: boolean) =>
  async ({ response, RequestBuilder }: { response: Response; RequestBuilder: RequestBuilder<ResponseType> }) => {
    if (response.ok && persistData) {
      try {
        const data = await response.clone().json();
        const binding_step_string = data.binding_step?.current_step?.string_value;
        binding_step_string && store.dispatch(setBindingStepString(binding_step_string));
        let bindingStep =
          data.binding_step?.current_step?.int_value === undefined ? null : data.binding_step.current_step.int_value;
        store.dispatch(setOnboardingId(data.request_id === '0' ? '' : data.request_id));
        store.dispatch(setBindingInfo({ basic_profile: data.basic_profile, detail_profile: data.detail_profile }));
        if (bindingStep === BindingStep.BOD2_SUCCEEDED) {
          const shouldCheck = await shouldCheckFaceChallenge(data.request_id);
          bindingStep = shouldCheck ? BindingStep._BOD2_FACE_CHALLENGE : BindingStep._FACE_CHALLENGE_COMPLETED;
        }
        store.dispatch(setBindingStep(bindingStep));
      } catch (e) {}
    }
    return {
      response,
      RequestBuilder,
    };
  };

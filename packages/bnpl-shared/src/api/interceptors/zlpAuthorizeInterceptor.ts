/* eslint-disable no-undef */
import { Platform } from 'react-native';
import { getUserInfo, getZLPToken } from 'bnpl-shared/src/shared/ZaloPayModules';

export const zlpAuthorizeInterceptor = async (request: RequestInit) => {
  if (Platform.OS === 'web') {
    return {
      ...request,
      headers: {
        ...request.headers,
      },
    };
  }
  const { mUid, mAccessToken } = await getUserInfo();
  let authenHeaders: any = {};
  if (mUid && mAccessToken) {
    authenHeaders = {
      'm-uid': mUid,
      'm-access-token': mAccessToken,
    };
  }
  const zlp_token = (await getZLPToken()) || '';
  if (zlp_token) {
    authenHeaders = {
      ...authenHeaders,
      sessionid: zlp_token,
      Authorization: `Bearer ${zlp_token}`,
    };
  }
  return {
    ...request,
    headers: {
      ...request.headers,
      ...authenHeaders,
    },
  };
};

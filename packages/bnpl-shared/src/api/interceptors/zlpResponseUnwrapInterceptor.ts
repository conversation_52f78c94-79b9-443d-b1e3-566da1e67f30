/* eslint-disable no-undef */
import RequestBuilder, { SuccessfulResponse } from 'bnpl-shared/src/api/requestBuilder';
import { ServiceResponseSubcode } from 'bnpl-shared/src/types';

export const zlpResponseUnwrapInterceptor = async ({
  response,
  RequestBuilder,
}: {
  response: Response;
  RequestBuilder: RequestBuilder<ResponseType>;
}) => {
  if (response.ok) {
    try {
      const rawResp = (await response.clone().json()) as SuccessfulResponse<ResponseType>;
      if (rawResp.code === ServiceResponseSubcode.SUCCESS) {
        const jsonHandler = async () => {
          return rawResp.data;
        };
        response.json = jsonHandler;
        response.clone = () => {
          return {
            ...response,
            json: jsonHandler,
          };
        };
      }
    } catch (err) {
      console.log(err);
    }
  }
  return { response, RequestBuilder };
};

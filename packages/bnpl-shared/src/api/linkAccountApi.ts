import { withBaseUrl } from './withBaseUrl';
import { HttpRequestVerbs } from 'bnpl-shared/src/shared/api';
import createZlpRequest from 'bnpl-shared/src/api/createZlpRequest';
import { ContentType } from 'bnpl-shared/src/api/requestBuilder';

type Params = {
  request_id: string;
};

export const linkAccountApi = (params: Params) =>
  createZlpRequest<object>(withBaseUrl('/users/link-account'), HttpRequestVerbs.POST)
    .setDataBody(JSON.stringify(params))
    .setHeaders({ 'Content-Type': `${ContentType.JSON}` })
    .build();

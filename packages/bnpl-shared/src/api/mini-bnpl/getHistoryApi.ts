import { AppUrlPathBuilder } from 'bnpl-shared/src/api/UrlPathBuilder';
import { HttpRequestVerbs } from "bnpl-shared/src/shared/api";
import { HistoryItem } from "bnpl-shared/src/types/MiniBnpl";
import createZlpRequest from "../createZlpRequest";

const getRealHost = () => () =>
  new AppUrlPathBuilder('/bills', { version: 'v2', partner: 'mini-bnpl' }).build();

export const getRepaymentHistoryApi = (params: { account_id: string }) =>
  createZlpRequest<HistoryItem[]>(getRealHost(), HttpRequestVerbs.GET)
    .setQueryParams(params)
    .setRetryOptions({ retryCount: 3, retryInterval: 1000 })
    .build();

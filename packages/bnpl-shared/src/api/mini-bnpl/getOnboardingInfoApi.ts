import { AppUrlPathBuilder } from 'bnpl-shared/src/api/UrlPathBuilder';
import createZlpRequest from 'bnpl-shared/src/api/createZlpRequest';
import { HttpRequestVerbs } from 'bnpl-shared/src/shared/api';

const getRealHost = () => () =>
  new AppUrlPathBuilder('/onboardings/info', { version: 'v2', partner: 'mini-bnpl' }).build();

export const getOnboardingInfoApi = () =>
  createZlpRequest<{
    full_name: string;
    phone: string;
    permanent_address: string;
  }>(getRealHost(), HttpRequestVerbs.GET)
    .build();

import { AppUrlPathBuilder } from 'bnpl-shared/src/api/UrlPathBuilder';
import createZlpRequest from 'bnpl-shared/src/api/createZlpRequest';
import { MOCK_MINI_BNPL_API } from 'bnpl-shared/src/api/mini-bnpl';
import { HttpRequestVerbs } from 'bnpl-shared/src/shared/api';
import { MiniBNPLOnboardingStatusResp } from 'bnpl-shared/src/types';

const getRealHost = () => () =>
  new AppUrlPathBuilder('/onboardings/status', { version: 'v2', partner: 'mini-bnpl' }).build();
const MOCK_HOST = () => 'https://darling-mustang-moving.ngrok-free.app/mini-bnpl/onboardings';

export const getOnboardingStatusApi = () =>
  createZlpRequest<MiniBNPLOnboardingStatusResp>(MOCK_MINI_BNPL_API ? MOCK_HOST : getRealHost(), HttpRequestVerbs.GET)
    .setRetryOptions({ retryCount: 3, retryInterval: 1000 })
    .build();

import createZlpRequest from 'bnpl-shared/src/api/createZlpRequest';
import { AppUrlPathBuilder } from 'bnpl-shared/src/api/UrlPathBuilder';
import { HttpRequestVerbs } from 'bnpl-shared/src/shared/api';
import { TotalOutstandingData } from "bnpl-shared/src/types/MiniBnpl";

const REAL_HOST = () => new AppUrlPathBuilder('/total-outstanding', { version: 'v2', partner: 'mini-bnpl' }).build();
const MOCK_HOST = () => 'https://darling-mustang-moving.ngrok-free.app/account/statement';

export const getTotalOutstandingApi = (account_id: string) =>
  createZlpRequest<TotalOutstandingData>(REAL_HOST, HttpRequestVerbs.GET)
    .setQueryParams({ account_id })
    .build();

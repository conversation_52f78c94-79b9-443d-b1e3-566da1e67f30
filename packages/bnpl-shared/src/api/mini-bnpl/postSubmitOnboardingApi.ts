import createZlpRequest from 'bnpl-shared/src/api/createZlpRequest';
import { HttpRequestVerbs } from 'bnpl-shared/src/shared/api';
import { AppUrlPathBuilder } from 'bnpl-shared/src/api/UrlPathBuilder';
import { ContentType } from 'bnpl-shared/src/api/requestBuilder';
import { MiniBNPLOnboardingStatusResp } from 'bnpl-shared/src/types';
import { MOCK_MINI_BNPL_API } from 'bnpl-shared/src/api/mini-bnpl';

const REAL_HOST = () => new AppUrlPathBuilder('/onboardings/submit', { version: 'v2', partner: 'mini-bnpl' }).build();
const MOCK_HOST = () => 'https://darling-mustang-moving.ngrok-free.app/mini-bnpl/onboardings/submit';

export const postSubmitOnboardingApi = () =>
  createZlpRequest<MiniBNPLOnboardingStatusResp>(MOCK_MINI_BNPL_API ? MOCK_HOST : REAL_HOST, HttpRequestVerbs.POST)
    .setHeaders({ 'Content-Type': `${ContentType.JSON}` })
    .build();

import { AppUrlPathBuilder } from 'bnpl-shared/src/api/UrlPathBuilder';
import { HttpRequestVerbs } from "bnpl-shared/src/shared/api";
import { HistoryItem } from "bnpl-shared/src/types/MiniBnpl";
import createZlpRequest from "../createZlpRequest";
import { ContentType } from '../requestBuilder';
import { MiniBNPLOnboardingStatusResp } from 'bnpl-shared/src/types';

const getRealHost = () =>
  new AppUrlPathBuilder('/onboardings/verify-face-authen', { version: 'v2', partner: 'mini-bnpl' }).build();

export const verifyFaceChallengeApi = (params: { um_request_id: string }) =>
  createZlpRequest<MiniBNPLOnboardingStatusResp>(getRealHost, HttpRequestVerbs.POST)
    .setDataBody(JSON.stringify(params))
    .setHeaders({ 'Content-Type': `${ContentType.JSON}` })
    .build();

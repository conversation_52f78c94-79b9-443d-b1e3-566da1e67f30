/* eslint-disable no-undef */
import RequestBuilder from 'bnpl-shared/src/api/requestBuilder';
import { HttpRequestVerbs } from '../../shared/api';
import createZlpRequest from 'bnpl-shared/src/api/createZlpRequest';
import { DebtObligationResp, DebtObligation } from 'bnpl-shared/src/types';
import { AppUrlPathBuilder } from 'bnpl-shared/src/api/UrlPathBuilder';

const REAL_HOST = () => new AppUrlPathBuilder('/account/debt-obligation', { version: 'v2' }).build();
// const MOCK_HOST = () => 'https://darling-mustang-moving.ngrok-free.app/account/debt-obligation';

export const getAccountDebtObligationApi = (account_id: string) =>
  createZlpRequest<DebtObligation>(REAL_HOST, HttpRequestVerbs.GET)
    .setQueryParams({ account_id })
    .addResponseInterceptors([
      async ({ response, RequestBuilder }: { response: Response; RequestBuilder: RequestBuilder<ResponseType> }) => {
        if (response.ok) {
          const data = (await response.clone().json()) as DebtObligationResp;
          const debtObligation: DebtObligation = {
            zalopay_id: data.zalopay_id,
            partner_code: data.partner_code,
            repayment_options: data.repayment_options.map(option => ({
              selection_id: option.selection_id,
              type: option.type,
              metadata: {
                display_amount: option.metadata.display_amount?.value || '',
                message: option.metadata.message?.value || '',
                repay_value: option.metadata.repay_value?.value || '',
                title: option.metadata.title?.value || '',
                fee_amount: option.metadata.fee_amount?.value || '',
                min_amount: option.metadata.min_amount?.value || '',
                description: option.metadata.description?.value || '',
              },
            })),
            ...(data?.service_fee ? { service_fee: data?.service_fee } : undefined),
            ...(data?.info_message ? { info_message: data?.info_message } : undefined),
          };
          const jsonHandler = async () => {
            return debtObligation;
          };
          response.json = jsonHandler;
          response.clone = () => {
            return {
              ...response,
              json: jsonHandler,
            };
          };
        }
        return {
          response,
          RequestBuilder,
        };
      },
    ])
    .build();

/* eslint-disable no-undef */
import createZlpRequest from 'bnpl-shared/src/api/createZlpRequest';
import { HttpRequestVerbs } from 'bnpl-shared/src/shared/api';
import { AppUrlPathBuilder } from 'bnpl-shared/src/api/UrlPathBuilder';
import { MOCK_LOTTE_PARTNER_API } from 'bnpl-shared/src/api/partner_lotte/index';
import { AccountInfo } from 'bnpl-shared/src/types';
import { store } from 'bnpl-shared/src/redux/store';
import RequestBuilder from 'bnpl-shared/src/api/requestBuilder';
import { setAccountInfo } from 'bnpl-shared/src/redux/accountInfoReducer';

const REAL_HOST = () => new AppUrlPathBuilder('/accounts/info', { version: 'v2' }).build();
const MOCK_HOST = () => 'https://darling-mustang-moving.ngrok-free.app/accounts/info';

export const getAccountInfoApi = (account_id: string) =>
  createZlpRequest<AccountInfo>(MOCK_LOTTE_PARTNER_API ? MOCK_HOST : REAL_HOST, HttpRequestVerbs.GET)
    .setQueryParams({ account_id })
    .addResponseInterceptors([
      async ({ response, RequestBuilder }: { response: Response; RequestBuilder: RequestBuilder<ResponseType> }) => {
        if (response.ok) {
          try {
            const data = await response.clone().json();
            store.dispatch(setAccountInfo({ partner: data.partner_code, data }));
          } catch (e) {}
        }
        return {
          response,
          RequestBuilder,
        };
      },
    ])
    .build();

import createZlpRequest from 'bnpl-shared/src/api/createZlpRequest';
import { MOCK_LOTTE_PARTNER_API } from 'bnpl-shared/src/api/partner_lotte/index';
import { HttpRequestVerbs } from 'bnpl-shared/src/shared/api';
import { AccountStatementType } from 'bnpl-shared/src/types';
import { AppUrlPathBuilder } from 'bnpl-shared/src/api/UrlPathBuilder';

const REAL_HOST = () => new AppUrlPathBuilder('/account/statement', { version: 'v2' }).build();
const MOCK_HOST = () => 'https://darling-mustang-moving.ngrok-free.app/account/statement';

export const getAccountStatementApi = (account_id: string) =>
  createZlpRequest<AccountStatementType>(MOCK_LOTTE_PARTNER_API ? MOCK_HOST : REAL_HOST, HttpRequestVerbs.GET)
    .setQueryParams({ account_id })
    .build();

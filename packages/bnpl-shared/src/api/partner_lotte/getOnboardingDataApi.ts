import { AppUrlPathBuilder } from 'bnpl-shared/src/api/UrlPathBuilder';
import createZlpRequest from 'bnpl-shared/src/api/createZlpRequest';
import { MOCK_LOTTE_PARTNER_API } from 'bnpl-shared/src/api/partner_lotte/index';
import { HttpRequestVerbs } from 'bnpl-shared/src/shared/api';
import { MPOnboardingInfo } from 'bnpl-shared/src/types';

const getRealHost = (request_id: string) => () =>
  new AppUrlPathBuilder(`/onboardings/${request_id}`, { version: 'v2', partner: 'lfvn' }).build();
const MOCK_HOST = () => 'https://darling-mustang-moving.ngrok-free.app/lfvn/onboardings';

export const getOnboardingDataApi = (request_id: string) =>
  createZlpRequest<MPOnboardingInfo>(MOCK_LOTTE_PARTNER_API ? MOCK_HOST : getRealHost(request_id), HttpRequestVerbs.GET)
    .setRetryOptions({ retryCount: 3, retryInterval: 1000 })
    .build();

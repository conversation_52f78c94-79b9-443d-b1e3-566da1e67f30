import createZlpRequest from 'bnpl-shared/src/api/createZlpRequest';
import { HttpRequestVerbs } from 'bnpl-shared/src/shared/api';
import { AppUrlPathBuilder } from 'bnpl-shared/src/api/UrlPathBuilder';
import { MOCK_LOTTE_PARTNER_API } from 'bnpl-shared/src/api/partner_lotte/index';
import { ResourceResponse, ResourceType } from 'bnpl-shared/src/types';

const REAL_HOST = () =>
  new AppUrlPathBuilder('/onboardings/users/resources', { version: 'v2', partner: 'lfvn' }).build();
const MOCK_HOST = () => 'https://darling-mustang-moving.ngrok-free.app/lfvn/onboardings/users/resources';

export const getOnboardingResourcesApi = (params: { types: ResourceType[] }) =>
  createZlpRequest<ResourceResponse>(MOCK_LOTTE_PARTNER_API ? MOCK_HOST : REAL_HOST, HttpRequestVerbs.GET)
    .setQueryParams(params)
    .setStringifyOptions({ arrayFormat: 'none' })
    .build();

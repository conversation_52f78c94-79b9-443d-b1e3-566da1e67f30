import { AppUrlPathBuilder } from 'bnpl-shared/src/api/UrlPathBuilder';
import createZlpRequest from 'bnpl-shared/src/api/createZlpRequest';
import { MOCK_LOTTE_PARTNER_API } from 'bnpl-shared/src/api/partner_lotte/index';
import { HttpRequestVerbs } from 'bnpl-shared/src/shared/api';
import { ContentType } from 'bnpl-shared/src/api/requestBuilder';
import { BindingUIData } from 'bnpl-shared/src/types';

const getRealHost = (requestId: string) => () =>
  new AppUrlPathBuilder(`/onboardings/detail-profile/${requestId}`, { version: 'v2', partner: 'lfvn' }).build();
const MOCK_HOST = () => 'https://darling-mustang-moving.ngrok-free.app/lfvn/onboardings/initiate';

export const postSubmitDetailProfileApi = (requestId: string, submitData: BindingUIData) =>
  createZlpRequest<{ request_id: string }>(
    MOCK_LOTTE_PARTNER_API ? MOCK_HOST : getRealHost(requestId),
    HttpRequestVerbs.POST,
  )
    .setDataBody(JSON.stringify(submitData))
    .setHeaders({ 'Content-Type': `${ContentType.JSON}` })
    .build();

import createZlpRequest from 'bnpl-shared/src/api/createZlpRequest';
import { HttpRequestVerbs } from 'bnpl-shared/src/shared/api';
import { AppUrlPathBuilder } from 'bnpl-shared/src/api/UrlPathBuilder';
import { MOCK_LOTTE_PARTNER_API } from 'bnpl-shared/src/api/partner_lotte/index';
import { ContentType } from 'bnpl-shared/src/api/requestBuilder';

const REAL_HOST = () =>
  new AppUrlPathBuilder('/onboardings/users/verify-otp', { version: 'v2', partner: 'lfvn' }).build();
const MOCK_HOST = () => 'https://darling-mustang-moving.ngrok-free.app/lfvn/onboardings/users/verify-otp';

export const postVerifyOTPApi = (params: { code: string; request_id: string }) =>
  createZlpRequest(MOCK_LOTTE_PARTNER_API ? MOCK_HOST : REAL_HOST, HttpRequestVerbs.POST)
    .setHeaders({ 'Content-Type': `${ContentType.JSON}` })
    .setDataBody(JSON.stringify(params))
    .build();

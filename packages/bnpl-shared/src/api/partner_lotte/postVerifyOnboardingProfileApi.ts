import createZlpRequest from 'bnpl-shared/src/api/createZlpRequest';
import { HttpRequestVerbs } from 'bnpl-shared/src/shared/api';
import { AppUrlPathBuilder } from 'bnpl-shared/src/api/UrlPathBuilder';
import { MOCK_LOTTE_PARTNER_API } from 'bnpl-shared/src/api/partner_lotte/index';

const getRealHost = (requestId: string) => () =>
  new AppUrlPathBuilder(`/onboardings/verify-profile/${requestId}`, { version: 'v2', partner: 'lfvn' }).build();
const MOCK_HOST = () => 'https://darling-mustang-moving.ngrok-free.app/lfvn/onboardings/verify-profile';

export const postVerifyOnboardingProfileApi = (requestId: string) =>
  createZlpRequest(MOCK_LOTTE_PARTNER_API ? MOCK_HOST : getRealHost(requestId), HttpRequestVerbs.POST).build();

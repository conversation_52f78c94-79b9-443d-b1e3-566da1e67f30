import createZlpRequest from 'bnpl-shared/src/api/createZlpRequest';
import { withBaseUrl } from 'bnpl-shared/src/api/withBaseUrl';
import { HttpRequestVerbs } from 'bnpl-shared/src/shared/api';
import { ContentType } from 'bnpl-shared/src/api/requestBuilder';
import { RepayOptions, RequestSourceOfFund } from 'bnpl-shared/src/types';

// const MOCK_HOST = () => 'https://darling-mustang-moving.ngrok-free.app/autorepayment/binding/register';
const REAL_HOST = withBaseUrl('/autorepayment/binding/register');

type Params = {
  pin_hash: string;
  list_sof: RequestSourceOfFund[];
  repay_option: RepayOptions;
  binding_version: string;
};

export const postAutoRepaymentRegister = (params: Params) =>
  createZlpRequest<{ status: 'active' | 'inactive'; repay_option: RepayOptions }>(REAL_HOST, HttpRequestVerbs.POST)
    .setDataBody(JSON.stringify(params))
    .setHeaders({ 'Content-Type': `${ContentType.JSON}` })
    .build();

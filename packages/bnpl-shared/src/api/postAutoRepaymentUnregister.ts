import createZlpRequest from 'bnpl-shared/src/api/createZlpRequest';
import { HttpRequestVerbs } from 'bnpl-shared/src/shared/api';
import { ContentType } from 'bnpl-shared/src/api/requestBuilder';
import { withBaseUrl } from 'bnpl-shared/src/api/withBaseUrl';
import { AutoRepaymentBindingStatus } from 'bnpl-shared/src/types';

// const MOCK_HOST = () => 'https://darling-mustang-moving.ngrok-free.app/autorepayment/binding/unregister';
const REAL_HOST = withBaseUrl('/autorepayment/binding/unregister');

export const postAutoRepaymentUnregister = (params: { pin_hash: string }) =>
  createZlpRequest<AutoRepaymentBindingStatus>(REAL_HOST, HttpRequestVerbs.POST)
    .setDataBody(JSON.stringify(params))
    .setHeaders({ 'Content-Type': `${ContentType.JSON}` })
    .build();

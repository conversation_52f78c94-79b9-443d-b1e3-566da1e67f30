import { setOnboardingId } from '../redux/bindingReducer';
import { store } from '../redux/store';
import { HttpRequestVerbs } from '../shared/api';
import { Bod1SubmitStatus, EdgeCaseInfo } from '../types';
import { withBaseUrl } from './withBaseUrl';
import createZlpRequest from 'bnpl-shared/src/api/createZlpRequest';
import { ContentType } from 'bnpl-shared/src/api/requestBuilder';

const REAL_HOST = withBaseUrl('/users/basic-profile');
// const MOCK_HOST = () => 'https://darling-mustang-moving.ngrok-free.app/users/basic-profile';

type BasicProfile = {
  full_name: string;
  phone_number: string;
  id_number: string;
};

type DataResponse = {
  permission: Bod1SubmitStatus;
  request_id: string;
  reject_code: string;
  message: string;
  edge_case_info?: EdgeCaseInfo;
};

export const postBasicInfoApi = (params: BasicProfile) =>
  createZlpRequest<DataResponse>(REAL_HOST, HttpRequestVerbs.POST)
    .setDataBody(JSON.stringify(params))
    .setHeaders({ 'Content-Type': `${ContentType.JSON}` })
    .addResponseInterceptors([
      async ({ response, RequestBuilder }) => {
        if (response.ok) {
          const data = await response.clone().json();
          if (data) {
            store.dispatch(setOnboardingId(data.request_id));
          }
        }
        return { response, RequestBuilder };
      },
    ])
    .build();

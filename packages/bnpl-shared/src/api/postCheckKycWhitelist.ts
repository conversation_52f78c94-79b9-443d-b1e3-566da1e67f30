import { HttpRequestVerbs } from 'bnpl-shared/src/shared/api';
import { getZLPToken } from 'bnpl-shared/src/shared/ZaloPayModules';
import { buildZlpPublicBaseUrl } from 'bnpl-shared/src/api/withBaseUrl';
import RequestBuilder from 'bnpl-shared/src/api/requestBuilder';

const REAL_HOST = `${buildZlpPublicBaseUrl()}/v2/ekyc/update/valid`;
// const MOCK_HOST = 'https://darling-mustang-moving.ngrok-free.app/v2/ekyc/update/valid';

export const postCheckKycWhitelist = async (source: string) => {
  const zlp_token = await getZLPToken();
  return new RequestBuilder<any>(REAL_HOST, HttpRequestVerbs.POST)
    .setHeaders({
      Authorization: `Bearer ${zlp_token}`,
      'Content-Type': 'application/json',
    })
    .setDataBody(JSON.stringify({ source }))
    .build();
};

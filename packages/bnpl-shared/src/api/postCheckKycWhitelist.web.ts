import { HttpRequestVerbs } from 'bnpl-shared/src/shared/api';
import { buildZlpPublicBaseUrl } from 'bnpl-shared/src/api/withBaseUrl';

export const postCheckKycWhitelist = async (source: string) => {
  return await fetch(`${buildZlpPublicBaseUrl()}/v2/ekyc/update/valid`, {
    method: HttpRequestVerbs.POST,
    headers: {
      'Content-Type': 'application/json',
    },
    credentials: 'include',
    mode: 'cors',
    body: JSON.stringify({ source }),
  });
};

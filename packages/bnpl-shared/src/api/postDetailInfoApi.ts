import { withBaseUrl } from './withBaseUrl';
import { HttpRequestVerbs } from 'bnpl-shared/src/shared/api';
import createZlpRequest from 'bnpl-shared/src/api/createZlpRequest';
import { ContentType } from 'bnpl-shared/src/api/requestBuilder';
import { DetailProfileResp, DetailProfileRequest } from 'bnpl-shared/src/types';

const REAL_HOST = withBaseUrl('/users/detail-profile');
// const MOCK_HOST = () => 'https://darling-mustang-moving.ngrok-free.app/users/detail-profile';

export const postDetailInfoApi = (params: DetailProfileRequest) =>
  createZlpRequest<DetailProfileResp>(REAL_HOST, HttpRequestVerbs.POST)
    .setDataBody(JSON.stringify(params))
    .setHeaders({ 'Content-Type': `${ContentType.JSON}` })
    .build();

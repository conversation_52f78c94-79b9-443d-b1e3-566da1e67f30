import { AppUrlPathBuilder } from 'bnpl-shared/src/api/UrlPathBuilder';
import createZlpRequest from 'bnpl-shared/src/api/createZlpRequest';
import { HttpRequestVerbs } from 'bnpl-shared/src/shared/api';
import { ContentType } from 'bnpl-shared/src/api/requestBuilder';
import { InitSignContractResp } from 'bnpl-shared/src/types';

const getRealHost = () =>
  new AppUrlPathBuilder('/onboardings/init-sign-contract', { version: 'v2', partner: 'cimb' }).build();
// const MOCK_HOST = () => 'https://darling-mustang-moving.ngrok-free.app/onboardings/init-sign-contract';

export const postInitOnboardingSignContractApi = (request_id: string) =>
  createZlpRequest<InitSignContractResp>(getRealHost, HttpRequestVerbs.POST)
    .setQueryParams({ request_id })
    .setHeaders({ 'Content-Type': `${ContentType.JSON}` })
    .build();

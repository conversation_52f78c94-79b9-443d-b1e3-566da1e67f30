import createZlpRequest from 'bnpl-shared/src/api/createZlpRequest';
import { HttpRequestVerbs } from 'bnpl-shared/src/shared/api';
import { AppUrlPathBuilder } from 'bnpl-shared/src/api/UrlPathBuilder';

const getHost = (account_id: string) => () =>
  new AppUrlPathBuilder(`/accounts/lock/${account_id}`, { version: 'v2' }).build();
// const MOCK_HOST = () => 'https://darling-mustang-moving.ngrok-free.app/accounts/lock';

export const postLockAccountApi = (account_id: string) =>
  createZlpRequest(getHost(account_id), HttpRequestVerbs.POST).build();

import { HttpRequestVerbs } from 'bnpl-shared/src/shared/api';
import { withBaseUrl } from 'bnpl-shared/src/api/withBaseUrl';
import createZlpRequest from 'bnpl-shared/src/api/createZlpRequest';
import { ContentType, SuccessfulResponse } from 'bnpl-shared/src/api/requestBuilder';

const REAL_HOST = withBaseUrl('/users/binding/reset-due-to-changed-phone');
// const MOCK_HOST = () => 'https://darling-mustang-moving.ngrok-free.app/users/binding/reset';

export const postResetBindingDueChangePhone = (request_id: string) =>
  createZlpRequest<SuccessfulResponse<any>>(REAL_HOST, HttpRequestVerbs.POST)
    .setDataBody(JSON.stringify({ request_id }))
    .setHeaders({ 'Content-Type': `${ContentType.JSON}` })
    .build();

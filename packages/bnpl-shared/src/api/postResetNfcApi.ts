import createZlpRequest from 'bnpl-shared/src/api/createZlpRequest';
import { HttpRequestVerbs } from 'bnpl-shared/src/shared/api';
import { withBaseUrl } from 'bnpl-shared/src/api/withBaseUrl';
import { ContentType } from 'bnpl-shared/src/api/requestBuilder';

const REAL_HOST = withBaseUrl('/users/reset-nfc');
// const MOCK_HOST = () => 'https://darling-mustang-moving.ngrok-free.app/users/reset-nfc';

export const postResetNfcApi = () =>
  createZlpRequest(REAL_HOST, HttpRequestVerbs.POST)
    .setHeaders({ 'Content-Type': `${ContentType.JSON}` })
    .build();

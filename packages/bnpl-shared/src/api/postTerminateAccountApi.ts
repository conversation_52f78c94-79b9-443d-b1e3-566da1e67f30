import createZlpRequest from 'bnpl-shared/src/api/createZlpRequest';
import { HttpRequestVerbs } from 'bnpl-shared/src/shared/api';
import { AppUrlPathBuilder } from 'bnpl-shared/src/api/UrlPathBuilder';
import { ContentType } from 'bnpl-shared/src/api/requestBuilder';

const REAL_HOST = () => new AppUrlPathBuilder('/accounts/terminate', { version: 'v2' }).build();
// const MOCK_HOST = () => 'https://darling-mustang-moving.ngrok-free.app/accounts/terminate';
export const postTerminateAccountApi = (account_id: string) =>
  createZlpRequest(REAL_HOST, HttpRequestVerbs.POST)
    .setHeaders({ 'Content-Type': `${ContentType.JSON}` })
    .setQueryParams({ account_id })
    .build();

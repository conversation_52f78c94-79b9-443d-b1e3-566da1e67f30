import createZlpRequest from 'bnpl-shared/src/api/createZlpRequest';
import { HttpRequestVerbs } from 'bnpl-shared/src/shared/api';
import { AppUrlPathBuilder } from 'bnpl-shared/src/api/UrlPathBuilder';
import { ContentType } from 'bnpl-shared/src/api/requestBuilder';

const getHost = (account_id: string) => () =>
  new AppUrlPathBuilder(`/accounts/unlock/${account_id}`, { version: 'v2' }).build();
// const MOCK_HOST = () => 'https://darling-mustang-moving.ngrok-free.app/accounts/unlock';

export const postUnlockAccountApi = (account_id: string, pin_hash: string) =>
  createZlpRequest(getHost(account_id), HttpRequestVerbs.POST)
    .setDataBody(JSON.stringify({ pin_hash }))
    .setHeaders({ 'Content-Type': `${ContentType.JSON}` })
    .build();

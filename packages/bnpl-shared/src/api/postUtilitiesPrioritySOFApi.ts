import { withBaseUrl } from 'bnpl-shared/src/api/withBaseUrl';
import createZlpRequest from 'bnpl-shared/src/api/createZlpRequest';
import { SuccessfulResponse } from 'bnpl-shared/src/api/requestBuilder';
import { HttpRequestVerbs } from 'bnpl-shared/src/shared/api';

const REAL_HOST = withBaseUrl('/utilities/payment/setup-priority');
// const MOCK_HOST = () => 'https://darling-mustang-moving.ngrok-free.app/utilities/payment/setup-priority';

export const postUtilitiesPrioritySOFApi = () =>
  createZlpRequest<SuccessfulResponse<any>>(REAL_HOST, HttpRequestVerbs.POST).build();

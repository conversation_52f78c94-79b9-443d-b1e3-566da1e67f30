import { AppUrlPathBuilder } from 'bnpl-shared/src/api/UrlPathBuilder';
import createZlpRequest from 'bnpl-shared/src/api/createZlpRequest';
import { HttpRequestVerbs } from 'bnpl-shared/src/shared/api';
import { ContentType } from 'bnpl-shared/src/api/requestBuilder';

const getRealHost = () =>
  new AppUrlPathBuilder('/onboardings/verify-face-challenge', { version: 'v2', partner: 'cimb' }).build();
// const MOCK_HOST = () => 'https://darling-mustang-moving.ngrok-free.app/onboardings/verify-face-challenge';

export const postVerifyFaceChallengeApi = (params: { um_request_id: string; request_id: string }) =>
  createZlpRequest(getRealHost, HttpRequestVerbs.POST)
    .setDataBody(JSON.stringify(params))
    .setHeaders({ 'Content-Type': `${ContentType.JSON}` })
    .build();

import createZlpRequest from 'bnpl-shared/src/api/createZlpRequest';
import { VerifyProfile } from 'bnpl-shared/src/types';
import { withBaseUrl } from 'bnpl-shared/src/api/withBaseUrl';
import { HttpRequestVerbs } from 'bnpl-shared/src/shared/api';

// const MOCK_HOST = () => 'https://darling-mustang-moving.ngrok-free.app/users/verify-profile';
const REAL_HOST = withBaseUrl('/users/verify-profile');

export const postVerifyProfile = () => createZlpRequest<VerifyProfile>(REAL_HOST, HttpRequestVerbs.POST).build();

import { withBaseUrl } from 'bnpl-shared/src/api/withBaseUrl';
import createZlpRequest from 'bnpl-shared/src/api/createZlpRequest';
import { RenewContractResp } from 'bnpl-shared/src/types';
import { HttpRequestVerbs } from 'bnpl-shared/src/shared/api';
import { MOCK_RENEW_API } from 'bnpl-shared/src/api/renew_overdraft/index';

const REAL_HOST = withBaseUrl('/renewal/contract');
const MOCK_HOST = () => 'https://darling-mustang-moving.ngrok-free.app/renewal/contract';

export const getRenewContractApi = (appendix_id: number) =>
  createZlpRequest<RenewContractResp>(MOCK_RENEW_API ? MOCK_HOST : REAL_HOST, HttpRequestVerbs.GET)
    .setQueryParams({ appendix_id })
    .build();

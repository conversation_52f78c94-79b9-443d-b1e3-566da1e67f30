import { withBaseUrl } from 'bnpl-shared/src/api/withBaseUrl';
import createZlpRequest from 'bnpl-shared/src/api/createZlpRequest';
import { RenewStatusResp } from 'bnpl-shared/src/types';
import { HttpRequestVerbs } from 'bnpl-shared/src/shared/api';
import { MOCK_RENEW_API } from 'bnpl-shared/src/api/renew_overdraft/index';

const REAL_HOST = withBaseUrl('/renewal/status');
const MOCK_HOST = () => 'https://darling-mustang-moving.ngrok-free.app/renewal/status';

export const getRenewOverDraftStatusApi = () =>
  createZlpRequest<RenewStatusResp>(MOCK_RENEW_API ? MOCK_HOST : REAL_HOST, HttpRequestVerbs.GET).build();

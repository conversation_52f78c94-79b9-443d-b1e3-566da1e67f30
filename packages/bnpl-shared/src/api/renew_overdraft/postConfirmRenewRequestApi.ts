import { withBaseUrl } from 'bnpl-shared/src/api/withBaseUrl';
import { HttpRequestVerbs } from 'bnpl-shared/src/shared/api';
import { MOCK_RENEW_API } from 'bnpl-shared/src/api/renew_overdraft/index';
import { EdgeCaseInfo } from 'bnpl-shared/src/types';
import RequestBuilder, { ContentType, SuccessfulResponse } from 'bnpl-shared/src/api/requestBuilder';
import {
  defaultHeaderInterceptor,
  maintenanceInterceptor,
  zlpAuthorizeInterceptor,
} from 'bnpl-shared/src/api/interceptors';

const REAL_HOST = withBaseUrl('/renewal/confirm-renew-appendix');
const MOCK_HOST = () => 'https://darling-mustang-moving.ngrok-free.app/renewal/confirm-renew-appendix';

export const postConfirmRenewRequestApi = (appendix_id: number) =>
  new RequestBuilder<SuccessfulResponse<EdgeCaseInfo>>(
    MOCK_RENEW_API ? MOCK_HOST() : REAL_HOST(),
    HttpRequestVerbs.POST,
  )
    .addRequestInterceptors([defaultHeaderInterceptor, zlpAuthorizeInterceptor])
    .addResponseInterceptors([maintenanceInterceptor])
    .setDataBody(JSON.stringify({ appendix_id }))
    .setHeaders({ 'Content-Type': `${ContentType.JSON}` })
    .build();

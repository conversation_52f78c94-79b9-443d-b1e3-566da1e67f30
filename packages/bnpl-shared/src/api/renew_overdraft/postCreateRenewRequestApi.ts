import { withBaseUrl } from 'bnpl-shared/src/api/withBaseUrl';
import { CreateRenewResp } from 'bnpl-shared/src/types';
import { HttpRequestVerbs } from 'bnpl-shared/src/shared/api';
import { MOCK_RENEW_API } from 'bnpl-shared/src/api/renew_overdraft/index';
import RequestBuilder, { ContentType, SuccessfulResponse } from 'bnpl-shared/src/api/requestBuilder';
import {
  defaultHeaderInterceptor,
  maintenanceInterceptor,
  zlpAuthorizeInterceptor,
} from 'bnpl-shared/src/api/interceptors';

const REAL_HOST = withBaseUrl('/renewal/create');
const MOCK_HOST = () => 'https://darling-mustang-moving.ngrok-free.app/renewal/create';

export const postCreateRenewRequestApi = () =>
  new RequestBuilder<SuccessfulResponse<CreateRenewResp>>(
    MOCK_RENEW_API ? MOCK_HOST() : REAL_HOST(),
    HttpRequestVerbs.POST,
  )
    .addRequestInterceptors([defaultHeaderInterceptor, zlpAuthorizeInterceptor])
    .addResponseInterceptors([maintenanceInterceptor])
    .setHeaders({ 'Content-Type': `${ContentType.JSON}` })
    .build();

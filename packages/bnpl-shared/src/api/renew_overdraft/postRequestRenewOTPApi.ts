import { withBaseUrl } from 'bnpl-shared/src/api/withBaseUrl';
import createZlpRequest from 'bnpl-shared/src/api/createZlpRequest';
import { RequestRenewOTPResp } from 'bnpl-shared/src/types';
import { HttpRequestVerbs } from 'bnpl-shared/src/shared/api';
import { MOCK_RENEW_API } from 'bnpl-shared/src/api/renew_overdraft/index';
import { ContentType } from 'bnpl-shared/src/api/requestBuilder';

const REAL_HOST = withBaseUrl('/renewal/request-otp');
const MOCK_HOST = () => 'https://darling-mustang-moving.ngrok-free.app/renewal/request-otp';

export const postRequestRenewOTPApi = (appendix_id: number) =>
  createZlpRequest<RequestRenewOTPResp>(MOCK_RENEW_API ? MOCK_HOST : REAL_HOST, HttpRequestVerbs.POST)
    .setDataBody(JSON.stringify({ appendix_id }))
    .setHeaders({ 'Content-Type': `${ContentType.JSON}` })
    .build();

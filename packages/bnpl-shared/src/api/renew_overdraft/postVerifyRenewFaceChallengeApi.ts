import { AppUrlPathBuilder } from 'bnpl-shared/src/api/UrlPathBuilder';
import createZlpRequest from 'bnpl-shared/src/api/createZlpRequest';
import { HttpRequestVerbs } from 'bnpl-shared/src/shared/api';
import { MOCK_RENEW_API } from 'bnpl-shared/src/api/renew_overdraft/index';
import { ContentType } from 'bnpl-shared/src/api/requestBuilder';

const getRealHost = () =>
  new AppUrlPathBuilder('/renewal/verify-face-challenge', { version: 'v2', partner: 'cimb' }).build();
const MOCK_HOST = () => 'https://darling-mustang-moving.ngrok-free.app/renewal/verify-face-challenge';

export const postVerifyRenewFaceChallengeApi = (appendix_id: number, um_request_id: string) =>
  createZlpRequest(MOCK_RENEW_API ? MOCK_HOST : getRealHost, HttpRequestVerbs.POST)
    .setDataBody(JSON.stringify({ request_id: appendix_id, um_request_id }))
    .setHeaders({ 'Content-Type': `${ContentType.JSON}` })
    .build();

import { withBaseUrl } from 'bnpl-shared/src/api/withBaseUrl';
import createZlpRequest from 'bnpl-shared/src/api/createZlpRequest';
import { HttpRequestVerbs } from 'bnpl-shared/src/shared/api';
import { MOCK_RENEW_API } from 'bnpl-shared/src/api/renew_overdraft/index';
import { ContentType } from 'bnpl-shared/src/api/requestBuilder';

const REAL_HOST = withBaseUrl('/renewal/verify-otp');
const MOCK_HOST = () => 'https://darling-mustang-moving.ngrok-free.app/renewal/verify-otp';

export const postVerifyRenewOTPApi = (params: { appendix_id: number; code: string }) =>
  createZlpRequest(MOCK_RENEW_API ? MOCK_HOST : REAL_HOST, HttpRequestVerbs.POST)
    .setDataBody(JSON.stringify(params))
    .setHeaders({ 'Content-Type': `${ContentType.JSON}` })
    .build();

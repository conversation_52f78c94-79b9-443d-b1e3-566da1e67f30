import requestBuilder, { ContentType } from 'bnpl-shared/src/api/requestBuilder';
import RequestBuilder from 'bnpl-shared/src/api/requestBuilder';
import { HttpRequestVerbs } from 'bnpl-shared/src/shared/api';
import { waitFor } from '@testing-library/react-native';
import { mockFetch, SUCCESS_RESPONSE } from 'bnpl-shared/src/jest/fakeData';

describe(requestBuilder.name, () => {
  it('verify builder GET request constructed as expected', async () => {
    mockFetch(SUCCESS_RESPONSE);
    await new RequestBuilder('https://domain.com/fetch', HttpRequestVerbs.GET)
      .setHeaders({ 'Content-Type': `${ContentType.JSON}` })
      .setQueryParams({ version: 1 })
      .build();
    await waitFor(() =>
      expect(global.fetch as jest.Mock).toHaveBeenCalledWith('https://domain.com/fetch?version=1', {
        method: 'GET',
        headers: { 'Content-Type': 'application/json' },
        credentials: 'include',
        mode: 'cors',
        body: undefined,
      }),
    );
  });
  it('verify builder POST request constructed as expected', async () => {
    mockFetch(SUCCESS_RESPONSE);
    await new RequestBuilder('https://domain.com/fetch', HttpRequestVerbs.POST)
      .setHeaders({ 'Content-Type': `${ContentType.JSON}` })
      .setDataBody(JSON.stringify({ body: 'text' }))
      .build();
    await waitFor(() =>
      expect(global.fetch as jest.Mock).toHaveBeenCalledWith('https://domain.com/fetch', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        credentials: 'include',
        mode: 'cors',
        body: '{"body":"text"}',
      }),
    );
  });
});

/* eslint-disable no-undef */
import { HttpRequestVerbs } from 'bnpl-shared/src/shared/api/types';
import qs from 'query-string';
import { sleep } from 'bnpl-shared/src/shared/utils/sleep';

//#region
export type SuccessfulResponse<T> = {
  code: number;
  message: string;
  data: T;
};

export enum ContentType {
  JSON = 'application/json',
  FORM_DATA = 'multipart/form-data',
}
type RequestInterceptor = (context: RequestInit) => RequestInit | Promise<RequestInit>;
type ResponseInterceptor = ({
  response,
  RequestBuilder,
}: {
  response: Response;
  RequestBuilder: RequestBuilder<ResponseType>;
}) => Promise<{ response: Response; RequestBuilder: RequestBuilder<ResponseType> }> | Promise<Response>;

class RequestBuilder<ResponseType> {
  private requestInterceptors?: RequestInterceptor[];
  private responseInterceptors?: ResponseInterceptor[];
  private url: string;
  private readonly requestMethod: HttpRequestVerbs;
  private dataBody?: any | FormData;
  private queryParams?: any;
  private headers?: { [key: string]: string };
  private credentials?: RequestCredentials = 'include';
  private parseJson: boolean = true;
  private stringifyOptions: qs.StringifyOptions = { arrayFormat: 'bracket' };
  private retryOptions?: { retryCount: number; retryInterval: number };

  constructor(url: string, requestMethod: HttpRequestVerbs) {
    this.url = url;
    this.requestMethod = requestMethod;
  }

  setStringifyOptions(options: qs.StringifyOptions) {
    this.stringifyOptions = options;
    return this;
  }

  addRequestInterceptors(interceptors: RequestInterceptor[]) {
    if (!this.requestInterceptors) {
      this.requestInterceptors = [];
    }
    this.requestInterceptors = this.requestInterceptors.concat(interceptors);
    return this;
  }

  addResponseInterceptors(interceptors: ResponseInterceptor[]) {
    if (!this.responseInterceptors) {
      this.responseInterceptors = [];
    }
    this.responseInterceptors = this.responseInterceptors?.concat(interceptors);
    return this;
  }

  setDataBody(data: any) {
    this.dataBody = data;
    return this;
  }

  setRetryOptions(options?: { retryCount: number; retryInterval: number }) {
    this.retryOptions = options;
    return this;
  }

  setQueryParams(params: any) {
    this.queryParams = params;
    return this;
  }

  setCredentials(credentials: RequestCredentials) {
    this.credentials = credentials;
    return this;
  }

  setHeaders(headers: { [key: string]: string }) {
    this.headers = headers;
    return this;
  }

  setParseJson(parseJson: boolean) {
    this.parseJson = parseJson;
    return this;
  }

  async build(): Promise<ResponseType> {
    const responseInterceptorChain = flow(this.responseInterceptors || []);
    let fetchResponse;
    for (let i = 0; i < (this.retryOptions?.retryCount || 1); i++) {
      await sleep(this.retryOptions?.retryInterval || 1000 * i + 1);
      fetchResponse = await this.onFetch();
      if (fetchResponse.ok) {
        break;
      }
    }
    const { response } = await responseInterceptorChain({ response: fetchResponse, RequestBuilder: this });
    if (response.ok) {
      if (this.parseJson) {
        return response.json() as ResponseType;
      } else {
        return response;
      }
    } else {
      if (this.parseJson) {
        let errorDetail;
        try {
          errorDetail = await response.json();
        } catch (e: any) {
          errorDetail = { message: 'Có lỗi xảy ra, vui lòng thử sau' };
        }
        return Promise.reject({ ...errorDetail, http_code: response.status });
      } else {
        return Promise.reject(response);
      }
    }
  }

  async onFetch() {
    if (this.queryParams) {
      this.url = qs.stringifyUrl({ url: this.url, query: this.queryParams }, this.stringifyOptions);
    }

    if (this.dataBody && this.headers && !this.headers['Content-Type']) {
      console.warn(
        'Look like request dataBody is set without Content-Type header, this may cause unexpected behavior on the BE side.' +
          ' Please set Content-Type header for this request',
      );
    }

    const requestInterceptorChain = flow(this.requestInterceptors || []);
    const requestMethod = this.requestMethod;
    let headers = { ...this.headers };
    const options = await requestInterceptorChain({
      method: requestMethod,
      headers,
      credentials: this.credentials,
      mode: 'cors',
      body: this.dataBody,
    });
    return fetch(this.url, options);
  }
}

/**
 * Feed `value` to the `fns[0]`, get the result and then feed to `fns[1]`,... until all functions are executed.
 */
const flow = (fns: any[]) => {
  return async (value: any) => {
    let result = value;
    for (let i = 0; i < fns.length; i++) {
      result = await fns[i](result);
    }
    return result;
  };
};

export default RequestBuilder;

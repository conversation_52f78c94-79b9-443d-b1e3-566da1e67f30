import { withBaseUrl } from './withBaseUrl';
import { HttpRequestVerbs } from 'bnpl-shared/src/shared/api';
import createZlpRequest from 'bnpl-shared/src/api/createZlpRequest';
import { ContentType } from 'bnpl-shared/src/api/requestBuilder';

type Response = {
  expired_time: number;
  resend_time: number;
};

type Params = {
  request_id: string;
  otp_type: 'CONTRACT_SIGNING' | 'LINK_TYPE_3';
};

// const MOCK_HOST = () => 'https://darling-mustang-moving.ngrok-free.app/users/request-otp';
const REAL_HOST = withBaseUrl('/users/request-otp');

export const requestOTPApi = (params: Params) =>
  createZlpRequest<Response>(REAL_HOST, HttpRequestVerbs.POST)
    .setDataBody(JSON.stringify(params))
    .setHeaders({ 'Content-Type': `${ContentType.JSON}` })
    .build();

import { withBaseUrl } from './withBaseUrl';
import { HttpRequestVerbs } from 'bnpl-shared/src/shared/api';
import createZlpRequest from 'bnpl-shared/src/api/createZlpRequest';
import { ContentType } from 'bnpl-shared/src/api/requestBuilder';
import { SubmitRepayRequest, SubmitSuccess } from 'bnpl-shared/src/types';

const REAL_HOST = withBaseUrl('/orders/repayment');
// const MOCK_HOST = () => 'https://darling-mustang-moving.ngrok-free.app/orders/repayment';

export const submitRepayOrderApi = (params: SubmitRepayRequest) =>
  createZlpRequest<SubmitSuccess>(REAL_HOST, HttpRequestVerbs.POST)
    .setDataBody(JSON.stringify(params))
    .setHeaders({ 'Content-Type': `${ContentType.JSON}` })
    .build();

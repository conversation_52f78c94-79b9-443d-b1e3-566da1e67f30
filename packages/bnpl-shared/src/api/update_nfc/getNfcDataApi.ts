import createZlpRequest from 'bnpl-shared/src/api/createZlpRequest';
import { HttpRequestVerbs } from 'bnpl-shared/src/shared/api';
import { GetNfcDataResponse } from 'bnpl-shared/src/types';
import { AppUrlPathBuilder } from 'bnpl-shared/src/api/UrlPathBuilder';

const REAL_HOST = () => new AppUrlPathBuilder('/accounts/nfc-data', { version: 'v2' }).build();

// const MOCK_HOST = () => 'https://darling-mustang-moving.ngrok-free.app/users/nfc-data';

export const getNfcDataApi = () => createZlpRequest<GetNfcDataResponse>(REAL_HOST, HttpRequestVerbs.GET).build();

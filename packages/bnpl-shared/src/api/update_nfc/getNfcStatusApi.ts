import createZlpRequest from 'bnpl-shared/src/api/createZlpRequest';
import { HttpRequestVerbs } from 'bnpl-shared/src/shared/api';
import { GetNfcStatusResponse } from 'bnpl-shared/src/types';
import { AppUrlPathBuilder } from 'bnpl-shared/src/api/UrlPathBuilder';

const REAL_HOST = () => new AppUrlPathBuilder('/accounts/nfc-status', { version: 'v2' }).build();

// const MOCK_HOST = () => 'https://darling-mustang-moving.ngrok-free.app/users/nfc-status';

export const getNfcStatusApi = () => createZlpRequest<GetNfcStatusResponse>(REAL_HOST, HttpRequestVerbs.GET).build();

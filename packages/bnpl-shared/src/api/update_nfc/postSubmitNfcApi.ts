import createZlpRequest from 'bnpl-shared/src/api/createZlpRequest';
import { HttpRequestVerbs } from 'bnpl-shared/src/shared/api';
import { SubmitNfcResponse } from 'bnpl-shared/src/types';
import { ContentType } from 'bnpl-shared/src/api/requestBuilder';
import { AppUrlPathBuilder } from 'bnpl-shared/src/api/UrlPathBuilder';

const REAL_HOST = () => new AppUrlPathBuilder('/accounts/submit-nfc', { version: 'v2' }).build();
// const MOCK_HOST = () => 'https://darling-mustang-moving.ngrok-free.app/users/nfc-status';

export const postSubmitNfcApi = (account_id: string) =>
  createZlpRequest<SubmitNfcResponse>(REAL_HOST, HttpRequestVerbs.POST)
    .setHeaders({ 'Content-Type': `${ContentType.JSON}` })
    .setDataBody(JSON.stringify({ account_id }))
    .build();

import { withBaseUrl } from './withBaseUrl';
import { HttpRequestVerbs } from 'bnpl-shared/src/shared/api';
import createZlpRequest from 'bnpl-shared/src/api/createZlpRequest';
import { ContentType } from 'bnpl-shared/src/api/requestBuilder';

type Params = {
  request_id: string;
  otp_type: 'CONTRACT_SIGNING' | 'LINK_TYPE_3';
  code: string;
};

// const MOCK_HOST = () => 'https://darling-mustang-moving.ngrok-free.app/users/verify-otp';
const REAL_HOST = withBaseUrl('/users/verify-otp');

export const verifyOTPApi = (params: Params) =>
  createZlpRequest(REAL_HOST, HttpRequestVerbs.POST)
    .setDataBody(JSON.stringify(params))
    .setHeaders({ 'Content-Type': `${ContentType.JSON}` })
    .build();

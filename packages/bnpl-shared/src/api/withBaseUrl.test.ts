import { Environment, setEnvironment } from '../shared/environment';
import { getSurveyIdWithEnv, getSurveyWrapperResourceUrlWithEnv, withBaseUrl } from './withBaseUrl';

describe(withBaseUrl.name, () => {
  it('returns stgbnpl-gateway.zalopay.vn if env is STAGING', () => {
    setEnvironment(Environment.STAGING);
    expect(withBaseUrl('/home')()).toEqual('https://stgbnpl-gateway.zalopay.vn/api/zpa/v1/home');
  });

  it('returns qcbnpl-gateway.zalopay.vn if env is STAGING', () => {
    setEnvironment(Environment.QC_SANDBOX);
    expect(withBaseUrl('/home')()).toEqual('https://qcbnpl-gateway.zalopay.vn/api/zpa/v1/home');
  });

  it('returns bnpl-gateway.zalopay.vn if env is PRODUCTION', () => {
    setEnvironment(Environment.PRODUCTION);
    expect(withBaseUrl('/home')()).toEqual('https://bnpl-gateway.zalopay.vn/api/zpa/v1/home');
  });

  it('returns correct version path', () => {
    setEnvironment(Environment.QC_SANDBOX);
    expect(withBaseUrl('/home', { version: 'v2' })()).toEqual('https://qcbnpl-gateway.zalopay.vn/api/zpa/v2/home');
  });
});

describe('getSurveyWrapperResourceUrlWithEnv', () => {
  it('returns https://stg-support.zalopay.vn if env is STAGING', () => {
    setEnvironment(Environment.STAGING);
    expect(getSurveyWrapperResourceUrlWithEnv()).toEqual('https://stg-support.zalopay.vn');
  });
  it('returns https://qc-support.zalopay.vn if env is QC_SANDBOX', () => {
    setEnvironment(Environment.QC_SANDBOX);
    expect(getSurveyWrapperResourceUrlWithEnv()).toEqual('https://qc-support.zalopay.vn');
  });
  it('returns https://support.zalopay.vn if env is PRODUCTION', () => {
    setEnvironment(Environment.PRODUCTION);
    expect(getSurveyWrapperResourceUrlWithEnv()).toEqual('https://support.zalopay.vn');
  });
});

describe('getSurveyIdWithEnv', () => {
  it('when env is STAGING', () => {
    setEnvironment(Environment.STAGING);
    expect(getSurveyIdWithEnv('test-survey-id')).toEqual('test-survey-id-stg');
  });
  it('when env is QC_SANDBOX', () => {
    setEnvironment(Environment.QC_SANDBOX);
    expect(getSurveyIdWithEnv('test-survey-id')).toEqual('test-survey-id-qc');
  });
  it('when env is PRODUCTION', () => {
    setEnvironment(Environment.PRODUCTION);
    expect(getSurveyIdWithEnv('test-survey-id')).toEqual('test-survey-id');
  });
});

import { Platform } from 'react-native';
import { Environment, environment } from '../shared/environment';

/**
 * To enforce getting the latest environment.
 * TODO: Refactor to remove this function in function pattern.
 */
export const withBaseUrl = (path: string, options?: { version?: string }) => () =>
  `${buildBaseUrl(undefined, options?.version)}${path}`;

export const getSurveyWrapperResourceUrlWithEnv = (env: Environment = environment) => {
  switch (env) {
    case Environment.STAGING:
      return 'https://stg-support.zalopay.vn';
    case Environment.QC_SANDBOX:
      return 'https://qc-support.zalopay.vn';
    default:
    case Environment.PRODUCTION:
      return 'https://support.zalopay.vn';
  }
};

export const getSurveyIdWithEnv = (surveyId: string, env: Environment = environment) => {
  switch (env) {
    case Environment.STAGING:
      return `${surveyId}-stg`;
    case Environment.DEV_SANDBOX:
    case Environment.QC_SANDBOX:
    case Environment.MC_SANDBOX:
      return `${surveyId}-qc`;
    default:
    case Environment.PRODUCTION:
      return surveyId;
  }
};

export const getAdResourceUrlWithEnv = (env: Environment = environment) => {
  let domain = '';
  switch (env) {
    case Environment.STAGING:
      domain = 'https://stguudai.zalopay.vn/advertising/gateway';
      break;
    case Environment.DEV_SANDBOX:
    case Environment.QC_SANDBOX:
    case Environment.MC_SANDBOX:
      domain = 'https://qcuudai.zalopay.vn/advertising/gateway';
      break;
    case Environment.PRODUCTION:
      domain = 'https://uudai.zalopay.vn/advertising/gateway';
      break;
    default:
      domain = 'https://uudai.zalopay.vn/advertising/gateway';
  }

  return domain;
};

//https://confluence.zalopay.vn/display/ZPPROM/%5BADs%5D+Integrate+Module+Federation%28MF%29+with+Advertising
export const getAdvertisingGatewayApp = (env: Environment = environment) => {
  let domain = '';
  switch (env) {
    case Environment.STAGING:
      domain = 'https://socialstg.zalopay.vn/mfpublic/share-module/micro-app/4ca31e83-9d35-4b7a-9164-042ae342cd94';
      break;
    case Environment.DEV_SANDBOX:
    case Environment.QC_SANDBOX:
    case Environment.MC_SANDBOX:
      domain = 'https://socialdev.zalopay.vn/mfpublic/share-module/micro-app/aa144c16-f1e5-4c40-8383-366e55233187';
      break;
    case Environment.PRODUCTION:
      domain = 'https://sapi.zalopay.vn/mfpublic/share-module/micro-app/bba80482-e108-46f2-bf04-a4ca73d125ab';
      break;
    default:
      domain = 'https://sapi.zalopay.vn/mfpublic/share-module/micro-app/bba80482-e108-46f2-bf04-a4ca73d125ab';
  }

  return domain;
};


//https://confluence.zalopay.vn/pages/viewpage.action?spaceKey=ZTM&title=Highlight+Telco+Packages
export const getTelcoSharedApp = (env: Environment = environment) => {
  let domain = '';
  switch (env) {
    case Environment.STAGING:
      domain = 'https://socialstg.zalopay.vn/mfpublic/share-module/micro-app/fbd34b29-b309-4731-90e7-16f09cc22eee';
      break;
    case Environment.DEV_SANDBOX:
    case Environment.QC_SANDBOX:
    case Environment.MC_SANDBOX:
      domain = 'https://socialdev.zalopay.vn/mfpublic/share-module/micro-app/c664b7d6-f54b-46c1-8b31-c2cde5c2f278';
      break;
    case Environment.PRODUCTION:
      domain = 'https://sapi.zalopay.vn/mfpublic/share-module/micro-app/2acc16bd-ef71-4c42-a877-586af845ea8d';
      break;
    default:
      domain = 'https://sapi.zalopay.vn/mfpublic/share-module/micro-app/2acc16bd-ef71-4c42-a877-586af845ea8d';
  }

  return domain;
};

//#region
export const buildZlpPublicBaseUrl = (env: Environment = environment) => {
  let domain = '';
  switch (env) {
    case Environment.STAGING:
      domain = 'https://socialstg.zalopay.vn';
      break;
    case Environment.DEV_SANDBOX:
    case Environment.QC_SANDBOX:
    case Environment.MC_SANDBOX:
      domain = 'https://socialdev.zalopay.vn';
      break;
    case Environment.PRODUCTION:
      domain = 'https://sapi.zalopay.vn';
      break;
    default:
      domain = 'https://sapi.zalopay.vn';
  }

  return domain;
};
//#endregion

export const getPayLaterDomain = (env: Environment = environment) => {
  switch (env) {
    case Environment.STAGING:
      return 'https://stgbnpl-gateway.zalopay.vn';
    case Environment.DEV_SANDBOX:
    case Environment.QC_SANDBOX:
    case Environment.MC_SANDBOX:
      return 'https://qcbnpl-gateway.zalopay.vn';
    case Environment.PRODUCTION:
      return 'https://bnpl-gateway.zalopay.vn';
    default:
      return 'https://bnpl-gateway.zalopay.vn';
  }
};

//#region
export const buildBaseUrl = (env: Environment = environment, version = 'v1') => {
  return `${getPayLaterDomain(env)}/api/${Platform.OS === 'web' ? 'zpi' : 'zpa'}/${version}`;
};
//#endregion

export const getEnvSuffix = () => {
  let envSuffix = '';
  switch (environment) {
    case Environment.STAGING:
      envSuffix = 'stg';
      break;
    case Environment.QC_SANDBOX:
      envSuffix = 'dev';
      break;
    default:
    case Environment.PRODUCTION:
      envSuffix = '';
      break;
  }
  return envSuffix;
};

export const buildZpiUrl = (env: Environment = environment) => {
  let domain = '';
  switch (env) {
    case Environment.STAGING:
      domain = 'https://socialstg.zalopay.vn';
      break;
    case Environment.DEV_SANDBOX:
    case Environment.QC_SANDBOX:
      domain = 'https://socialdev.zalopay.vn';
      break;
    case Environment.PRODUCTION:
    default:
      domain = 'https://social.zalopay.vn';
  }
  return domain;
};

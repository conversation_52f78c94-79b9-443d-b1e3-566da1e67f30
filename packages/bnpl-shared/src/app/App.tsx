import React, { useEffect } from 'react';
import { ErrorBoundary } from 'bnpl-shared/src/app/ErrorBoundary';
import { Navigator } from 'bnpl-shared/src/app/Navigator';
import { useUtmHandler } from 'bnpl-shared/src/hooks/useUtmHandler';
import { notifyTopMostSubscriber, useOnSwipeToBackIOS } from 'bnpl-shared/src/hooks/useOnSwipeToBackIOS';
import { setDefaultTrackingParams } from 'bnpl-shared/src/utils/tracking/trackEvent';

export const App = (props: any) => {
  const { setUtmCampaign, setUtmSource, setUtmToast, setUtmPartner } = useUtmHandler();

  useOnSwipeToBackIOS(() => {
    //Workaround to make a delay to prevent the call is trigger twice
    setTimeout(notifyTopMostSubscriber, 300);
  });

  useEffect(() => {
    setUtmCampaign(props?.utm_campaign);
    setUtmSource(props?.utm_source);
    setUtmToast(props?.utm_toast);
    setDefaultTrackingParams({ utm_source: props?.utm_source || 'paylater_home' });
    setUtmPartner(props?.partner);
  }, [props, setUtmCampaign, setUtmSource, setUtmToast, setUtmPartner]);

  return (
    <ErrorBoundary>
      <Navigator />
    </ErrorBoundary>
  );
};

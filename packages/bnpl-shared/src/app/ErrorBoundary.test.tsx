import React from 'react';
import { ErrorBoundary } from './ErrorBoundary';
import { useErrorBoundary } from '../shared/utils/useErrorBoundary';
import { renderWithRedux } from 'bnpl-shared/src/jest/renderWithRedux';
const ThrowErrorComp = () => {
  const throwError = useErrorBoundary();
  throwError({ title: 'testing title', message: 'testing message' });
  return null;
};

jest.mock('bnpl-shared/src/shared/sentry', () => ({ captureMessage: jest.fn() }));

describe(ErrorBoundary.name, () => {
  it('renders', () => {
    const { getByTestId } = renderWithRedux(
      <ErrorBoundary>
        <ThrowErrorComp />
      </ErrorBoundary>,
    );
    expect(getByTestId('titleError').children[0]).toEqual('testing title');
    expect(getByTestId('messageError').children[0]).toEqual('testing message');
  });
});

import React, { Component } from 'react';
import { ImageSourcePropType } from 'react-native';
import { ErrorScreen } from 'bnpl-shared/src/screens';

type ErrorType = {
  img?: ImageSourcePropType;
  title?: string;
  message: string;
  btnTitle?: string;
  btnPress?: () => void;
  stack?: string;
};
export class ErrorBoundary extends Component<unknown, { error?: ErrorType; viewErrorDetails: boolean }> {
  constructor(props: unknown) {
    super(props);
    this.state = { viewErrorDetails: false };
    this.toggleErrorDetails = this.toggleErrorDetails.bind(this);
  }

  static getDerivedStateFromError(error: Error) {
    return { error };
  }

  commponentDidCatch(_: Error) {}

  toggleErrorDetails() {
    this.setState({ viewErrorDetails: !this.state.viewErrorDetails });
  }

  render() {
    const { error } = this.state;
    if (error) {
      return <ErrorScreen error={error} />;
    }
    return this.props.children;
  }
}

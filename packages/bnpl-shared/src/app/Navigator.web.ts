import React from 'react';
import { ScreenKey } from '../constants';
import { buildNavigator } from '../shared/nav/buildNavigator';
import { getLocationEnv } from '../shared/ZaloPayModules';
import withSuspense from 'bnpl-shared/src/hocs/withSuspense';

const EmbeddedRenewFaceAuthenScreen = React.lazy(() => import('bnpl-shared/src/features/embedded/screens/RenewFaceAuthenScreen').then(m => ({ default: m.default })));
const EmbeddedRenewSignScreen = React.lazy(() => import('bnpl-shared/src/features/embedded/screens/RenewSignScreen').then(m => ({ default: m.default })));
const EmbeddedRenewalInfoScreen = React.lazy(() => import('bnpl-shared/src/features/embedded/screens/RenewalInfoScreen').then(m => ({ default: m.default })));
const AddInformationScreen = React.lazy(() => import('../screens/onboarding-flow/AddInformationScreen'));
const HomeScreen = React.lazy(() => import('../screens/HomeScreen').then(m => ({ default: m.HomeScreen })));
const HomeUnbindScreen = React.lazy(() => import('../screens/HomeScreen').then(m => ({ default: m.HomeUnbindScreen })));
const LockOutScreen = React.lazy(() => import('../screens/LockOutScreen').then(m => ({ default: m.LockOutScreen })));
const OldVersionLockOutScreen = React.lazy(() =>
  import('../screens/OldVersionLockOutScreen').then(m => ({ default: m.OldVersionLockOutScreen })),
);
const OnboardingScreen = React.lazy(() =>
  import('../screens/onboarding-flow/OnboardingScreen').then(m => ({ default: m.OnboardingScreen })),
);
const SplashScreen = React.lazy(() => import('../screens/SplashScreen'));
const Webview = React.lazy(() => import('../screens/Webview/Webview').then(m => ({ default: m.Webview })));
const ApplicationReview = React.lazy(() => import('../screens/onboarding-flow/ApplicationReview'));
const Faq = React.lazy(() => import('bnpl-shared/src/screens/HelpCenter').then(m => ({ default: m.Faq })));
const ProductInfo = React.lazy(() =>
  import('bnpl-shared/src/screens/HelpCenter').then(m => ({ default: m.ProductInfo })),
);
const PersonalExtraDataScreen = React.lazy(() =>
  import('../screens/onboarding-flow/PersonalExtraDataScreen').then(m => ({ default: m.PersonalExtraDataScreen })),
);

const ResubmitOnboardingScreen = React.lazy(() =>
  import('bnpl-shared/src/screens').then(m => ({ default: m.ResubmitOnboardingScreen })),
);
const RepaymentScreen = React.lazy(() => import('bnpl-shared/src/screens').then(m => ({ default: m.RepaymentScreen })));
const MaintenanceScreen = React.lazy(() =>
  import('bnpl-shared/src/screens').then(m => ({ default: m.MaintenanceScreen })),
);
const UserGuideScreen = React.lazy(() => import('bnpl-shared/src/screens').then(m => ({ default: m.UserGuideScreen })));
const TransactionDetailScreen = React.lazy(() =>
  import('bnpl-shared/src/screens').then(m => ({ default: m.TransactionDetailScreen })),
);
const TransactionHistoryScreen = React.lazy(() =>
  import('bnpl-shared/src/screens').then(m => ({ default: m.TransactionHistoryScreen })),
);
const NotificationScreen = React.lazy(() =>
  import('bnpl-shared/src/screens').then(m => ({ default: m.NotificationScreen })),
);
const StatementDetailScreen = React.lazy(() =>
  import('bnpl-shared/src/screens').then(m => ({ default: m.StatementDetailScreen })),
);
const ErrorScreen = React.lazy(() => import('bnpl-shared/src/screens').then(m => ({ default: m.ErrorScreen })));
const AccountScreen = React.lazy(() => import('bnpl-shared/src/screens').then(m => ({ default: m.AccountScreen })));
const MPAccountScreen = React.lazy(() => import('bnpl-shared/src/screens').then(m => ({ default: m.MPAccountScreen })));
const PaymentHistory = React.lazy(() => import('bnpl-shared/src/screens').then(m => ({ default: m.PaymentHistory })));
const RepaymentHistory = React.lazy(() =>
  import('bnpl-shared/src/screens').then(m => ({ default: m.RepaymentHistory })),
);
const MPRepaymentHistory = React.lazy(() =>
  import('bnpl-shared/src/screens').then(m => ({ default: m.MPRepaymentHistory })),
);
const MPPaymentHistory = React.lazy(() =>
  import('bnpl-shared/src/screens').then(m => ({ default: m.MPPaymentHistory })),
);
const MPTransactionHistoryScreen = React.lazy(() =>
  import('bnpl-shared/src/screens').then(m => ({ default: m.MPTransactionHistoryScreen })),
);
const ReminderSettingScreen = React.lazy(() =>
  import('bnpl-shared/src/screens').then(m => ({ default: m.ReminderSettingScreen })),
);

const RenewFaceChallengeScreen = React.lazy(() =>
  import('bnpl-shared/src/features/renew_overdraft').then(m => ({ default: m.RenewFaceChallengeScreen })),
);
const MainScreen = React.lazy(() => import('bnpl-shared/src/screens/MainScreen'));
const NonWhitelistOnboardingScreen = React.lazy(
  () => import('bnpl-shared/src/features/multipartner_integration/screens/NonWhitelistOnboardingScreen'),
);

const PartnerPickScreen = React.lazy(() =>
  import('bnpl-shared/src/features/multipartner_integration/screens').then(m => ({ default: m.PartnerPickScreen })),
);
const MPSubmitInfoDetailScreen = React.lazy(() =>
  import('bnpl-shared/src/features/multipartner_integration/screens').then(m => ({
    default: m.MPSubmitInfoDetailScreen,
  })),
);
const MPOnboardingScreen = React.lazy(() =>
  import('bnpl-shared/src/features/multipartner_integration/screens').then(m => ({ default: m.MPOnboardingScreen })),
);
const MPVerifyOTPScreen = React.lazy(() =>
  import('bnpl-shared/src/features/multipartner_integration/screens').then(m => ({ default: m.MPVerifyOTPScreen })),
);
const MPHomeScreen = React.lazy(() =>
  import('bnpl-shared/src/features/multipartner_integration/screens').then(m => ({ default: m.MPHomeScreen })),
);
const MPMainScreen = React.lazy(() =>
  import('bnpl-shared/src/features/multipartner_integration/screens').then(m => ({ default: m.MPMainScreen })),
);
const MPRepayScreen = React.lazy(() =>
  import('bnpl-shared/src/features/multipartner_integration/screens').then(m => ({ default: m.MPRepayScreen })),
);
const MPResubmitScreen = React.lazy(() =>
  import('bnpl-shared/src/features/multipartner_integration/screens').then(m => ({ default: m.MPResubmitScreen })),
);
const MPWaitingApprovalScreen = React.lazy(() =>
  import('bnpl-shared/src/features/multipartner_integration/screens').then(m => ({
    default: m.MPWaitingApprovalScreen,
  })),
);
const MiniRepaymentHistoryScreen = React.lazy(() =>
  import('bnpl-shared/src/features/mini_bnpl/screens').then(m => ({
    default: m.MiniRepaymentHistoryScreen,
  })),
);

const MultiPartnerRoutes = {
  [ScreenKey.PartnerPickScreen]: {
    component: withSuspense(PartnerPickScreen),
    title: 'Tài khoản trả sau',
  },
  [ScreenKey.MPSubmitDetailInfoScreen]: {
    component: withSuspense(MPSubmitInfoDetailScreen),
    title: 'Đăng ký thông tin',
  },
  [ScreenKey.MPOnboardingScreen]: {
    component: withSuspense(MPOnboardingScreen),
    header: null,
  },

  [ScreenKey.MPVerifyOTPScreen]: {
    component: withSuspense(MPVerifyOTPScreen),
    title: 'Tài khoản trả sau',
  },
  [ScreenKey.MPHomeScreen]: {
    component: withSuspense(MPHomeScreen as any),
    header: null,
  },
  [ScreenKey.MPMainScreen]: {
    ignoreObserver: true,
    component: withSuspense(MPMainScreen as any),
    title: 'Tài khoản trả sau',
  },
  [ScreenKey.MPRepayScreen]: {
    component: withSuspense(MPRepayScreen),
    header: null,
  },
  [ScreenKey.AccountScreen]: {
    component: withSuspense(MPAccountScreen),
    header: null,
  },
  [ScreenKey.MPResubmitScreen]: {
    component: withSuspense(MPResubmitScreen),
    title: 'Tài khoản trả sau',
  },
  [ScreenKey.MPAccountScreen]: {
    component: withSuspense(MPAccountScreen),
    title: 'Tài khoản trả sau',
  },
  [ScreenKey.MPPaymentHistory]: {
    component: withSuspense(MPPaymentHistory),
    header: null,
  },
  [ScreenKey.MPRepaymentHistory]: {
    component: withSuspense(MPRepaymentHistory),
    header: null,
  },
  [ScreenKey.MPTransactionHistory]: {
    component: withSuspense(MPTransactionHistoryScreen as any),
    ignoreObserver: true,
    title: 'Lịch sử giao dịch',
  },
  [ScreenKey.NonWhitelistOnboardingScreen]: {
    component: withSuspense(NonWhitelistOnboardingScreen),
    header: null,
  },
  [ScreenKey.MPWaitingApproval]: {
    component: withSuspense(MPWaitingApprovalScreen),
    header: null,
  },
  [ScreenKey.MiniRepaymentHistory]: {
    component: withSuspense(MiniRepaymentHistoryScreen),
    header: null,
  },
  [ScreenKey.EmbeddedRenewalInfoScreen]: {
    component: withSuspense(EmbeddedRenewalInfoScreen),
    header: null,
  },
  [ScreenKey.EmbeddedRenewSignScreen]: {
    component: withSuspense(EmbeddedRenewSignScreen),
    header: null,
  },
  [ScreenKey.EmbeddedRenewFaceAuthenScreen]: {
    component: withSuspense(EmbeddedRenewFaceAuthenScreen),
    header: null,
  },
};

export const Navigator = buildNavigator(
  {
    ...MultiPartnerRoutes,
    [ScreenKey.LockOutScreen]: {
      component: withSuspense(LockOutScreen),
      header: null,
    },
    [ScreenKey.OldVersionLockOutScreen]: {
      component: withSuspense(OldVersionLockOutScreen),
      header: null,
    },
    [ScreenKey.SplashScreen]: {
      component: withSuspense(SplashScreen),
      header: null,
    },
    [ScreenKey.AddInformationScreen]: {
      component: withSuspense(AddInformationScreen),
      header: null,
    },
    [ScreenKey.HomeScreen]: {
      component: withSuspense(HomeScreen),
      header: null,
    },
    [ScreenKey.HomeUnbindScreen]: {
      component: withSuspense(HomeUnbindScreen),
      header: null,
    },
    [ScreenKey.OnboardingScreen]: {
      component: withSuspense(OnboardingScreen),
      header: null,
    },
    [ScreenKey.WebviewScreen]: {
      component: withSuspense(Webview),
      header: null,
    },
    [ScreenKey.ApplicationReview]: {
      component: withSuspense(ApplicationReview),
      header: null,
    },
    [ScreenKey.Faq]: {
      component: withSuspense(Faq),
      title: 'Câu hỏi thường gặp',
    },
    [ScreenKey.ProductInfo]: {
      component: withSuspense(ProductInfo),
      title: 'Thông tin sản phẩm',
    },
    [ScreenKey.TransactionHistory]: {
      component: withSuspense(TransactionHistoryScreen as any),
      ignoreObserver: true,
      title: 'Lịch sử giao dịch',
    },
    [ScreenKey.PaymentHistory]: {
      component: withSuspense(PaymentHistory),
      header: null,
    },
    [ScreenKey.RepaymentHistory]: {
      component: withSuspense(RepaymentHistory),
      header: null,
    },
    [ScreenKey.TransactionDetail]: {
      component: withSuspense(TransactionDetailScreen),
      title: 'Chi tiết giao dịch',
    },
    [ScreenKey.UserGuide]: {
      component: withSuspense(UserGuideScreen),
      title: 'Hướng dẫn sử dụng',
    },
    [ScreenKey.PersonalExtraDataScreen]: {
      component: withSuspense(PersonalExtraDataScreen),
      title: 'Thông tin cá nhân',
    },
    [ScreenKey.Maintenance]: {
      component: withSuspense(MaintenanceScreen),
      title: 'Tài khoản trả sau',
    },
    [ScreenKey.Repayment]: {
      component: withSuspense(RepaymentScreen),
      title: 'Tài khoản trả sau',
    },
    [ScreenKey.ResubmitOnboarding]: {
      component: withSuspense(ResubmitOnboardingScreen),
      header: null,
    },
    [ScreenKey.Notification]: {
      component: withSuspense(NotificationScreen),
      title: 'Thông báo',
    },
    [ScreenKey.Error]: {
      component: withSuspense(ErrorScreen),
      header: null,
    },
    [ScreenKey.RenewFaceChallenge]: {
      component: withSuspense(RenewFaceChallengeScreen),
      title: 'Tài khoản trả sau',
    },
    [ScreenKey.MainScreen]: {
      component: withSuspense(MainScreen as any),
      ignoreObserver: true,
      title: 'Tài khoản trả sau',
    },
    [ScreenKey.StatementDetailScreen]: {
      component: withSuspense(StatementDetailScreen),
      title: 'Chi tiết sao kê',
    },
    [ScreenKey.AccountScreen]: {
      component: withSuspense(AccountScreen),
      header: null,
    },
    [ScreenKey.ReminderSettingScreen]: {
      component: withSuspense(ReminderSettingScreen),
      header: null,
      title: 'Cài đặt nhắc hạn',
    },
  },
  {
    default: ScreenKey.SplashScreen,
    zpiBaseUrl: getLocationEnv().zpiBaseUrl,
  },
);

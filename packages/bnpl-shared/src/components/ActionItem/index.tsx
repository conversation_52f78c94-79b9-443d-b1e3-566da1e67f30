import { StyleProp, TouchableOpacity, View, ViewStyle } from 'react-native';
import { AppImage, AppText } from 'bnpl-shared/src/shared/react-native-customized';
import { ChevronIcon } from 'bnpl-shared/src/shared/ChevronIcon';
import React, { FC } from 'react';
import { StyleSheet } from 'bnpl-shared/src/shared/styles/StyleSheet';
import { AppColors } from 'bnpl-shared/src/constants';
import { ImageSource } from 'bnpl-shared/src/shared/types';

const ActionItem: FC<{
  style?: StyleProp<ViewStyle>;
  onPress?: () => void;
  icon: ImageSource;
  title: string;
}> = ({ title, icon, onPress, style }) => {
  return (
    <TouchableOpacity onPress={onPress}>
      <View testID={'action-item'} style={[styles.item, style]}>
        <AppImage width={24} height={24} source={icon} />
        <AppText style={styles.itemText}>{title}</AppText>
        <ChevronIcon direction={'right'} />
      </View>
    </TouchableOpacity>
  );
};

export default ActionItem;

const styles = StyleSheet.create({
  root: {
    backgroundColor: AppColors.background,
    borderRadius: 8,
  },
  itemText: {
    marginHorizontal: 12,
    flex: 1,
  },
  item: {
    height: 48,
    paddingHorizontal: 16,
    paddingVertical: 12,
    flexDirection: 'row',
    alignItems: 'center',
  },
});

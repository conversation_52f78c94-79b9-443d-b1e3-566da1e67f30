import React from 'react';
import { Platform, View } from 'react-native';
import { calculateBannerSizeByTemplate } from 'bnpl-shared/src/components/AdBannerSlider/calculateBannerSizeByTemplate';
import { CarouselSlider, SwiperSlider } from 'bnpl-shared/src/components/Slider';
import { Spacer } from 'bnpl-shared/src/components/Spacer';
import { Skeleton } from 'bnpl-shared/src/shared/animated-wrappers/Skeleton';
import { StyleSheet } from 'bnpl-shared/src/shared/styles/StyleSheet';
import { StyleUtils, windowWidth } from 'bnpl-shared/src/shared/styles/StyleUtils';
import { AdBannerResource } from 'bnpl-shared/src/types';

const ITEM_PADDING = 16;

export const AdSliderUI = ({
  resources,
  onBannerPress,
}: {
  resources?: AdBannerResource;
  onBannerPress?: (url: string) => void;
}) => {
  if (!resources) {
    return (
      <>
        <View testID="loading-banner" style={StyleUtils.flexRow}>
          <Skeleton style={styles.loadingBanner} width={240} height={120} />
          <Skeleton style={styles.loadingBanner} width={240} height={120} />
        </View>
        <Spacer height={12} />
        <View testID="loading-dot" style={[StyleUtils.flexRow, StyleUtils.centered]}>
          <Skeleton style={styles.loadingDot} width={4} height={4} />
          <Skeleton style={styles.loadingDot} width={4} height={4} />
          <Skeleton style={styles.loadingDot} width={4} height={4} />
        </View>
        <Spacer height={12} />
      </>
    );
  }

  if (!resources?.data?.length) {
    return null;
  }

  const itemSize = calculateBannerSizeByTemplate(resources.template, resources.template_info, {
    paddingLeft: ITEM_PADDING,
    paddingRight: ITEM_PADDING,
  });

  const loop = resources?.data?.length > 1 ? true : false;

  return Platform.OS === 'web' ? (
    <SwiperSlider
      autoplay
      loop={loop}
      pagination
      slidesPerView={windowWidth / (itemSize.width + ITEM_PADDING)}
      itemSize={itemSize}
      imageStyle={{ marginStart: ITEM_PADDING, marginEnd: ITEM_PADDING }}
      resources={resources.data}
      onBannerPress={onBannerPress}
    />
  ) : (
    <CarouselSlider
      autoplay
      pagination
      itemPadding={ITEM_PADDING}
      loop={loop}
      itemSize={itemSize}
      resources={resources.data}
      onBannerPress={onBannerPress}
    />
  );
};

const styles = StyleSheet.create({
  loadingBanner: {
    width: 240,
    height: 120,
    borderRadius: 8,
    marginEnd: 16,
  },
  loadingDot: {
    width: 4,
    height: 4,
    borderRadius: 2,
    marginEnd: 4,
  },
  fullBannerImg: {
    borderRadius: 8,
    marginHorizontal: ITEM_PADDING,
  },
  multiBannerImg: {
    marginStart: ITEM_PADDING,
    borderRadius: 8,
  },
  pagingStyle: { bottom: 16 },
});

import { AdBannerTemplate } from 'bnpl-shared/src/types';
import { windowWidth } from 'bnpl-shared/src/shared/styles/StyleUtils';

export const calculateBannerSizeByTemplate = (
  template: string,
  template_info?: { width: number; height: number },
  options?: {
    paddingLeft?: number;
    paddingRight?: number;
    paddingTop?: number;
    paddingBottom?: number;
  },
) => {
  let width = 0;
  let height = 0;

  switch (template) {
    case AdBannerTemplate.PRODUCT_PAGE_FULL_IMAGE_SLIDER:
      width = windowWidth - (options?.paddingLeft || 0) - (options?.paddingRight || 0);
      height = width / 3;
      break;
    case AdBannerTemplate.PRODUCT_PAGE_FULL_IMAGE_SLIDER_41:
      width = windowWidth - (options?.paddingLeft || 0) - (options?.paddingRight || 0);
      height = width / 4;
      break;
    default:
    case AdBannerTemplate.PRODUCT_PAGE_MULTI_IMAGE_SLIDER:
      height = template_info?.height || 100;
      width = height * 2 - (options?.paddingLeft || 0);
      height = width / 2;
      break;
  }
  return { width, height };
};

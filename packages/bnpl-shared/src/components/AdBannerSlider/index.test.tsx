import React from 'react';
import { fireEvent, render, waitFor } from '@testing-library/react-native';
import { AdBannerSlider } from 'bnpl-shared/src/components/AdBannerSlider/index';
import { getAdResourceProductPageApi } from 'bnpl-shared/src/api/getAdResourceProductPageApi';
import { getAdResourceProductPageApiBuilder } from 'bnpl-shared/src/api/__mocks__/getAdResourceProductPageApi';
import { AdsInventoryId } from 'bnpl-shared/src/types';

describe('AdBannerSlider', () => {
  beforeEach(() => {
    (getAdResourceProductPageApi as jest.Mock).mockClear();
  });

  it('should render loading when data is fetching', () => {
    const { queryByTestId } = render(<AdBannerSlider inventoryId={AdsInventoryId.BANNER_SLIDER} />);
    expect(queryByTestId('loading-banner')).toBeTruthy();
    expect(queryByTestId('loading-dot')).toBeTruthy();
  });
  it('should not render when data is empty', async () => {
    (getAdResourceProductPageApi as jest.Mock).mockResolvedValueOnce(getAdResourceProductPageApiBuilder.build('empty'));
    const { queryByTestId } = render(<AdBannerSlider inventoryId={AdsInventoryId.BANNER_SLIDER} />);
    await waitFor(() => expect(queryByTestId('carousel-banner-0')).toBeFalsy());
    expect(queryByTestId('loading-banner')).toBeFalsy();
    expect(queryByTestId('loading-dot')).toBeFalsy();
  });

  describe('multi image banner slider', () => {
    it('should render correctly when data is available', async () => {
      (getAdResourceProductPageApi as jest.Mock).mockResolvedValueOnce(getAdResourceProductPageApiBuilder.build());
      const { queryByTestId } = render(<AdBannerSlider inventoryId={AdsInventoryId.BANNER_SLIDER} />);
      await waitFor(() => expect(queryByTestId('carousel-banner-0')).toBeTruthy());
    });
    it('verify cta is handle as expected', async () => {
      (getAdResourceProductPageApi as jest.Mock).mockResolvedValueOnce(getAdResourceProductPageApiBuilder.build());
      const onBannerPress = jest.fn();
      const { getByTestId, queryByTestId } = render(
        <AdBannerSlider inventoryId={AdsInventoryId.BANNER_SLIDER} onBannerPress={onBannerPress} />,
      );
      await waitFor(() => expect(queryByTestId('carousel-banner-0')).toBeTruthy());
      fireEvent.press(getByTestId('carousel-banner-0'));
      expect(onBannerPress).toBeCalledWith('https://www.google.com');
    });
  });
});

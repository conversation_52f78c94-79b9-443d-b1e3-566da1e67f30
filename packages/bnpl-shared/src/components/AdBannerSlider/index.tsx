import React, { FC, useEffect } from 'react';
import { Platform, View } from 'react-native';
import { AdType, useAdResource } from 'bnpl-shared/src/hooks/useAdResource';
import { Skeleton } from 'bnpl-shared/src/shared/animated-wrappers/Skeleton';
import { StyleSheet } from 'bnpl-shared/src/shared/styles/StyleSheet';
import { StyleUtils, windowWidth } from 'bnpl-shared/src/shared/styles/StyleUtils';
import { Spacer } from 'bnpl-shared/src/components/Spacer';
import { trackingAdBannerClick, trackingAdBannerLoad } from 'bnpl-shared/src/screens/HomeScreen/tracking';
import { CarouselSlider, SwiperSlider } from 'bnpl-shared/src/components/Slider';
import { calculateBannerSizeByTemplate } from 'bnpl-shared/src/components/AdBannerSlider/calculateBannerSizeByTemplate';

const ITEM_PADDING = 16;

export const AdBannerSlider: FC<{ inventoryId: string; onBannerPress?: (url: string) => void; onEmpty?: () => void }> =
  ({ onBannerPress, inventoryId, onEmpty }) => {
    const { resources, fetchResources } = useAdResource({
      inventoryId: inventoryId,
      type: AdType.Banner,
    });

    useEffect(() => {
      if (resources?.data?.length) {
        trackingAdBannerLoad(inventoryId, JSON.stringify(resources.data.map(resource => resource.id)));
      }
    }, [resources]);

    useEffect(() => {
      (async () => {
        await fetchResources();
      })();
    }, []);

    const handleOnBannerPress = (url: string, index: number) => {
      if (resources?.data) {
        trackingAdBannerClick(inventoryId, JSON.stringify(resources.data.map(resource => resource.id)), index);
      }
      onBannerPress?.(url);
    };

    if (!resources) {
      return (
        <>
          <View testID="loading-banner" style={StyleUtils.flexRow}>
            <Skeleton style={styles.loadingBanner} width={240} height={120} />
            <Skeleton style={styles.loadingBanner} width={240} height={120} />
          </View>
          <Spacer height={12} />
          <View testID="loading-dot" style={[StyleUtils.flexRow, StyleUtils.centered]}>
            <Skeleton style={styles.loadingDot} width={4} height={4} />
            <Skeleton style={styles.loadingDot} width={4} height={4} />
            <Skeleton style={styles.loadingDot} width={4} height={4} />
          </View>
          <Spacer height={12} />
        </>
      );
    }

    if (!resources?.data?.length) {
      onEmpty?.();
      return null;
    }

    const itemSize = calculateBannerSizeByTemplate(resources.template, resources.template_info, {
      paddingLeft: ITEM_PADDING,
      paddingRight: ITEM_PADDING,
    });

    return Platform.OS === 'web' ? (
      <SwiperSlider
        autoplay
        loop
        pagination
        slidesPerView={windowWidth / (itemSize.width + ITEM_PADDING)}
        itemSize={itemSize}
        imageStyle={{ marginStart: ITEM_PADDING }}
        resources={resources.data}
        onBannerPress={handleOnBannerPress}
      />
    ) : (
      <CarouselSlider
        autoplay
        pagination
        itemPadding={ITEM_PADDING}
        loop
        itemSize={itemSize}
        resources={resources.data}
        onBannerPress={handleOnBannerPress}
      />
    );
  };

const styles = StyleSheet.create({
  loadingBanner: {
    width: 240,
    height: 120,
    borderRadius: 8,
    marginEnd: 16,
  },
  loadingDot: {
    width: 4,
    height: 4,
    borderRadius: 2,
    marginEnd: 4,
  },
  fullBannerImg: {
    borderRadius: 8,
    marginHorizontal: ITEM_PADDING,
  },
  multiBannerImg: {
    marginStart: ITEM_PADDING,
    borderRadius: 8,
  },
  pagingStyle: { bottom: 16 },
});

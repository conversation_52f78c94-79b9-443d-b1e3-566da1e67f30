import { AdPopupUI } from 'bnpl-shared/src/components/AdPopup/index';
import { renderWithRedux } from 'bnpl-shared/src/jest/renderWithRedux';
import React from 'react';
import { getAdResourcePopupApiBuilder } from 'bnpl-shared/src/api/__mocks__/getAdResourcePopupApi';
import { AdPopup } from 'bnpl-shared/src/types';

describe(AdPopupUI.name, () => {
  it('render as epxected when resources is valid', () => {
    const resource: AdPopup = getAdResourcePopupApiBuilder.build().infos[0].data;
    const { queryByTestId } = renderWithRedux(<AdPopupUI resources={resource} inventoryId="123" />);
    expect(queryByTestId('popup-content')).toBeTruthy();
  });
});

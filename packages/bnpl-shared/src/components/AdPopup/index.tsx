import React, { FC, useEffect, useState } from 'react';
import { AppImage, AppText } from 'bnpl-shared/src/shared/react-native-customized';
import { StyleSheet } from 'bnpl-shared/src/shared/styles/StyleSheet';
import { Image, TouchableOpacity, TouchableWithoutFeedback, View } from 'react-native';
import { StyleUtils } from 'bnpl-shared/src/shared/styles/StyleUtils';
import { images } from 'bnpl-shared/src/res';
import { AppColors } from 'bnpl-shared/src/constants';
import { Spacer } from 'bnpl-shared/src/components/Spacer';
import { trackingAdPopupClick, trackingAdPopupShow } from 'bnpl-shared/src/screens/HomeScreen/tracking';
import { ScaleView } from 'bnpl-shared/src/shared/animated-wrappers/ScaleView';
import { InfoModalService } from 'bnpl-shared/src/services';
import { AdPopup, ZlpPlatform } from 'bnpl-shared/src/types';
import { getAppInfo, launchDeepLink, launchInAppWebView } from 'bnpl-shared/src/shared/ZaloPayModules';
import { isZLPDeeplink } from 'bnpl-shared/src/shared/utils/isZLPDeeplink';

const DEFAULT_WIDTH = 300;

type AdPopupProps = {
  inventoryId: string;
  resources: AdPopup;
  onDismiss?: () => void;
};

export const AdPopupUI: FC<AdPopupProps> = ({ inventoryId, resources, onDismiss }) => {
  const [height, setHeight] = useState(300);
  const [imageVisible, setImageVisible] = useState(false);

  const manuallyClosePopup = () => {
    onDismiss?.();
    InfoModalService.hideModal();
  };

  useEffect(() => {
    fetchAndShowImage();
  }, []);

  const fetchAndShowImage = () => {
    if (resources) {
      Image.getSize(resources.background, (width, height) => {
        const ratio = height / width;
        setHeight(DEFAULT_WIDTH * ratio);
        setTimeout(() => {
          setImageVisible(true);
        }, 100);
        trackingAdPopupShow({ inventoryId, ads_id: resources.popupContentId.toString() });
      });
    }
  };

  const handlePress = async () => {
    manuallyClosePopup();
    if (resources?.mainButton) {
      trackingAdPopupClick({ inventoryId, ads_id: resources?.popupContentId?.toString() || '' });
      const appInfo = await getAppInfo();
      if (appInfo.platform === ZlpPlatform.ZPI) {
        const zpiUrl = resources.mainButton.zpiActionLink;
        window.zlpSdk?.Navigator.navigateTo?.({ url: zpiUrl });
      } else {
        const zpaUrl = resources.mainButton.zpaActionLink;
        if (isZLPDeeplink(zpaUrl)) {
          launchDeepLink(zpaUrl);
        } else {
          launchInAppWebView(zpaUrl);
        }
      }
    }
  };

  return (
    <View testID="popup-content" style={styles.root}>
      <TouchableWithoutFeedback onPress={() => manuallyClosePopup()}>
        <View style={styles.overlay} />
      </TouchableWithoutFeedback>
      <TouchableOpacity onPress={handlePress}>
        <ScaleView visible={imageVisible}>
          <AppImage testID="popup-image" height={height} width={300} source={{ uri: resources?.background || '' }} />
        </ScaleView>
      </TouchableOpacity>
      <Spacer height={16} />
      {imageVisible && (
        <TouchableOpacity onPress={() => manuallyClosePopup()}>
          <View style={StyleUtils.flexRow}>
            <AppImage width={16} height={16} source={images.IconClose} />
            <Spacer width={8} />
            <AppText size={12} color={AppColors.white}>
              ĐÓNG
            </AppText>
          </View>
        </TouchableOpacity>
      )}
    </View>
  );
};

const styles = StyleSheet.create({
  root: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  overlay: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    backgroundColor: 'rgba(0,0,0,0.7)',
  },
});

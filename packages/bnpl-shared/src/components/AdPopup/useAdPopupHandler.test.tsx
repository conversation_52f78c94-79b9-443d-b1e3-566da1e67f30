import { renderHookWithRedux } from 'bnpl-shared/src/jest/renderWithRedux';
import { waitFor } from '@testing-library/react-native';
import * as spyRemoteConfigHook from 'bnpl-shared/src/remote_configs/useRemoteConfigs';
import { useAdPopupHandler } from 'bnpl-shared/src/components/AdPopup/useAdPopupHandler';
import { InfoModalService } from 'bnpl-shared/src/services';
import { AdsInventoryId } from 'bnpl-shared/src/types';
import { getAdResourcePopupApiBuilder } from 'bnpl-shared/src/api/__mocks__/getAdResourcePopupApi';

const DEFAULT_CONFIG = {
  enable: true,
  hidden_time_in_hour: 1,
};

const mockValidateSavedConfigTimeExceed = jest.fn();
const mockSaveConfigTime = jest.fn();

jest.mock('bnpl-shared/src/utils/configTimeValidator', () => ({
  configTimeValidator: () => ({
    validateSavedConfigTimeExceed: mockValidateSavedConfigTimeExceed,
    saveConfigTime: mockSaveConfigTime,
  }),
}));

const mockFetchResources = jest.fn();

jest.mock('bnpl-shared/src/hooks/useAdResource', () => {
  const originalModule = jest.requireActual('bnpl-shared/src/hooks/useAdResource');
  return {
    __esModule: true,
    ...originalModule,
    useAdResource: () => ({
      fetchResources: mockFetchResources,
    }),
  };
});

const mockGetConfig = jest.fn();

jest.spyOn(spyRemoteConfigHook, 'useRemoteConfigs').mockReturnValue({
  fetchConfigs: jest.fn(),
  getConfig: mockGetConfig,
  getConfigWithType: jest.fn(),
});

const mockShowModal = jest.fn();
const mockHideModal = jest.fn();

describe('useAdPopupHandler', () => {
  beforeAll(() => {
    InfoModalService.showModal = mockShowModal;
    InfoModalService.hideModal = mockHideModal;
  });

  beforeEach(() => {
    InfoModalService.hideModal();
    mockValidateSavedConfigTimeExceed.mockClear();
    mockShowModal.mockClear();
    mockGetConfig.mockClear();
  });

  it('not show ad popup when enable is false', () => {
    mockGetConfig.mockReturnValue({ enable: false });
    const { checkAndShowAdPopup } = renderHookWithRedux(() => useAdPopupHandler(AdsInventoryId.POPUP_HOME)).result
      .current;
    checkAndShowAdPopup();
    expect(mockShowModal).not.toHaveBeenCalled();
  });

  it('show popup when hidden time in config is not defined', async () => {
    mockFetchResources.mockResolvedValue(getAdResourcePopupApiBuilder.build().infos[0].data);
    mockGetConfig.mockReturnValue({ enable: true });
    const { checkAndShowAdPopup } = renderHookWithRedux(() => useAdPopupHandler(AdsInventoryId.POPUP_HOME)).result
      .current;
    checkAndShowAdPopup();
    await waitFor(() => expect(mockShowModal).toHaveBeenCalled());
  });

  it('show popup when hidden time is exceed', async () => {
    mockFetchResources.mockResolvedValue(getAdResourcePopupApiBuilder.build().infos[0].data);
    mockValidateSavedConfigTimeExceed.mockReturnValue(true);
    mockGetConfig.mockReturnValue(DEFAULT_CONFIG);
    const { checkAndShowAdPopup } = renderHookWithRedux(() => useAdPopupHandler(AdsInventoryId.POPUP_HOME)).result
      .current;
    checkAndShowAdPopup();
    await waitFor(() => expect(mockShowModal).toHaveBeenCalled());
  });

  it('NOT show popup when hidden time is NOT exceed', async () => {
    mockValidateSavedConfigTimeExceed.mockReturnValue(false);
    mockGetConfig.mockReturnValue(DEFAULT_CONFIG);
    const { checkAndShowAdPopup } = renderHookWithRedux(() => useAdPopupHandler(AdsInventoryId.POPUP_HOME)).result
      .current;
    checkAndShowAdPopup();
    await waitFor(() => expect(mockShowModal).not.toHaveBeenCalled());
  });

  it('NOT show popup when popup resource is null', async () => {
    mockFetchResources.mockResolvedValue(null);
    mockValidateSavedConfigTimeExceed.mockReturnValue(true);
    mockGetConfig.mockReturnValue(DEFAULT_CONFIG);
    const { checkAndShowAdPopup } = renderHookWithRedux(() => useAdPopupHandler(AdsInventoryId.POPUP_HOME)).result
      .current;
    checkAndShowAdPopup();
    await waitFor(() => expect(mockShowModal).not.toHaveBeenCalled());
  });
});

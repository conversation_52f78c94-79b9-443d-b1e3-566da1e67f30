import get from 'lodash/get';
import { useRemoteConfigs } from 'bnpl-shared/src/remote_configs/useRemoteConfigs';
import { InfoModalService, ModalType } from 'bnpl-shared/src/services';
import { AdPopupUI } from 'bnpl-shared/src/components/AdPopup/index';
import { AdsInventoryId, TimeUnit } from 'bnpl-shared/src/types';
import React from 'react';
import { ConfigKey } from 'bnpl-shared/src/constants';
import { configTimeValidator } from 'bnpl-shared/src/utils/configTimeValidator';
import { AdType, useAdResource } from 'bnpl-shared/src/hooks/useAdResource';

export const useAdPopupHandler = (inventoryId: string) => {
  const { fetchResources } = useAdResource({ inventoryId, type: AdType.Popup });
  const { getConfig } = useRemoteConfigs();
  const { saveConfigTime, validateSavedConfigTimeExceed } = configTimeValidator(ConfigKey.ADS_POPUP_TIMESTAMP);
  const adsPopupConfig = getConfig('ads_popup');
  const hiddenTimeInHour = get(adsPopupConfig, 'hidden_time_in_hour');
  const enable = get(adsPopupConfig, 'enable');

  const checkShouldShowAdPopup = async (): Promise<boolean> => {
    if (enable) {
      if (hiddenTimeInHour) {
        return await validateSavedConfigTimeExceed({
          diffTime: hiddenTimeInHour,
          timeUnit: TimeUnit.HOURS,
        });
      } else {
        //when hiddenTime is not provided, show popup immediately
        return true;
      }
    }
    return false;
  };

  const checkAndShowAdPopup = () => {
    (async () => {
      if (await checkShouldShowAdPopup()) {
        const popupResource = await fetchResources();
        if (popupResource) {
          InfoModalService.showModal({
            screen: (
              <AdPopupUI
                resources={popupResource}
                onDismiss={() => {
                  saveConfigTime();
                }}
                inventoryId={AdsInventoryId.POPUP_HOME}
              />
            ),
            type: ModalType.MODAL,
            options: {
              transparent: true,
            },
          });
        }
      }
    })();
  };

  return { checkAndShowAdPopup };
};

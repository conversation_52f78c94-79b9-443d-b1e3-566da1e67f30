import { ErrorBoundary } from 'bnpl-shared/src/app/ErrorBoundary';
import { Skeleton } from 'bnpl-shared/src/shared/animated-wrappers/Skeleton';
import { StyleSheet } from 'bnpl-shared/src/shared/styles/StyleSheet';
import { loadRemote } from 'bnpl-shared/src/utils/loadRemote';
import React, { createElement, Suspense, useEffect, useState } from 'react';
import { View } from 'react-native';
import { getAdvertisingGatewayAppURL } from './getAdvertisingGatewayAppURL';
import { AdvertisingProductPageSliderProps } from './types';

function AdvertisingProductPageSlider({
  onLayout,
  ...props
}: AdvertisingProductPageSliderProps) {
  const [MFComponent, setMFComponent] = useState<React.ComponentType<any> | null>(null);
  const [isEmpty, setIsEmpty] = useState(false);

  useEffect(() => {
    const fetchRemoteComponent = async () => {
      try {
        const remoteModule = await loadRemote(
          getAdvertisingGatewayAppURL(), // Remote URL
          'advertising_gateway_app', // Remote container name
          './ProductPageSlider', // Module name (remote component)
        );

        // Assuming RecommendView is a default export
        const Component = remoteModule.default;

        setMFComponent(() => Component); // Set the remote component for rendering
      } catch (error) {
        setIsEmpty?.(true);
        console.error('Error loading remote component:', error);
      }
    };
    fetchRemoteComponent();
  }, []);

  if (isEmpty) {
    return null;
  }

  return (
    <Suspense fallback={() => <Skeleton width={120} height={110} />}>
      <ErrorBoundary>
        <View onLayout={onLayout} style={styles.root}>
          {MFComponent ? createElement(() => <MFComponent {...props} />, {}) : null}
        </View>
      </ErrorBoundary>
    </Suspense>
  );
}

export default AdvertisingProductPageSlider;

const styles = StyleSheet.create({
  root: {
    paddingHorizontal: 16,
  },
});

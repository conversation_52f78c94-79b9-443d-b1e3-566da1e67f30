import React, { FC, useEffect } from 'react';
import { TextInput as RNTextInput, TouchableOpacity, View } from 'react-native';
import { DebtRepayMetadata, DebtRepayOptions, DebtRepaySelectionId, DebtRepayType } from 'bnpl-shared/src/types';
import { SuggestButton } from 'bnpl-shared/src/components/SuggestButton';
import { Event, publishTracking } from 'bnpl-shared/src/utils/tracking';
import { calculateScaleDimension, StyleSheet } from 'bnpl-shared/src/shared/styles/StyleSheet';
import { AppColors } from 'bnpl-shared/src/constants';
import { StyleUtils } from 'bnpl-shared/src/shared/styles/StyleUtils';
import { AppImage, AppText, AppTextInput } from 'bnpl-shared/src/shared/react-native-customized';
import { Spacer } from 'bnpl-shared/src/components/Spacer';
import { handleChangeImmediateText } from 'bnpl-shared/src/components/AmountInput/helper';
import { images } from 'bnpl-shared/src/res';
import { LinkButton } from 'bnpl-shared/src/shared/LinkButton';
import { Skeleton } from 'bnpl-shared/src/shared/animated-wrappers/Skeleton';
import { useDebtObligation } from 'bnpl-shared/src/components/AmountInput/useDebtObligation';
import { useDispatch } from 'react-redux';
import { setActiveInput } from 'bnpl-shared/src/redux/debtObligationReducer';

type AdaptiveInput = {
  repayInput: DebtRepayOptions<DebtRepayMetadata>;
  isActive: boolean;
  onPress?: (repayInput: DebtRepayOptions<DebtRepayMetadata>) => void;
  inputError?: string;
  value?: string;
  onChangeText?: (text: string) => void;
  faqLabel?: string;
};

export const AdaptiveInputAmount: FC<{
  value: string;
  onAmountApply: (amount: string) => void;
  onChangeText: (text: string) => void;
  onFaqCtaPress?: () => void;
  inputError?: string;
  faqLabel?: string;
}> = ({ faqLabel, onAmountApply, onChangeText, value, inputError, onFaqCtaPress }) => {
  const dispatch = useDispatch();
  const { activeInput, repayInputs } = useDebtObligation();

  useEffect(() => {
    if (activeInput) {
      if (activeInput.type !== DebtRepayType.INPUT) {
        onAmountApply(activeInput.metadata.repay_value);
      } else {
        onAmountApply(value);
      }
    }
  }, [activeInput]);

  const wrapperInput = (component: any, key: number) => {
    return (
      <View key={key}>
        {component}
        <Spacer height={16} />
      </View>
    );
  };

  const renderLoading = () => {
    return (
      <>
        <Skeleton width={200} height={20} style={styles.loadingTitle} />
        <Skeleton width={calculateScaleDimension(340)} height={62} style={styles.loading} />
        <Skeleton width={calculateScaleDimension(340)} height={62} style={styles.loading} />
        <Skeleton width={calculateScaleDimension(340)} height={62} style={styles.loading} />
      </>
    );
  };
  const hasRepayInputs = repayInputs && repayInputs.length > 0;
  return (
    <View testID={'adaptive-input'} style={styles.container}>
      {!repayInputs && renderLoading()}
      {hasRepayInputs && (
        <AppText style={styles.title} size={16} bold>
          Chọn số tiền thanh toán
        </AppText>
      )}
      {hasRepayInputs &&
        repayInputs.map((repayInput, index) => {
          switch (repayInput.type) {
            case DebtRepayType.REPAID:
              return wrapperInput(<AdaptiveRepaid repayInput={repayInput} />, index);
            case DebtRepayType.FIXED:
              return wrapperInput(
                <AdaptiveSuggestInput
                  repayInput={repayInput}
                  isActive={activeInput?.selection_id === repayInput.selection_id}
                  onPress={repayInput => {
                    publishTracking?.({
                      source:
                        repayInput.selection_id === DebtRepaySelectionId.MAX ? 'pick_total_amount' : 'pick_min_amount',
                      event: Event.CTA_CLICK,
                    });
                    dispatch(setActiveInput(repayInput));
                  }}
                />,
                index,
              );
            case DebtRepayType.INPUT:
              return wrapperInput(
                <AdaptiveManualInput
                  repayInput={repayInput}
                  value={value}
                  onChangeText={onChangeText}
                  onPress={_repayInput => {
                    publishTracking?.({ source: 'pick_custom_amount', event: Event.CTA_CLICK });
                    dispatch(setActiveInput(repayInput));
                  }}
                  inputError={inputError}
                  isActive={activeInput?.selection_id === repayInput.selection_id}
                />,
                index,
              );
          }
        })}
      {hasRepayInputs && onFaqCtaPress && (
        <View style={[StyleUtils.flexRow, StyleUtils.centered]}>
          <AppImage width={16} height={16} source={images.IconInfoBlueCircle} />
          <Spacer width={4} />
          <LinkButton
            size={14}
            onPress={() => {
              publishTracking?.({ source: 'view_term', event: Event.CTA_CLICK });
              onFaqCtaPress();
            }}>
            {faqLabel ? faqLabel : ' Giải thích thuật ngữ'}
          </LinkButton>
        </View>
      )}
    </View>
  );
};

const AdaptiveRepaid: FC<{ repayInput: DebtRepayOptions<DebtRepayMetadata> }> = ({ repayInput }) => {
  return (
    <View testID={repayInput.selection_id} style={styles.completeContainer}>
      <AppImage width={20} height={20} source={images.IconCheckCircle} tintColor={AppColors.green[4]} />
      <View style={styles.selectableTextContainer}>
        <View style={StyleUtils.rowStretchBetween}>
          <AppText>{repayInput.metadata.title}</AppText>
        </View>
        <Spacer height={2} />
        <AppText color={AppColors.green[4]}>{repayInput.metadata.message}</AppText>
        
      </View>
    </View>
  );
};

const AdaptiveSuggestInput: FC<AdaptiveInput> = ({ repayInput, isActive, onPress }) => {
  return (
    <SuggestButton
      testID={repayInput.selection_id}
      style={styles.suggestButton}
      active={isActive}
      onPress={() => {
        onPress?.(repayInput);
      }}>
      <View style={styles.selectableTextContainer}>
        <View style={StyleUtils.rowStretchBetween}>
          <AppText>{repayInput.metadata.title}</AppText>
        </View>
        <Spacer height={2} />
        <AppText color={AppColors.text2}>{repayInput.metadata.display_amount}</AppText>
      </View>
    </SuggestButton>
  );
};

const AdaptiveManualInput: FC<AdaptiveInput> = ({ value, onChangeText, repayInput, onPress, inputError, isActive }) => {
  const inputRef = React.useRef<RNTextInput>(null);
  return (
    <>
      <SuggestButton
        testID={repayInput.selection_id}
        style={inputError && isActive ? styles.warningContainer : styles.suggestButton}
        active={isActive}
        onPress={() => {
          onPress?.(repayInput);
          inputRef?.current?.focus?.();
        }}>
        {!isActive && (
          <TouchableOpacity
            testID="suggest-manual-overlay"
            activeOpacity={1}
            style={styles.selectableInputOverlay}
            onPress={() => {
              inputRef?.current?.focus?.();
              onPress?.(repayInput);
            }}
          />
        )}
        <AppTextInput
          ref={inputRef}
          style={StyleUtils.flexOne}
          containerStyle={styles.selectableInputContainer}
          containerDisabledStyle={styles.selectableInputContainer}
          borderStyle={styles.selectableInputBorderStyle}
          testID="inputMoney"
          clearable
          placeholder={repayInput.metadata.message || 'Nhập số khác'}
          // onPressIn={() => setActiveAmount('manual')}
          keyboardType="number-pad"
          clearButtonSize={20}
          value={value}
          onClear={() => {
            publishTracking?.({ source: 'delete_amount', event: Event.CTA_CLICK });
          }}
          onChangeImmediateText={handleChangeImmediateText}
          onChangeText={value => {
            onChangeText?.(value);
          }}
          textStyle={styles.selectableInputText}
        />
      </SuggestButton>
      {Boolean(inputError) && isActive && <AppText color={AppColors.error}>{inputError}</AppText>}
    </>
  );
};

const styles = StyleSheet.create({
  textInput: { fontSize: 24, paddingTop: 12, paddingBottom: 12 },
  selectableInputText: { fontSize: 16 },
  selectableInputOverlay: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    zIndex: 100,
  },
  selectableInputBorderStyle: { borderWidth: 0 },
  selectableInputContainer: { paddingStart: 0, backgroundColor: AppColors.transparent },
  container: { padding: 16, borderRadius: 20, backgroundColor: AppColors.background },
  warningContainer: { borderColor: AppColors.error, marginBottom: 8 },
  selectableTextContainer: { flexDirection: 'column', marginStart: 16 },
  completeContainer: {
    flex: 1,
    borderWidth: 1,
    borderColor: AppColors.expensePieStroke,
    borderRadius: 8,
    paddingHorizontal: 16,
    paddingVertical: 8,
    paddingRight: 8,
    flexDirection: 'row',
    alignItems: 'center',
    height: 62,
  },
  suggestButton: { height: 62 },
  ribbon: {
    position: 'absolute',
    top: -12,
    right: -1,
    height: 24,
  },
  loading: {
    borderRadius: 8,
    marginBottom: 16,
  },
  title: { marginBottom: 16 },
  loadingTitle: {
    borderRadius: 4,
    marginBottom: 16,
  },
});

import { AmountInput } from './AmountInput';
import React from 'react';
import { fireEvent, waitFor } from '@testing-library/react-native';
import { renderWithRedux } from 'bnpl-shared/src/jest/renderWithRedux';
import { store } from 'bnpl-shared/src/redux/store';
import { setChosenPartner, setPartnerData } from 'bnpl-shared/src/redux/multipartnerReducer';
import { PartnerCode } from 'bnpl-shared/src/constants';
import { RoutingInfoMapper } from 'bnpl-shared/src/features/multipartner_integration/helpers';
import { FakePartnerCIMB } from 'bnpl-shared/src/jest/fakeData';
import { setActiveInput, setRepayInputs, setServiceFee } from 'bnpl-shared/src/redux/debtObligationReducer';
import { formatCurrency } from 'bnpl-shared/src/shared/utils/formatCurrency';
import { DebtRepayMetadata, DebtRepayType } from 'bnpl-shared/src/types';

jest.mock('bnpl-shared/src/api/partner_lotte/getAccountDebtObligationApi', () => ({
  getAccountDebtObligationApi: jest.fn(),
}));

describe(AmountInput.name, () => {
  it('render adaptive input amount as expected', async () => {
    store.dispatch(
      setPartnerData({
        partnerCode: PartnerCode.CIMB,
        data: new RoutingInfoMapper(FakePartnerCIMB).toPartnerData(),
      }),
    );
    store.dispatch(setChosenPartner(PartnerCode.CIMB));
    store.dispatch(setRepayInputs([
      {
        selection_id: 'max_repayment',
        type: DebtRepayType.FIXED,
        metadata: {
          repay_value: '300000',
          display_amount: formatCurrency(300000),
          title: 'Tổng dư nợ',
        } as DebtRepayMetadata,
      },
      {
        selection_id: 'min_repayment',
        type: 'fixed' as DebtRepayType,
        metadata: {
          repay_value: '100000',
          display_amount: formatCurrency(100000),
          title: 'Dư nợ tối thiểu',
        } as DebtRepayMetadata,
      },
      {
        selection_id: 'input_amount',
        type: 'input' as DebtRepayType,
        metadata: {
          message: 'Nhập số tiền',
        } as DebtRepayMetadata,
      },
    ]))
    store.dispatch(setActiveInput({
        selection_id: 'min_repayment',
        type: 'fixed' as DebtRepayType,
        metadata: {
          repay_value: '100000',
          display_amount: formatCurrency(100000),
          title: 'Dư nợ tối thiểu',
        } as DebtRepayMetadata,      
    }));
    store.dispatch(setServiceFee("200000"))

    const { queryByText, queryByTestId } = renderWithRedux(
      <AmountInput selectable={true} onAmountApply={jest.fn()} onFaqCtaPress={jest.fn()} />,
    );

    await waitFor(() => expect(queryByText('100.000đ')).toBeTruthy());
    expect(queryByText('300.000đ')).toBeTruthy();
    await waitFor(() => expect(queryByTestId('adaptive-input')).toBeTruthy());

    await waitFor(() => expect(queryByTestId('input_amount')).toBeTruthy());
  });
  it('render manually input amount as expected', () => {
    const { queryByText, queryByTestId } = renderWithRedux(
      <AmountInput selectable={false} onAmountApply={jest.fn()} onFaqCtaPress={jest.fn()} />,
    );
    expect(queryByText('100.000đ')).toBeFalsy();
    expect(queryByText('300.000đ')).toBeFalsy();
    expect(queryByTestId('manually-input')).toBeTruthy();
  });
  it('verify onChangeText work as expected', async () => {
    const onAmountApply = jest.fn();
    const { getByTestId } = renderWithRedux(
      <AmountInput selectable={false} onAmountApply={onAmountApply} onFaqCtaPress={jest.fn()} />,
    );
    fireEvent.changeText(getByTestId('manually-input'), '100000');
    expect(onAmountApply).toHaveBeenCalledWith('100.000đ');
  });
});

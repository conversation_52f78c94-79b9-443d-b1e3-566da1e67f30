import React, { FC, useEffect, useState } from 'react';
import { CURRENCY } from 'bnpl-shared/src/constants';
import { formatCurrency } from 'bnpl-shared/src/shared/utils/formatCurrency';
import { formatMoneyToNumber } from 'bnpl-shared/src/shared/utils/formatMoneyToNumber';
import { validateMoneyInput } from 'bnpl-shared/src/shared/utils/validateMoneyInput';
import { ManuallyInput } from 'bnpl-shared/src/components/AmountInput/ManuallyInput';
import { AdaptiveInputAmount } from './AdaptiveInputAmount';
import { useUserBalanceController } from 'bnpl-shared/src/utils/useUserBalanceController';

type Props = {
  selectable: boolean;
  onAmountApply: (amount: string) => void;
  onFaqCtaPress?: () => void;
  faqLabel?: string;
};

export const AmountInput: FC<Props> = ({ faqLabel, onAmountApply, selectable, onFaqCtaPress }) => {
  const { balanceSummary } = useUserBalanceController();
  const userOB = balanceSummary.data?.outstanding_balance;
  const [value, setValue] = useState<string>('');
  const [inputError, setInputError] = useState<string>('');
  const onChangeText = (text: string) => {
    let stringMoney = text;
    if (inputError) {
      setInputError('');
    }
    try {
      const { error, isValid } = validateMoneyInput(text, { max: userOB });
      if (error !== '') {
        setInputError(error);
      }
      if (isValid) {
        const formatMoney: any = formatMoneyToNumber(text);
        stringMoney = formatCurrency(formatMoney, CURRENCY);
        onAmountApply(stringMoney);
      } else {
        onAmountApply('');
      }
    } catch (error) {
      setValue('');
    }
    setValue(stringMoney);
  };

  //disable submit if value = '' on mount
  useEffect(() => {
    onAmountApply('');
  }, []);

  return selectable ? (
    <AdaptiveInputAmount
      inputError={inputError}
      onFaqCtaPress={onFaqCtaPress}
      value={value}
      onChangeText={onChangeText}
      onAmountApply={onAmountApply}
      faqLabel={faqLabel}
    />
  ) : (
    <ManuallyInput onChangeText={onChangeText} inputError={inputError} value={value} />
  );
};

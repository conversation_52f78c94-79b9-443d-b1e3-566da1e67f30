import React, { FC, useLayoutEffect } from 'react';
import { AppText, AppTextInput } from 'bnpl-shared/src/shared/react-native-customized';
import { StyleSheet } from 'bnpl-shared/src/shared/styles/StyleSheet';
import { handleChangeImmediateText } from 'bnpl-shared/src/components/AmountInput/helper';
import { TextInput as RNTextInput } from 'react-native';

export const ManuallyInput: FC<{
  onChangeText: (text: string) => void;
  inputError: string;
  value: string;
  editable?: boolean;
  clearable?: boolean;
}> = ({ onChangeText, inputError, value, editable = true, clearable = true }) => {
  const inputRef = React.useRef<RNTextInput>(null);
  useLayoutEffect(() => {
    inputRef.current?.focus();
  }, []);
  return (
    <>
      <AppText style={styles.title} size={16} bold>
        Nhập số tiền thanh toán
      </AppText>
      <AppTextInput
        editable={editable}
        ref={inputRef}
        testID="manually-input"
        clearable={clearable}
        error={inputError}
        placeholder="Nhập số tiền"
        keyboardType="number-pad"
        clearButtonSize={20}
        value={value}
        onChangeImmediateText={handleChangeImmediateText}
        onChangeText={value => {
          onChangeText(value);
        }}
        textStyle={styles.textInput}
        style={styles.selectableInputText}
      />
    </>
  );
};

const styles = StyleSheet.create({
  textInput: { fontSize: 24, paddingTop: 12, paddingBottom: 12 },
  selectableInputText: { paddingHorizontal: 16 },
  title: { marginTop: 20, marginStart: 16, marginBottom: 16 },
});

import { SelectableInput } from './SelectableInput';
import { fireEvent, render } from '@testing-library/react-native';
import React from 'react';

describe(SelectableInput.name, () => {
  it('renders as expected', () => {
    const { queryByText, queryByTestId } = render(
      <SelectableInput
        value={''}
        showMinPaySuggest={false}
        showMinPayCompleted={false}
        minPay={'30.000đ'}
        totalPay={'300.000đ'}
        minPaySuggest={'0đ'}
        onAmountApply={jest.fn()}
        onChangeText={jest.fn()}
        onFaqCtaPress={jest.fn()}
        inputError={''}
      />,
    );
    expect(queryByText('30.000đ')).toBeTruthy();
    expect(queryByText('300.000đ')).toBeTruthy();
    expect(queryByTestId('suggest-total')).toBeTruthy();
    expect(queryByTestId('suggest-min')).toBeTruthy();
    expect(queryByTestId('suggest-manual')).toBeTruthy();
    expect(queryByText('Thanh toán tối thiểu')).toBeTruthy();
    expect(queryByText('Dư nợ đến hạn')).toBeTruthy();
  });
  it('verify onAmountApply work as expected', () => {
    const onAmountApply = jest.fn();
    const { getByTestId, queryByTestId } = render(
      <SelectableInput
        showMinPaySuggest={false}
        showMinPayCompleted={false}
        value={'100.000đ'}
        minPay={'30.000đ'}
        totalPay={'300.000đ'}
        minPaySuggest={'0đ'}
        onAmountApply={onAmountApply}
        onChangeText={jest.fn()}
        onFaqCtaPress={jest.fn()}
        inputError={''}
      />,
    );
    expect(onAmountApply).toHaveBeenNthCalledWith(1, '30.000đ');
    fireEvent.press(getByTestId('suggest-min'));
    expect(onAmountApply).toHaveBeenNthCalledWith(2, '30.000đ');
    expect(queryByTestId('suggest-min-center-dot')).toBeTruthy();
    fireEvent.press(getByTestId('suggest-total'));
    expect(onAmountApply).toHaveBeenNthCalledWith(3, '300.000đ');
    expect(queryByTestId('suggest-total-center-dot')).toBeTruthy();
    fireEvent.press(getByTestId('suggest-manual'));
    expect(queryByTestId('suggest-manual-center-dot')).toBeTruthy();
    fireEvent.changeText(getByTestId('inputMoney'), '100000');
    expect(onAmountApply).toHaveBeenNthCalledWith(4, '100.000đ');
  });
  it('verify suggest manual overlay handle as expected', () => {
    const { getByTestId, queryByTestId } = render(
      <SelectableInput
        showMinPaySuggest={false}
        showMinPayCompleted={false}
        value={'100.000đ'}
        minPay={'30.000đ'}
        totalPay={'300.000đ'}
        minPaySuggest={'0đ'}
        onAmountApply={jest.fn()}
        onChangeText={jest.fn()}
        onFaqCtaPress={jest.fn()}
        inputError={''}
      />,
    );
    expect(queryByTestId('suggest-manual-overlay')).toBeTruthy();
    fireEvent.press(getByTestId('suggest-manual-overlay'));
    expect(queryByTestId('suggest-manual-overlay')).toBeFalsy();
    expect(queryByTestId('suggest-manual-center-dot')).toBeTruthy();
  });
  it('verify onFaqCtaPress handle as expected', () => {
    const onFaqCtaPress = jest.fn();
    const { getByText } = render(
      <SelectableInput
        showMinPaySuggest={false}
        showMinPayCompleted={false}
        value={'100.000đ'}
        minPay={'30.000đ'}
        totalPay={'300.000đ'}
        minPaySuggest={'0đ'}
        onAmountApply={jest.fn()}
        onChangeText={jest.fn()}
        onFaqCtaPress={onFaqCtaPress}
        inputError={''}
      />,
    );
    fireEvent.press(getByText('Giải thích thuật ngữ'));
    expect(onFaqCtaPress).toHaveBeenCalled();
  });

  it('verify render suggest min pay as expected', () => {
    const onAmountApply = jest.fn();
    const { queryByText } = render(
      <SelectableInput
        showMinPayCompleted={false}
        showMinPaySuggest={true}
        value={'100.000đ'}
        minPay={'0đ'}
        totalPay={'300.000đ'}
        minPaySuggest={'20.000đ'}
        onAmountApply={onAmountApply}
        onChangeText={jest.fn()}
        onFaqCtaPress={jest.fn()}
        inputError={''}
      />,
    );
    expect(queryByText('20.000đ')).toBeTruthy();
    expect(queryByText('Thanh toán tối thiểu')).toBeTruthy();
    expect(onAmountApply).toHaveBeenCalledWith('20.000đ');
  });

  describe('verify render min pay completed as expected', () => {
    it('with suggest min pay', () => {
      const onAmountApply = jest.fn();
      const { queryByText, queryByTestId } = render(
        <SelectableInput
          showMinPayCompleted={true}
          showMinPaySuggest={true}
          value={'100.000đ'}
          minPay={'0đ'}
          totalPay={'300.000đ'}
          minPaySuggest={'0đ'}
          onAmountApply={onAmountApply}
          onChangeText={jest.fn()}
          onFaqCtaPress={jest.fn()}
          inputError={''}
        />,
      );
      expect(queryByTestId('suggest-min')).toBeFalsy();
      expect(queryByTestId('min-pay-completed')).toBeTruthy();
      expect(queryByText('Thanh toán tối thiểu')).toBeTruthy();
      expect(queryByText('Đã thanh toán')).toBeTruthy();
      expect(onAmountApply).toHaveBeenCalledWith('300.000đ');
    });

    it('with min pay', () => {
      const onAmountApply = jest.fn();
      const { queryByText, queryByTestId } = render(
        <SelectableInput
          showMinPayCompleted={true}
          showMinPaySuggest={false}
          value={'100.000đ'}
          minPay={'0đ'}
          totalPay={'300.000đ'}
          minPaySuggest={'0đ'}
          onAmountApply={onAmountApply}
          onChangeText={jest.fn()}
          onFaqCtaPress={jest.fn()}
          inputError={''}
        />,
      );
      expect(queryByTestId('suggest-min')).toBeFalsy();
      expect(queryByTestId('min-pay-completed')).toBeTruthy();
      expect(queryByText('Thanh toán tối thiểu')).toBeTruthy();
      expect(queryByText('Đã thanh toán')).toBeTruthy();
      expect(onAmountApply).toHaveBeenCalledWith('300.000đ');
    });
  });
});

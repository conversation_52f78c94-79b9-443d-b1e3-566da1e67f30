import React, { FC, useEffect, useState } from 'react';
import { TextInput as RNTextInput, TouchableOpacity, View } from 'react-native';
import { StyleUtils } from 'bnpl-shared/src/shared/styles/StyleUtils';
import { AppImage, AppText, AppTextInput } from 'bnpl-shared/src/shared/react-native-customized';
import { Spacer } from 'bnpl-shared/src/components/Spacer';
import { AppColors } from 'bnpl-shared/src/constants';
import { SuggestButton } from 'bnpl-shared/src/components/SuggestButton';
import { images } from 'bnpl-shared/src/res';
import { LinkButton } from 'bnpl-shared/src/shared/LinkButton';
import { StyleSheet } from 'bnpl-shared/src/shared/styles/StyleSheet';
import { handleChangeImmediateText } from './helper';
import { Event, publishTracking } from 'bnpl-shared/src/utils/tracking';

export const SelectableInput: FC<{
  value: string;
  minPay: string;
  minPaySuggest: string;
  totalPay: string;
  onAmountApply: (amount: string) => void;
  onChangeText: (text: string) => void;
  onFaqCtaPress: () => void;
  inputError: string;
  showMinPaySuggest: boolean;
  showMinPayCompleted: boolean;
}> = ({
  minPay,
  totalPay,
  onAmountApply,
  value,
  onChangeText,
  onFaqCtaPress,
  inputError,
  minPaySuggest,
  showMinPaySuggest = false,
  showMinPayCompleted = false,
}) => {
  const [activeAmount, setActiveAmount] = useState<'min' | 'total' | 'manual'>();
  const inputRef = React.useRef<RNTextInput>(null);

  useEffect(() => {
    if (showMinPayCompleted) {
      setActiveAmount('total');
      onAmountApply(totalPay);
    } else {
      setActiveAmount('min');
      onAmountApply(showMinPaySuggest ? minPaySuggest : minPay);
    }
  }, []);

  const renderSelectableAmountView = (title: string, amount: string) => {
    return (
      <View style={styles.selectableTextContainer}>
        <View style={StyleUtils.rowStretchBetween}>
          <AppText>{title}</AppText>
        </View>
        <Spacer height={2} />
        <AppText color={AppColors.text2}>{amount}</AppText>
      </View>
    );
  };

  const renderMinPayCompleted = () => {
    return (
      <View testID="min-pay-completed" style={styles.completeContainer}>
        <AppImage width={20} height={20} source={images.IconCheckCircle} tintColor={AppColors.green[4]} />
        <View style={styles.selectableTextContainer}>
          <View style={StyleUtils.rowStretchBetween}>
            <AppText>Thanh toán tối thiểu</AppText>
          </View>
          <Spacer height={2} />
          <AppText color={AppColors.green[4]}>Đã thanh toán</AppText>
        </View>
      </View>
    );
  };

  const renderMinPayButton = () => {
    return (
      <SuggestButton
        style={styles.suggestButton}
        testID="suggest-min"
        active={activeAmount === 'min'}
        onPress={() => {
          publishTracking?.({ source: 'pick_min_amount', event: Event.CTA_CLICK });
          onAmountApply(showMinPaySuggest ? minPaySuggest : minPay);
          setActiveAmount('min');
          inputRef?.current?.blur?.();
        }}>
        {renderSelectableAmountView('Thanh toán tối thiểu', showMinPaySuggest ? minPaySuggest : minPay)}
      </SuggestButton>
    );
  };

  return (
    <View testID="selectable-input" style={[styles.selectableContainer]}>
      <AppText bold size={16}>
        Chọn số tiền thanh toán
      </AppText>
      <Spacer height={16} />
      <SuggestButton
        style={styles.suggestButton}
        testID="suggest-total"
        active={activeAmount === 'total'}
        onPress={() => {
          publishTracking?.({ source: 'pick_total_amount', event: Event.CTA_CLICK });
          onAmountApply(totalPay);
          setActiveAmount('total');
          inputRef?.current?.blur?.();
        }}>
        {renderSelectableAmountView('Dư nợ đến hạn', totalPay)}
      </SuggestButton>
      <Spacer height={16} />
      {showMinPayCompleted ? renderMinPayCompleted() : renderMinPayButton()}
      <Spacer height={16} />
      <SuggestButton
        testID="suggest-manual"
        style={inputError && activeAmount === 'manual' ? styles.warningContainer : styles.suggestButton}
        active={activeAmount === 'manual'}
        onPress={() => {
          publishTracking?.({ source: 'pick_custom_amount', event: Event.CTA_CLICK });
          setActiveAmount('manual');
          onAmountApply(value);
          inputRef?.current?.focus?.();
        }}>
        {activeAmount !== 'manual' && (
          <TouchableOpacity
            testID="suggest-manual-overlay"
            activeOpacity={1}
            style={styles.selectableInputOverlay}
            onPress={() => {
              publishTracking?.({ source: 'pick_custom_amount', event: Event.CTA_CLICK });
              setActiveAmount('manual');
              onAmountApply(value);
              inputRef?.current?.focus?.();
            }}
          />
        )}
        <AppTextInput
          ref={inputRef}
          style={StyleUtils.flexOne}
          containerStyle={styles.selectableInputContainer}
          containerDisabledStyle={styles.selectableInputContainer}
          borderStyle={styles.selectableInputBorderStyle}
          testID="inputMoney"
          clearable
          placeholder="Nhập số khác"
          onPressIn={() => setActiveAmount('manual')}
          keyboardType="number-pad"
          clearButtonSize={20}
          value={value}
          onClear={() => {
            publishTracking?.({ source: 'delete_amount', event: Event.CTA_CLICK });
          }}
          onChangeImmediateText={handleChangeImmediateText}
          onChangeText={value => {
            onChangeText(value);
          }}
          textStyle={styles.selectableInputText}
        />
      </SuggestButton>
      {Boolean(inputError) && activeAmount === 'manual' && <AppText color={AppColors.error}>{inputError}</AppText>}
      <Spacer height={20} />
      <View style={[StyleUtils.flexRow, StyleUtils.centered]}>
        <AppImage width={16} height={16} source={images.IconInfoBlueCircle} />
        <Spacer width={4} />
        <LinkButton
          size={14}
          onPress={() => {
            publishTracking?.({ source: 'view_term', event: Event.CTA_CLICK });
            onFaqCtaPress();
          }}>
          Giải thích thuật ngữ
        </LinkButton>
      </View>
    </View>
  );
};

const styles = StyleSheet.create({
  textInput: { fontSize: 24, paddingTop: 12, paddingBottom: 12 },
  selectableInputText: { fontSize: 16 },
  selectableInputOverlay: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    zIndex: 100,
  },
  selectableInputBorderStyle: { borderWidth: 0 },
  selectableInputContainer: { paddingStart: 0, backgroundColor: AppColors.background },
  container: { flex: 1, backgroundColor: AppColors.background },
  selectableContainer: {
    marginBottom: 16,
    borderRadius: 8,
    backgroundColor: AppColors.background,
    padding: 16,
    overflow: 'visible',
  },
  warningContainer: { borderColor: AppColors.error, marginBottom: 8 },
  selectableTextContainer: { flexDirection: 'column', marginStart: 16 },
  completeContainer: {
    flex: 1,
    borderWidth: 1,
    borderColor: AppColors.expensePieStroke,
    borderRadius: 8,
    paddingHorizontal: 16,
    paddingVertical: 8,
    paddingRight: 8,
    flexDirection: 'row',
    alignItems: 'center',
    height: 62,
  },
  suggestButton: { height: 62 },
  ribbon: {
    position: 'absolute',
    top: -12,
    right: -1,
    height: 24,
  },
});

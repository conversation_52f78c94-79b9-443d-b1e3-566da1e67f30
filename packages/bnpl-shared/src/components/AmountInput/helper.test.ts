import { asNumber, handleChangeImmediateText } from './helper';

describe(asNumber.name, () => {
  it('verify execute as expected', () => {
    expect(asNumber('012345')).toEqual(12345);
    expect(asNumber('1230485')).toEqual(1230485);
  });
});

describe(handleChangeImmediateText.name, () => {
  it('verify execute as expected', () => {
    expect(handleChangeImmediateText(undefined, '15000')).toEqual('15.000đ');
    expect(handleChangeImmediateText('115000', '15000')).toEqual('15.000đ');
    expect(handleChangeImmediateText('15000', '215000')).toEqual('215.000đ');
  });
});

import { CURRENCY } from 'bnpl-shared/src/constants';
import { formatCurrency } from 'bnpl-shared/src/shared/utils/formatCurrency';

export const asNumber = (value: string) => {
  return Number.parseInt(value.replace(/[^0-9]/g, '').match(/^\d{0,9}/)?.[0] || '', 10);
};

export const handleChangeImmediateText = (text: string | undefined, newText: string) => {
  let newValue;

  if (!text) {
    const newValue = asNumber(newText);
    return formatCurrency(newValue, CURRENCY);
  }

  // If current = text - (latest character)...
  if (newText.length === text.length - 1 && text.startsWith(newText)) {
    // We infer that user just pressed Backspace.
    newValue = asNumber(newText.slice(0, -CURRENCY.length));
  } else {
    newValue = asNumber(newText);
  }

  return formatCurrency(newValue, CURRENCY);
};

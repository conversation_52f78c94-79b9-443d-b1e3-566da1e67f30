import { getAccountDebtObligationApi } from 'bnpl-shared/src/api/partner_lotte/getAccountDebtObligationApi';
import { INVALID_ACCOUNT_ID } from 'bnpl-shared/src/constants';
import { usePartnerData } from 'bnpl-shared/src/features/multipartner_integration/helpers';
import {
  setActiveInput,
  setInfoMessage,
  setRepayInputs,
  setServiceFee,
} from 'bnpl-shared/src/redux/debtObligationReducer';
import { useAppSelector } from 'bnpl-shared/src/redux/store';
import {
  DebtRepayMetadata,
  DebtRepayOptions,
  DebtRepaySelectionId,
  DebtRepayType,
  InfoMessageType,
} from 'bnpl-shared/src/types';
import { isNumber } from 'lodash';
import { useCallback } from 'react';
import { useDispatch } from 'react-redux';

export const useDebtObligation = () => {
  const dispatch = useDispatch();
  const { getChosenPartner } = usePartnerData();
  const chosenPartnerAccountId = getChosenPartner()?.account_id;
  const repayInputs: DebtRepayOptions<DebtRepayMetadata>[] | undefined = useAppSelector(
    state => state.debtObligation.repayInputs,
  );
  const activeInput: DebtRepayOptions<DebtRepayMetadata> | undefined = useAppSelector(
    state => state.debtObligation.activeInput,
  );
  const serviceFee: string | undefined = useAppSelector(state => state.debtObligation.serviceFee);
  const hasServiceFee: boolean = !!(serviceFee && isNumber(Number(serviceFee)) && Number(serviceFee) > 0);
  const infoMessage: InfoMessageType | undefined = useAppSelector(state => state.debtObligation.infoMessage);
  const fetchDebtObligation = useCallback(async () => {
    if (chosenPartnerAccountId && chosenPartnerAccountId !== INVALID_ACCOUNT_ID) {
      try {
        const resp = await getAccountDebtObligationApi(chosenPartnerAccountId);
        if (resp.service_fee) {
          dispatch(setServiceFee(resp.service_fee));
        }
        if (resp.info_message) {
          dispatch(setInfoMessage(resp.info_message));
        }

        if (resp.repayment_options.length) {
          dispatch(setRepayInputs(resp.repayment_options));
          const minAmountInput = resp.repayment_options.find(input => input.selection_id === DebtRepaySelectionId.MIN);
          const maxAmountInput = resp.repayment_options.find(input => input.selection_id === DebtRepaySelectionId.MAX);
          const odTotalAmountInput = resp.repayment_options.find(
            input => input.selection_id === DebtRepaySelectionId.OD_OUTSTANDING,
          );
          switch (true) {
            case minAmountInput && minAmountInput.type !== DebtRepayType.REPAID:
              dispatch(setActiveInput(minAmountInput));
              break;
            case maxAmountInput && maxAmountInput.type !== DebtRepayType.REPAID:
              dispatch(setActiveInput(maxAmountInput));
              break;
            case odTotalAmountInput && odTotalAmountInput.type !== DebtRepayType.REPAID:
              dispatch(setActiveInput(odTotalAmountInput));
              break;
          }
        } else {
          dispatch(setRepayInputs([]));
        }
      } catch (e) {
        console.log(e);
      }
    }
  }, [chosenPartnerAccountId]);

  return {
    fetchDebtObligation,
    repayInputs,
    activeInput,
    setActiveInput,
    serviceFee,
    hasServiceFee,
    infoMessage,
  };
};

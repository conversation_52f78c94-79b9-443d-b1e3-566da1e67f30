import { RewardInfo } from 'bnpl-shared/src/types/RewardVoucher';
import React, { memo, useEffect, useState } from 'react';
import { getAssetInventory } from 'bnpl-shared/src/api/getAssetInventory';
import AssetFrameUI from './AssetFrameUI';

const AssetFrame = ({
  assetInventoryId,
  onItemPress,
  onEmpty,
}: {
  assetInventoryId?: string;
  onItemPress: (url: string) => void;
  onEmpty?: () => void;
}) => {
  const [rewardVoucherList, setRewardVoucherList] = useState<RewardInfo[]>([]);
  const [error, setError] = useState<string>('');

  useEffect(() => {
    if (!assetInventoryId) {
      return;
    }
    const fetchData = async () => {
      try {
        const response = await getAssetInventory(assetInventoryId);
        if (!response || !response?.asset_data || !response?.asset_data[0] || !response?.asset_data[0].reward_infos) {
          setError('empty_data');
          onEmpty?.();
          return;
        }
        setRewardVoucherList(response.asset_data[0].reward_infos);
      } catch (e) {
        console.log(e);
        setError('fetch_failed');
      }
    };
    fetchData();
  }, [assetInventoryId]);

  if (error || !assetInventoryId) {
    return null;
  }

  return <AssetFrameUI onItemPress={onItemPress} data={rewardVoucherList} />;
};

export default memo(AssetFrame);

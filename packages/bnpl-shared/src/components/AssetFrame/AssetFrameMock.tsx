import React from 'react';
import AssetFrame from 'bnpl-shared/src/components/AssetFrame/AssetFrame';
import GuideRewardRedemBottomSheetUI from 'bnpl-shared/src/components/GuideRewardRedemBottomSheetUI';
import { InfoModalService, ModalType } from 'bnpl-shared/src/services';
import { AppColors } from 'bnpl-shared/src/constants';

// Main mission of this component is show mock asset inventory in ZPI/ZMP for user not payment with PL yet.
const AssetFrameMock = ({ assetInventoryId, onEmpty }: { assetInventoryId?: string; onEmpty?: () => void }) => {
  const onItemPress = async (_url: string) => {
    InfoModalService.showModal({
      screen: <GuideRewardRedemBottomSheetUI onClose={InfoModalService.hideModal} />,
      type: ModalType.BOTTOM_SHEET,
      bottomSheetProps: {
        title: 'Đổi quà',
        showHeader: false,
        containerStyle: {
          padding: 16,
          paddingTop: 50,
          backgroundColor: AppColors.transparent,
        },
      },
    });
  };
  return (
    <AssetFrame
      assetInventoryId={assetInventoryId}
      onItemPress={onItemPress}
      onEmpty={() => {
        onEmpty?.();
      }}
    />
  );
};

export default AssetFrameMock;

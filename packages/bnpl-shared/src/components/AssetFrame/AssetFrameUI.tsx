import { Skeleton } from 'bnpl-shared/src/shared/animated-wrappers/Skeleton';
import { RewardInfo } from 'bnpl-shared/src/types/RewardVoucher';
import React, { memo } from 'react';
import { FlatList, TouchableOpacity, View } from 'react-native';
import { StyleSheet } from 'bnpl-shared/src/shared/styles/StyleSheet';
import { Spacer } from 'bnpl-shared/src/components/Spacer';
import { AppColors } from 'bnpl-shared/src/constants';
import { AppImage, AppText } from 'bnpl-shared/src/shared/react-native-customized';
import LoyaltyCoinIcon from './LoyaltyCoinIcon';
import { buildZlpPublicBaseUrl } from 'bnpl-shared/src/api/withBaseUrl';
import { showDialog } from 'bnpl-shared/src/shared/ZaloPayModules';
import { DialogType } from 'bnpl-shared/src/shared/constants';
import { avoidCacheImageUrl } from 'bnpl-shared/src/shared/utils';
import { StyleUtils } from 'bnpl-shared/src/shared/styles/StyleUtils';

const AssetFrameUI = (props: { data?: RewardInfo[]; onItemPress: (url: string) => void }) => {
  const { data, onItemPress } = props;
  const handleItemPress = (item: RewardInfo) => {
    const directionZPALink = item?.direction_info?.detail_link_zpa;
    if (directionZPALink) {
      onItemPress?.(directionZPALink);
      return;
    }

    const directionZPILink = item?.direction_info?.detail_link_zpi;
    if (directionZPILink) {
      onItemPress(buildZlpPublicBaseUrl() + '/spa/v2' + directionZPILink);
      return;
    }
    //fallback fail case
    showDialog(DialogType.INFO, 'Úi có gì đó không đúng', 'Mong bạn thông cảm và thử lại sau', ['Đóng']);
  };

  const renderItem = ({ item }: { item: RewardInfo }) => (
    <>
      <TouchableOpacity style={styles.item} onPress={() => handleItemPress(item)}>
        <View style={styles.voucherItem}>
          <AppImage source={{ uri: avoidCacheImageUrl(item?.display_info?.icon) }} width={46} height={46} />
          <Spacer width={8} />
          <View>
            <AppText size={12} color={AppColors.dark300} style={styles.text} numberOfLines={1}>
              {item.display_info?.brand_name}
            </AppText>
            <AppText bold style={styles.text} numberOfLines={1}>
              {item.general_info?.reward_name}
            </AppText>
            <Spacer height={4} />
            <View style={styles.coinContainer}>
              <LoyaltyCoinIcon width={16} height={16} />
              <Spacer width={2} />
              <AppText color={AppColors.green[4]} bold>
                {item.exchange_info?.reward_prices?.POINT?.sell_price}
              </AppText>

              {item.exchange_info?.reward_prices?.POINT?.is_discounted ? (
                <>
                  <Spacer width={2} />
                  <AppText size={12} color={AppColors.dark200} style={{ textDecorationLine: 'line-through' }}>
                    {item.exchange_info?.reward_prices?.POINT?.original_price}
                  </AppText>
                  <Spacer width={2} />
                </>
              ) : null}
            </View>
          </View>
        </View>
      </TouchableOpacity>
    </>
  );

  return (
    <>
      <FlatList
        {...props}
        showsHorizontalScrollIndicator={false}
        data={data}
        renderItem={renderItem}
        numColumns={2}
        keyExtractor={(_item: RewardInfo, idx: number) => `${idx}`}
        ItemSeparatorComponent={() => <Spacer width={12} />}
        ListEmptyComponent={RewardLoadingList}
        ListHeaderComponent={() => <Spacer width={16} />}
        ListFooterComponent={() => <Spacer width={16} />}
        contentContainerStyle={styles.list}
        columnWrapperStyle={styles.row}
      />
      <Spacer height={12} />
    </>
  );
};

export default memo(AssetFrameUI);

const styles = StyleSheet.create({
  voucherItem: {
    flexDirection: 'row',
    alignItems: 'flex-start',
    justifyContent: 'flex-start',
    padding: 8,
    backgroundColor: AppColors.white,
    borderWidth: 1,
    borderColor: AppColors.border,
    borderRadius: 8,
  },
  tray: {
    padding: 16,
    paddingTop: 50,
    backgroundColor: AppColors.transparent,
  },
  text: {
    maxWidth: 97,
  },
  coinContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'flex-start',
  },
  list: {
    paddingHorizontal: 8,
  },
  row: {
    justifyContent: 'space-between',
    marginBottom: 8,
  },
  item: {
    flex: 1,
    marginHorizontal: 4,
    borderRadius: 8,
  },
});

export const RewardLoadingList = () => {
  return (
    <>
      <View style={[styles.row, StyleUtils.flexRow]}>
        <Skeleton style={styles.item} width={154} height={68} />
        <Skeleton style={styles.item} width={154} height={68} />
      </View>
      <View style={[styles.row, StyleUtils.flexRow]}>
        <Skeleton style={styles.item} width={154} height={68} />
        <Skeleton style={styles.item} width={154} height={68} />
      </View>
    </>
  );
};

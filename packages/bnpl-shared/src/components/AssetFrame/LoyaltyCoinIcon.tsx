import * as React from 'react';
import Svg, { Path, Defs, LinearGradient, Stop } from 'react-native-svg';
const LoyaltyCoinIcon = (props: any) => (
  <Svg width={16} height={16} viewBox="0 0 16 16" fill="none" xmlns="http://www.w3.org/2000/svg" {...props}>
    <Path
      d="M7.84288 1.61363C9.64222 1.56383 11.387 2.24758 12.6881 3.51359C13.9901 4.77959 14.7422 6.5225 14.7779 8.35543C14.7779 12.1803 11.7471 15.2878 7.9933 15.3127C6.19679 15.3577 4.45668 14.6711 3.15748 13.407C1.85922 12.14 1.10902 10.399 1.07424 8.56994C1.06484 4.74703 4.09193 1.63757 7.84288 1.61267V1.61363Z"
      fill="url(#paint0_linear_15374_76706)"
    />
    <Path
      d="M7.84288 0.927961C7.84288 0.928226 7.8431 0.928439 7.84337 0.928431C9.64253 0.87877 11.3871 1.56252 12.6881 2.82841C13.9901 4.09441 14.7422 5.83732 14.7779 7.67025C14.7779 11.4951 11.7471 14.6026 7.9933 14.6275C6.19679 14.6725 4.45668 13.9859 3.15748 12.7218C1.85922 11.4549 1.10902 9.71386 1.07424 7.88476C1.06484 4.06201 4.09167 0.952649 7.84241 0.92749C7.84267 0.927488 7.84288 0.9277 7.84288 0.927961Z"
      fill="#FFBB33"
    />
    <Path
      d="M7.86697 2.98322C9.1265 2.94837 10.3479 3.427 11.2586 4.3132C12.17 5.1994 12.6965 6.41944 12.7215 7.70249C12.7215 10.3799 10.5999 12.5552 7.97226 12.5726C6.7147 12.6041 5.49663 12.1234 4.58719 11.2386C3.6784 10.3517 3.15327 9.13302 3.12892 7.85265C3.12234 5.17661 5.2413 2.99998 7.86697 2.98255V2.98322Z"
      fill="url(#paint1_linear_15374_76706)"
    />
    <Path
      fillRule="evenodd"
      clipRule="evenodd"
      d="M10.2884 6.34513C10.2884 6.34513 10.5525 5.07676 10.2884 4.88723C10.0243 4.6977 8.9239 5.39749 8.68914 5.65991C8.44522 5.63987 8.19946 5.63987 7.95553 5.65991C7.69052 5.65536 7.42458 5.67449 7.16323 5.71823C6.92848 5.45581 5.76938 4.75602 5.56397 4.94555C5.35856 5.13507 5.56397 6.40344 5.56397 6.40344C4.92481 6.8809 4.58093 7.65359 4.65429 8.4445C4.81752 9.58074 5.81798 10.4108 6.9725 10.3689H8.93857C10.0903 10.3771 11.0642 9.52334 11.1981 8.38618C11.2724 7.59527 10.9276 6.82259 10.2884 6.34513ZM8.21694 8.05922C8.27471 7.57538 8.6892 7.19998 9.19172 7.19998C9.73367 7.19998 10.1738 7.63643 10.1738 8.17585C10.1738 8.71345 9.73367 9.15082 9.1908 9.15082C8.69562 9.15082 8.28663 8.78635 8.21877 8.31253H7.78777C7.72083 8.78635 7.31093 9.15082 6.81574 9.15082C6.27379 9.15082 5.83454 8.71437 5.83454 8.17494C5.83454 7.63734 6.27379 7.19998 6.81574 7.19998C7.31826 7.19998 7.73275 7.57538 7.79052 8.05922H8.21694ZM9.19209 7.44314C9.59925 7.44314 9.92845 7.77117 9.92845 8.17482C9.92845 8.57939 9.59833 8.90741 9.19209 8.90741C8.78494 8.90741 8.45482 8.57939 8.45482 8.17482L8.45665 8.19851L8.45849 8.18576L8.45757 8.16662C8.46032 7.7657 8.78769 7.44314 9.19118 7.44314H9.19209ZM7.55211 8.17482C7.55211 7.77117 7.2229 7.44314 6.81667 7.44314L6.81575 7.44405C6.40951 7.44405 6.08031 7.77117 6.08031 8.17482C6.08031 8.57848 6.40951 8.9065 6.81575 8.9065C7.22199 8.9065 7.55211 8.57939 7.55211 8.17482ZM6.07388 5.58052L6.03353 5.58599C5.962 5.60604 5.95466 5.65433 5.98034 5.74454L6.00327 5.81196L6.05095 5.93588C6.11698 6.08623 6.17383 6.10901 6.26828 6.08167C6.37099 6.05252 6.34164 5.92131 6.26828 5.77552L6.19767 5.67073C6.14815 5.60239 6.10597 5.56503 6.03261 5.58599L6.07388 5.58052ZM9.40081 5.77556L9.47051 5.67169V5.67078C9.52003 5.60244 9.56313 5.56508 9.63557 5.58604C9.72177 5.61064 9.71443 5.67716 9.66583 5.81201L9.63557 5.8922C9.56221 6.08172 9.50352 6.11088 9.40081 6.08172C9.29811 6.05256 9.32745 5.92135 9.40081 5.77556Z"
      fill="#FFDB1F"
    />
    <Defs>
      <LinearGradient
        id="paint0_linear_15374_76706"
        x1={0.989265}
        y1={2.21417}
        x2={0.0953827}
        y2={20.209}
        gradientUnits="userSpaceOnUse">
        <Stop stopColor="#E25626" />
        <Stop offset={1} stopColor="#FFA200" />
      </LinearGradient>
      <LinearGradient
        id="paint1_linear_15374_76706"
        x1={3.06944}
        y1={3.4036}
        x2={2.44372}
        y2={16}
        gradientUnits="userSpaceOnUse">
        <Stop stopColor="#E25626" />
        <Stop offset={1} stopColor="#FFA200" />
      </LinearGradient>
    </Defs>
  </Svg>
);
export default LoyaltyCoinIcon;

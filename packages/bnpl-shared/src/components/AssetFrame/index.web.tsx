import { getAdvertisingGatewayApp } from 'bnpl-shared/src/api/withBaseUrl';
import { ErrorBoundary } from 'bnpl-shared/src/app/ErrorBoundary';
import { Skeleton } from 'bnpl-shared/src/shared/animated-wrappers/Skeleton';
import { StyleSheet } from 'bnpl-shared/src/shared/styles/StyleSheet';
import { loadRemote } from 'bnpl-shared/src/utils/loadRemote';
import React, { createElement, Suspense, useEffect, useState } from 'react';
import { View } from 'react-native';

const AssetFrame = ({
  assetInventoryId,
  onEmpty,
}: {
  assetInventoryId: string;
  onItemPress?: (url: string) => void;
  onEmpty?: () => void;
}) => {
  const [AssetFrameMF, setAssetFrameMF] = useState<React.ComponentType<any> | null>(null);
  const [isEmpty, setIsEmpty] = useState(false);

  useEffect(() => {
    const fetchRemoteComponent = async () => {
      try {
        const remoteModule = await loadRemote(
          getAdvertisingGatewayApp(), // Remote URL
          'advertising_gateway_app', // Remote container name
          './AssetFrame', // Module name (remote component)
        );

        // Assuming AssetFrame is a default export
        const Component = remoteModule.default;

        setAssetFrameMF(() => Component); // Set the remote component for rendering
      } catch (error) {
        setIsEmpty?.(true);
        console.error('Error loading remote component:', error);
      }
    };
    fetchRemoteComponent();
  }, []);

  if (isEmpty) {
    onEmpty?.();
    return null;
  }

  return (
    <Suspense fallback={<Skeleton width={343} height={200} />}>
      <ErrorBoundary>
        <View style={styles.root}>
          {AssetFrameMF
            ? createElement(
                () => <AssetFrameMF asset_inventory_id={assetInventoryId} onEmpty={() => setIsEmpty(true)} />,
                {},
              )
            : null}
        </View>
      </ErrorBoundary>
    </Suspense>
  );
};

export default AssetFrame;

const styles = StyleSheet.create({
  root: {
    paddingHorizontal: 16,
  },
});

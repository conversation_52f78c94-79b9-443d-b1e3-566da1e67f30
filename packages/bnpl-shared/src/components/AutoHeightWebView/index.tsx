import React, { FC, useEffect, useRef, useState } from 'react';
import WebView from 'react-native-webview';
import { toNumber } from 'lodash';
import { avoidCacheImageUrl } from 'bnpl-shared/src/shared/utils';

export const AutoHeightWebView: FC<{ url: string; injectJsScripts?: string }> = ({ url, injectJsScripts }) => {
  const [height, setHeight] = useState<number>(0);
  const [uri, setUri] = useState<string>(url);
  const loadCountRef = useRef<number>(0);
  const webViewRef = useRef<WebView | null>(null);

  useEffect(() => {
    const id = setInterval(() => {
      if (loadCountRef.current < 3) {
        setUri(avoidCacheImageUrl(url));
        loadCountRef.current++;
      } else {
        if (injectJsScripts) {
          webViewRef?.current?.injectJavaScript(injectJsScripts);
        }
      }
    }, 300);
    return () => {
      clearInterval(id);
    };
  }, []);

  return (
    <WebView
      ref={webViewRef}
      style={{ height }}

      automaticallyAdjustContentInsets={false}
      scrollEnabled={false}
      source={{ uri }}
      javaScriptEnabled={true}
      onMessage={event => {
        if (event.nativeEvent.data) {
          setHeight(toNumber(event.nativeEvent.data));
        }
      }}
      injectedJavaScript={injectJsScripts}
      domStorageEnabled={true}
    />
  );
};

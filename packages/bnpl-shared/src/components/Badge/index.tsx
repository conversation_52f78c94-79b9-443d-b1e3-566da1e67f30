import React from 'react';
import { StyleProp, View, ViewStyle } from 'react-native';
import { AppText } from 'bnpl-shared/src/shared/react-native-customized';
import { StyleSheet } from 'bnpl-shared/src/shared/styles/StyleSheet';
import { AppColors } from 'bnpl-shared/src/constants';

const Badge = ({
    title,
    style,
    variant = 'label',
}: {
    title?: string;
    style?: StyleProp<ViewStyle>;
    variant?: 'label' | 'card';
}) => {
    const badgeContainerStyle = variant === 'label' ? styles.badgeContainerLabel : styles.badgeContainerCard;
    return (
        <>
            <View style={style}>
                <View style={[styles.badgeTitle, badgeContainerStyle]}>
                    <AppText color={AppColors.white} bold size={12} height={16}>
                        {title}
                    </AppText>
                </View>
                {variant === 'label' ? <View style={styles.triangle} /> : null}
            </View>
        </>
    );
};

export default Badge;

const styles = StyleSheet.create({
    badgeTitle: {
        paddingVertical: 1,
        paddingHorizontal: 6,
        backgroundColor: AppColors.green[4],
    },
    badgeContainerLabel: {
        borderBottomLeftRadius: 4,
        borderBottomRightRadius: 0,
        borderTopLeftRadius: 4,
        borderTopRightRadius: 4,
    },
    badgeContainerCard: {
        borderRadius: 4,
    },
    triangle: {
        width: 0,
        height: 0,
        backgroundColor: AppColors.transparent,
        borderRightWidth: 2,
        borderTopWidth: 2,
        borderRightColor: AppColors.transparent,
        borderTopColor: '#007C40',
    },
});

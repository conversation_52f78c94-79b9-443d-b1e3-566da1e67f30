import React, { FC } from 'react';
import { View } from 'react-native';
import { AppText } from 'bnpl-shared/src/shared/react-native-customized';
import { AppColors } from 'bnpl-shared/src/constants';
import { StyleSheet } from 'bnpl-shared/src/shared/styles/StyleSheet';

export enum BadgeVariant {
  GREEN = 0,
  YELLOW = 1,
  RED = 2,
}

//#region BadgeText component
const BadgeText: FC<{ text: string; variant: BadgeVariant; size?: number; bold?: boolean }> = ({
  text,
  variant,
  size = 14,
  bold = false,
}) => {
  switch (variant) {
    case BadgeVariant.RED:
      return (
        <View style={[badgeTextStyles.baseBadge, badgeTextStyles.redBadge]}>
          <AppText size={size} height={size + 4} bold={bold} color={AppColors.red['3']}>
            {text}
          </AppText>
        </View>
      );
    case BadgeVariant.GREEN:
      return (
        <View style={[badgeTextStyles.baseBadge, badgeTextStyles.greenBadge]}>
          <AppText size={size} height={size + 4} bold={bold} color={AppColors.green['1']}>
            {text}
          </AppText>
        </View>
      );
    case BadgeVariant.YELLOW:
      return (
        <View style={[badgeTextStyles.baseBadge, badgeTextStyles.yellowBadge]}>
          <AppText size={size} height={size + 4} bold={bold} color={AppColors.orange['2']}>
            {text}
          </AppText>
        </View>
      );
  }
};

export default BadgeText;

const badgeTextStyles = StyleSheet.create({
  baseBadge: {
    paddingHorizontal: 8,
    paddingVertical: 3,
    borderRadius: 8,
  },
  yellowBadge: {
    backgroundColor: AppColors.warning,
  },
  greenBadge: {
    backgroundColor: AppColors.green['2'],
  },
  redBadge: {
    backgroundColor: AppColors.red['2'],
  },
});
//#endregion

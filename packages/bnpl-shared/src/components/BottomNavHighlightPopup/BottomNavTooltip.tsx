import React, { FC, memo, useEffect, useRef, useState } from 'react';
import { Animated, LayoutRectangle, Platform, StyleProp, TouchableOpacity, View, ViewStyle } from 'react-native';
import { StyleSheet } from 'bnpl-shared/src/shared/styles/StyleSheet';
import { AppText } from 'bnpl-shared/src/shared/react-native-customized';
import { AppColors } from 'bnpl-shared/src/constants';

const TRIANGLE_WIDTH = 10;

const BottomNavTooltip: FC<{
  visible?: boolean;
  content: string | React.ReactElement;
  onTooltipPress?: () => void;
  anchorView?: LayoutRectangle;
  tooltipPosition: 'top' | 'left' | 'right' | 'bottom';
  anchorStyle?: StyleProp<ViewStyle>;
  triangleStyle?: any;
}> = ({ triangleStyle, anchorStyle, tooltipPosition = 'bottom', visible, content, onTooltipPress, anchorView }) => {
  const [translateY] = useState(() => new Animated.Value(0));
  const translateYRef = useRef(translateY);
  translateYRef.current = translateY;

  useEffect(() => {
    // if (tooltipEnable) {
    const animation = Animated.loop(
      Animated.sequence([
        Animated.timing(translateYRef.current, {
          toValue: 8,
          duration: 800,
          useNativeDriver: Platform.OS !== 'web',
        }),
        Animated.timing(translateYRef.current, {
          toValue: 4,
          duration: 800,
          useNativeDriver: Platform.OS !== 'web',
        }),
      ]),
    );
    animation.start();
    return () => animation.stop();
    // }
  }, []);

  if (!anchorView || !visible) {
    return null;
  }

  return (
    <Animated.View style={[styles.tooltip, { transform: [{ translateY }] }, anchorStyle]}>
      <TouchableOpacity testID={'bottom-nav-tooltip'} onPress={onTooltipPress}>
        {tooltipPosition === 'bottom' && <View style={[styles.triangle, styles.triangleTop, triangleStyle]} />}
        <View style={styles.tooltipContainer}>
          {typeof content === 'string' ? <AppText color={AppColors.white}>{content}</AppText> : content}
        </View>
        {tooltipPosition === 'top' && <View style={[styles.triangle, styles.triangleBottom, triangleStyle]} />}
      </TouchableOpacity>
    </Animated.View>
  );
};

export default memo(BottomNavTooltip);

const styles = StyleSheet.create({
  tooltip: {
    position: 'absolute',
  },
  triangle: {
    width: 0,
    height: 0,
    backgroundColor: 'transparent',
    borderStyle: 'solid',
    borderLeftWidth: TRIANGLE_WIDTH,
    borderRightWidth: TRIANGLE_WIDTH,
    borderBottomWidth: 12,
    borderLeftColor: 'transparent',
    borderRightColor: 'transparent',
    borderBottomColor: AppColors.background,
    margin: 0,
    borderWidth: 0,
  },
  triangleTop: {
    marginBottom: -1,
  },
  triangleBottom: {
    transform: [{ rotate: '180deg' }],
    marginTop: -1,
  },
  tooltipContainer: {
    overflow: 'hidden',
    borderRadius: 8,
    justifyContent: 'center',
    alignItems: 'center',
  },
});

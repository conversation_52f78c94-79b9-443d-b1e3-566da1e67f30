import { calculateScaleDimension, StyleSheet } from 'bnpl-shared/src/shared/styles/StyleSheet';
import {
  LayoutChangeEvent,
  LayoutRectangle,
  Platform,
  TouchableOpacity,
  TouchableWithoutFeedback,
  View,
} from 'react-native';
import { Spacer } from 'bnpl-shared/src/components/Spacer';
import React, { FC, useState } from 'react';
import { AppColors } from 'bnpl-shared/src/constants';
import { BottomTabbarIcon, BottomTabbarLabel } from 'bnpl-shared/src/components/BottomTab';
import { getBottomSafe } from 'bnpl-shared/src/utils';
import { StyleUtils, windowWidth } from 'bnpl-shared/src/shared/styles/StyleUtils';
import BottomNavTooltip from './BottomNavTooltip';
import { TabItemConfig } from 'bnpl-shared/src/types';

export const BottomNavHighlightPopup: FC<{
  highlightIndex: number;
  content: string | React.ReactElement;
  onHighlightPress: () => void;
  tabBarConfigs: TabItemConfig[];
  onDismiss: () => void;
}> = ({ onDismiss, tabBarConfigs, onHighlightPress, highlightIndex, content }) => {
  const [tooltipAnchor, setTooltipAnchor] = useState<LayoutRectangle>();

  //false check highlightIndex props
  if (highlightIndex < 0 || highlightIndex >= tabBarConfigs.length) {
    onDismiss();
    return null;
  }

  const highlightTab = tabBarConfigs[highlightIndex];

  const getTriangleStyleByIndex = (index: number) => {
    if (tooltipAnchor) {
      switch (index) {
        case tabBarConfigs.length - 1:
          return {
            marginRight: tooltipAnchor.width / 2 - 12,
            alignSelf: 'flex-end',
          };
        case 2:
          return {
            marginRight: tooltipAnchor.width / 2 - 12,
            alignSelf: 'flex-end',
          };
        default:
        case 1:
          return { marginLeft: tooltipAnchor.width / 2 - 12 };
      }
    }
    return undefined;
  };

  const getAnchorStyleByIndex = (index: number) => {
    if (tooltipAnchor) {
      const bottom = tooltipAnchor.height + (Platform.OS === 'web' ? 10 : 30);
      switch (index) {
        case 0:
          return {
            bottom,
            left: 0,
          };
        case tabBarConfigs.length - 1:
          return {
            bottom,
            right: 0,
          };
        case 1:
          return {
            bottom,
            left: tooltipAnchor.width,
          };
        case 2:
          return {
            bottom,
            right: calculateScaleDimension(tooltipAnchor.width),
          };
      }
    }
    return undefined;
  };

  return (
    <View testID="highlight-popup-content" style={styles.root}>
      <TouchableWithoutFeedback onPress={onDismiss}>
        <View style={styles.overlay} />
      </TouchableWithoutFeedback>
      <Spacer height={16} />
      <View
        testID={'anchor-view'}
        onLayout={(event: LayoutChangeEvent) => {
          setTooltipAnchor(event.nativeEvent.layout);
        }}
        style={{
          position: 'absolute',
          alignItems: 'center',
          bottom: getBottomSafe(),
          left: (windowWidth / tabBarConfigs.length) * highlightIndex,
          width: windowWidth / tabBarConfigs.length,
        }}>
        <TouchableOpacity testID={'highlight-view'} onPress={onHighlightPress} style={[styles.highlightTabbar]}>
          <View style={StyleUtils.centered}>
            <BottomTabbarIcon routeName={highlightTab.path} tintColor={null} focused={false} />
            <BottomTabbarLabel routeName={highlightTab.path} options={{ tintColor: null, focused: false }} />
          </View>
        </TouchableOpacity>
      </View>
      <BottomNavTooltip
        triangleStyle={getTriangleStyleByIndex(highlightIndex)}
        anchorStyle={getAnchorStyleByIndex(highlightIndex)}
        tooltipPosition={'top'}
        visible={true}
        anchorView={tooltipAnchor}
        onTooltipPress={onDismiss}
        content={content}
      />
    </View>
  );
};

const styles = StyleSheet.create({
  root: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  overlay: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    backgroundColor: 'rgba(0,0,0,0.7)',
  },
  highlightTabbar: {
    width: 60,
    height: 60,
    borderRadius: 30,
    backgroundColor: AppColors.background,
  },
});

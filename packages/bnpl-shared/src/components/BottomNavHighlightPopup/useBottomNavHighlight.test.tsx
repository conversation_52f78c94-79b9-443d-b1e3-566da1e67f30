import InfoModal from 'bnpl-shared/src/components/InfoModal';
import { infoModalRef, InfoModalService } from 'bnpl-shared/src/services';
import { useBottomNavHighlight } from 'bnpl-shared/src/components/BottomNavHighlightPopup/useBottomNavHighlight';
import React, { FC } from 'react';
import { PartnerCode, ScreenKey, TAB_BAR_CONFIG } from 'bnpl-shared/src/constants';
import { AppButton } from 'bnpl-shared/src/shared/react-native-customized';
import * as spyRemoteConfigHook from 'bnpl-shared/src/remote_configs/useRemoteConfigs';
import { renderWithRedux } from 'bnpl-shared/src/jest/renderWithRedux';
import { FAKE_NAVIGATION } from 'bnpl-shared/src/jest/constants';
import { fireEvent, waitFor } from '@testing-library/react-native';
import AsyncStorage from '@react-native-community/async-storage';

const DEFAULT_CONFIG = {
  enable: true,
  index: 1,
  title: 'test title',
  description: 'test desc',
  image: 'image_url',
  count: 1,
  delayInHour: 1,
};

const mockGetConfigWithType = jest.fn();

jest.spyOn(spyRemoteConfigHook, 'useRemoteConfigs').mockReturnValue({
  fetchConfigs: jest.fn(),
  getConfig: jest.fn(),
  getConfigWithType: mockGetConfigWithType,
});

const mockValidateSavedConfigTimeExceed = jest.fn();
const mockSaveConfigTime = jest.fn();

jest.mock('bnpl-shared/src/utils/configTimeValidator', () => ({
  configTimeValidator: () => ({
    validateSavedConfigTimeExceed: mockValidateSavedConfigTimeExceed,
    saveConfigTime: mockSaveConfigTime,
  }),
}));

const Subject: FC<{ navigation: any }> = ({ navigation }) => {
  const { checkAndHighlightBottomNav } = useBottomNavHighlight(navigation, PartnerCode.CIMB);
  return (
    <>
      <AppButton onPress={checkAndHighlightBottomNav} title={'show'} />
      <InfoModal ref={infoModalRef} />
    </>
  );
};

describe('useBottomNavHighlight', () => {
  describe('validate highlight popup', () => {
    beforeEach(async () => {
      await AsyncStorage.setItem('bottom-nav-highlight-count', '0');
      mockGetConfigWithType.mockReturnValue(DEFAULT_CONFIG);
      mockValidateSavedConfigTimeExceed.mockReturnValue(true);
    });

    afterEach(() => {
      InfoModalService.hideModal();
    });

    it('show as expected', async () => {
      const { getByText, queryByTestId, queryByText } = renderWithRedux(<Subject navigation={FAKE_NAVIGATION} />);
      fireEvent.press(getByText('show'));
      await waitFor(() => expect(queryByTestId('highlight-popup-content')).toBeTruthy());
      //manually trigger onLayout props callback to set tooltipAnchor
      queryByTestId('anchor-view')?.props?.onLayout({
        nativeEvent: { layout: { x: 0, y: 0, width: 100, height: 100 } },
      });
      expect(queryByText('test title')).toBeTruthy();
      expect(queryByText('test desc')).toBeTruthy();
    });

    it('hide when touch tooltip', async () => {
      const { getByText, queryByTestId, getByTestId } = renderWithRedux(<Subject navigation={FAKE_NAVIGATION} />);
      fireEvent.press(getByText('show'));
      await waitFor(() => expect(queryByTestId('highlight-popup-content')).toBeTruthy());
      queryByTestId('anchor-view')?.props?.onLayout({
        nativeEvent: { layout: { x: 0, y: 0, width: 100, height: 100 } },
      });
      await waitFor(() => expect(queryByTestId('bottom-nav-tooltip')).toBeTruthy());
      fireEvent.press(getByTestId('bottom-nav-tooltip'));
      await waitFor(() => expect(mockSaveConfigTime).toHaveBeenCalled());
      await waitFor(() => expect(queryByTestId('highlight-popup-content')).toBeFalsy());
      expect(await AsyncStorage.getItem('bottom-nav-highlight-count')).toBe('1');
    });

    it('switch tab when touch highlight view', async () => {
      const { getByText, queryByTestId, getByTestId } = renderWithRedux(<Subject navigation={FAKE_NAVIGATION} />);
      fireEvent.press(getByText('show'));
      await waitFor(() => expect(queryByTestId('highlight-popup-content')).toBeTruthy());
      queryByTestId('anchor-view')?.props?.onLayout({
        nativeEvent: { layout: { x: 0, y: 0, width: 100, height: 100 } },
      });
      await waitFor(() => expect(queryByTestId('bottom-nav-tooltip')).toBeTruthy());
      fireEvent.press(getByTestId('highlight-view'));
      await waitFor(() => expect(mockSaveConfigTime).toHaveBeenCalled());
      await waitFor(() => expect(queryByTestId('highlight-popup-content')).toBeFalsy());
      expect(await AsyncStorage.getItem('bottom-nav-highlight-count')).toBe('1');
      expect(FAKE_NAVIGATION.navigate).toHaveBeenCalledWith(TAB_BAR_CONFIG[1].path);
    });
  });

  it('not show when delay time is not exceed', async () => {
    mockGetConfigWithType.mockReturnValue(DEFAULT_CONFIG);
    mockValidateSavedConfigTimeExceed.mockReturnValue(false);
    const { getByText, queryByTestId } = renderWithRedux(<Subject navigation={FAKE_NAVIGATION} />);
    fireEvent.press(getByText('show'));
    expect(queryByTestId('highlight-popup-content')).toBeFalsy();
  });

  it('not show when highlight count is exceed limit', async () => {
    await AsyncStorage.setItem('bottom-nav-highlight-count', '1');
    mockGetConfigWithType.mockReturnValue(DEFAULT_CONFIG);
    mockValidateSavedConfigTimeExceed.mockReturnValue(true);
    const { getByText, queryByTestId } = renderWithRedux(<Subject navigation={FAKE_NAVIGATION} />);
    fireEvent.press(getByText('show'));
    expect(queryByTestId('highlight-popup-content')).toBeFalsy();
  });

  it('not show when config is not valid', async () => {
    mockGetConfigWithType.mockReturnValue(null);
    const { getByText, queryByTestId } = renderWithRedux(<Subject navigation={FAKE_NAVIGATION} />);
    fireEvent.press(getByText('show'));
    expect(queryByTestId('highlight-popup-content')).toBeFalsy();
  });
  it('not show when config.enable = false', async () => {
    mockGetConfigWithType.mockReturnValue({ ...DEFAULT_CONFIG, enable: false });
    const { getByText, queryByTestId } = renderWithRedux(<Subject navigation={FAKE_NAVIGATION} />);
    fireEvent.press(getByText('show'));
    expect(queryByTestId('highlight-popup-content')).toBeFalsy();
  });
  it('not show when config.index is invalid', async () => {
    mockGetConfigWithType.mockReturnValue({ ...DEFAULT_CONFIG, index: -1 });
    const { getByText, queryByTestId } = renderWithRedux(<Subject navigation={FAKE_NAVIGATION} />);
    fireEvent.press(getByText('show'));
    expect(queryByTestId('highlight-popup-content')).toBeFalsy();
    mockGetConfigWithType.mockReturnValue({ ...DEFAULT_CONFIG, index: 10 });
    fireEvent.press(getByText('show'));
    expect(queryByTestId('highlight-popup-content')).toBeFalsy();
  });
});

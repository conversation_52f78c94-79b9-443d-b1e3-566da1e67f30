import { InfoModalService, ModalType } from 'bnpl-shared/src/services';
import { BottomNavHighlightPopup } from 'bnpl-shared/src/components/BottomNavHighlightPopup/index';
import {
  AppColors,
  ConfigKey,
  MULTI_PARTNER_TAB_BAR_CONFIG,
  PartnerCode,
  TAB_BAR_CONFIG,
} from 'bnpl-shared/src/constants';
import { View } from 'react-native';
import { AppImage, AppText } from 'bnpl-shared/src/shared/react-native-customized';
import React, { FC } from 'react';
import { useRemoteConfigs } from 'bnpl-shared/src/remote_configs/useRemoteConfigs';
import { avoidCacheImageUrl } from 'bnpl-shared/src/shared/utils';
import { Spacer } from 'bnpl-shared/src/components/Spacer';
import { configTimeValidator } from 'bnpl-shared/src/utils/configTimeValidator';
import { TimeUnit } from 'bnpl-shared/src/types';
import { StyleSheet } from 'bnpl-shared/src/shared/styles/StyleSheet';
import AsyncStorage from '@react-native-community/async-storage';

type BottomNavHighlightConfig = {
  enable: boolean;
  index: number;
  title: string;
  description: string;
  image: string;
  count: number;
  delayInHour: number;
};

export const useBottomNavHighlight = (navigation: any, partnerCode: PartnerCode) => {
  const { getConfigWithType } = useRemoteConfigs();
  const { saveConfigTime, validateSavedConfigTimeExceed } = configTimeValidator(
    ConfigKey.BOTTOM_NAV_HIGHLIGHT_TIMESTAMP,
  );
  const tabBarConfigs = partnerCode === PartnerCode.CIMB ? TAB_BAR_CONFIG : MULTI_PARTNER_TAB_BAR_CONFIG;
  const highlightConfig = getConfigWithType<BottomNavHighlightConfig>('bottom_nav_highlight');

  const markHighlightShowed = async () => {
    const bottomNavHighlightCount = await AsyncStorage.getItem('bottom-nav-highlight-count');
    saveConfigTime();
    await AsyncStorage.setItem('bottom-nav-highlight-count', (Number(bottomNavHighlightCount) + 1).toString());
    InfoModalService.hideModal();
  };

  const _showHighlightPopup = (config: BottomNavHighlightConfig) => {
    InfoModalService.showModal({
      screen: (
        <BottomNavHighlightPopup
          onDismiss={markHighlightShowed}
          tabBarConfigs={tabBarConfigs}
          onHighlightPress={async () => {
            await markHighlightShowed();
            if (config.index >= 0 && config.index < tabBarConfigs.length) {
              const tabBarConfig = tabBarConfigs[config.index];
              navigation.navigate(tabBarConfig.path);
            }
          }}
          content={<TooltipContent title={config.title} description={config.description} image={config.image} />}
          highlightIndex={config.index}
        />
      ),
      type: ModalType.MODAL,
      options: {
        transparent: true,
      },
    });
  };

  const checkAndHighlightBottomNav = () => {
    (async () => {
      if (
        highlightConfig &&
        highlightConfig.enable &&
        highlightConfig.index >= 0 &&
        highlightConfig.index < tabBarConfigs.length
      ) {
        const isDelayTimeExceed = await validateSavedConfigTimeExceed({
          diffTime: highlightConfig.delayInHour,
          timeUnit: TimeUnit.HOURS,
        });
        const bottomNavHighlightCount = await AsyncStorage.getItem('bottom-nav-highlight-count');
        if (isDelayTimeExceed && Number(bottomNavHighlightCount) < highlightConfig.count) {
          _showHighlightPopup(highlightConfig);
        }
      }
    })();
  };

  return {
    checkAndHighlightBottomNav,
  };
};

const TooltipContent: FC<{ title: string; description: string; image: string }> = ({ title, description, image }) => {
  return (
    <View style={styles.root}>
      <View style={styles.content}>
        <View style={styles.textContent}>
          <AppText bold>{title}</AppText>
          <Spacer height={4} />
          <AppText color={AppColors.text2}>{description}</AppText>
        </View>
        <Spacer width={16} />
        <AppImage width={54} height={54} source={{ uri: avoidCacheImageUrl(image) }} />
      </View>
    </View>
  );
};

const styles = StyleSheet.create({
  textContent: { maxWidth: 150 },
  root: { backgroundColor: AppColors.background },
  content: { flexDirection: 'row', alignItems: 'center', paddingStart: 16, paddingEnd: 8, paddingVertical: 8 },
});

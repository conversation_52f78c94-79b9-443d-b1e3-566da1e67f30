import BottomTabbarIcon from './BottomTabbarIcon';
import React from 'react';
import { renderWithRedux } from 'bnpl-shared/src/jest/renderWithRedux';
import { ScreenKey } from 'bnpl-shared/src/constants';

describe(BottomTabbarIcon.name, () => {
  it('renders', () => {
    const { queryByTestId } = renderWithRedux(
      <BottomTabbarIcon focused={false} routeName={ScreenKey.HomeScreen} tintColor={'red'} />,
    );
    expect(queryByTestId('bottom-tab-icon')).toBeTruthy();
  });
});

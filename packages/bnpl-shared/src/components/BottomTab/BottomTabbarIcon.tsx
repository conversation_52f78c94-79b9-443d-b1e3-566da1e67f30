import { View } from 'react-native';
import React, { FC } from 'react';
import { AppColors, ScreenKey } from 'bnpl-shared/src/constants';
import { AppImage } from 'bnpl-shared/src/shared/react-native-customized';
import { StyleSheet } from 'bnpl-shared/src/shared/styles/StyleSheet';
import { images } from 'bnpl-shared/src/res';

type Props = {
  routeName: string;
  tintColor: string | null;
  focused: boolean;
};

const BottomTabbarIcon: FC<Props> = ({ routeName, tintColor, focused }) => {
  let iconName;
  switch (routeName) {
    case ScreenKey.MPTransactionHistory:
    case ScreenKey.TransactionHistory:
      iconName = focused ? images.IconBottomHistoryActive : images.IconBottomHistoryInactive;
      break;
    case ScreenKey.MPRepayScreen:
    case ScreenKey.Repayment:
      iconName = focused ? images.IconBottomRepayActive : images.IconBottomRepayInactive;
      break;
    case ScreenKey.MPAccountScreen:
    case ScreenKey.AccountScreen:
      iconName = focused ? images.IconBottomProfileActive : images.IconBottomProfileInactive;
      break;
    default:
    case ScreenKey.HomeScreen:
      iconName = focused ? images.IconBottomHomeActive : images.IconBottomHomeInactive;
      break;
  }
  return (
    <>
      <View testID="bottom-tab-icon" style={styles.root}>
        <AppImage width={24} height={24} source={iconName} tintColor={tintColor || AppColors.dark[300]} />
      </View>
    </>
  );
};

export default BottomTabbarIcon;

const styles = StyleSheet.create({
  root: {
    width: 75,
    height: 44,
    justifyContent: 'center',
    alignItems: 'center',
    position: 'relative',
  },
});

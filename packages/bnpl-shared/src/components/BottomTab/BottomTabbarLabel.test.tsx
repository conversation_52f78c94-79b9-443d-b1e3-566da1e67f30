import { render } from '@testing-library/react-native';
import React from 'react';
import BottomTabbarLabel, { getMainTabLabelByRoute } from './BottomTabbarLabel';
import { ScreenKey } from 'bnpl-shared/src/constants';

describe(BottomTabbarLabel.name, () => {
  it('renders', () => {
    const { queryByText } = render(
      <BottomTabbarLabel routeName={ScreenKey.HomeScreen} options={{ tintColor: 'red', focused: true }} />,
    );
    expect(queryByText('Trang chủ')).toBeTruthy();
  });
  it('verify tab label value', () => {
    expect(getMainTabLabelByRoute(ScreenKey.HomeScreen)).toEqual('Trang chủ');
  });
});

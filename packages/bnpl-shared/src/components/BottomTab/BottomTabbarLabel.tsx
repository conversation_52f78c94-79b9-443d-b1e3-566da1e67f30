import React, { FC } from 'react';
import { ScreenKey } from 'bnpl-shared/src/constants';
import { AppText } from 'bnpl-shared/src/shared/react-native-customized';
import { StyleSheet } from 'bnpl-shared/src/shared/styles/StyleSheet';

type Props = {
  routeName: string;
  options: { tintColor: string | null; focused: boolean };
};

export const getMainTabLabelByRoute = (routeName: string): string => {
  switch (routeName) {
    case ScreenKey.MPTransactionHistory:
    case ScreenKey.TransactionHistory:
      return 'Lịch sử';
    case ScreenKey.MPRepayScreen:
    case ScreenKey.Repayment:
      return 'Dư nợ';
    case ScreenKey.MPAccountScreen:
    case ScreenKey.AccountScreen:
      return 'Cá nhân';
    default:
    case ScreenKey.MPHomeScreen:
    case ScreenKey.HomeScreen:
      return 'Trang chủ';
  }
};

const BottomTabbarLabel: FC<Props> = ({ routeName, options }) => {
  return (
    <AppText size={12} style={[tabbarTitleStyles.text, { color: options.tintColor || undefined }]}>
      {getMainTabLabelByRoute(routeName)}
    </AppText>
  );
};

const tabbarTitleStyles = StyleSheet.create({
  text: { marginBottom: 6, alignSelf: 'center' },
});

export default BottomTabbarLabel;

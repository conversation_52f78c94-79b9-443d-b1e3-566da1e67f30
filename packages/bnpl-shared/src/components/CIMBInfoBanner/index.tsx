import React, { FC, useEffect, useState } from 'react';
import { useRemoteConfigs } from 'bnpl-shared/src/remote_configs/useRemoteConfigs';
import get from 'lodash/get';
import { StyleSheet } from 'bnpl-shared/src/shared/styles/StyleSheet';
import { StyleProp, View, ViewStyle } from 'react-native';
import { AppText } from 'bnpl-shared/src/shared/react-native-customized';
import { LinkButton } from 'bnpl-shared/src/shared/LinkButton';
import { AppColors } from 'bnpl-shared/src/constants';
import { CloseButton } from 'bnpl-shared/src/shared/CloseButton';
import { StyleUtils } from 'bnpl-shared/src/shared/styles/StyleUtils';
import AsyncStorage from '@react-native-community/async-storage';
import { toNumber } from 'lodash';
import { getDiffTime } from 'bnpl-shared/src/utils/getDiffTime';
import { ExperimentName, TimeUnit } from 'bnpl-shared/src/types';
import { avoidCacheImageUrl } from 'bnpl-shared/src/shared/utils';
import { launchInAppWebView } from 'bnpl-shared/src/shared/ZaloPayModules';
import { useABTesting } from 'bnpl-shared/src/hooks/useABTesting';

export const CIMB_INFO_BANNER_TIMESTAMP_KEY = 'cimb_info_banner_timestamp';

export const CIMBInfoBanner: FC<{
  style?: StyleProp<ViewStyle>;
  hiddenTimeAmount?: number;
  hiddenTimeUnit?: TimeUnit;
  timestampKey?: string;
  infoBannerConfigKey?: string;
}> = ({
  style,
  hiddenTimeAmount = 24,
  hiddenTimeUnit = TimeUnit.HOURS,
  timestampKey = CIMB_INFO_BANNER_TIMESTAMP_KEY,
  infoBannerConfigKey = 'info_banner'
}) => {
  const [isHidden, setIsHidden] = useState(true);
  const { isInWhiteListRealtime } = useABTesting();

  const { getConfig } = useRemoteConfigs();
  const feeBanner = getConfig(infoBannerConfigKey);
  const content = get(feeBanner, 'content');
  const enable = get(feeBanner, 'enable');
  const url = get(feeBanner, 'url');

  useEffect(() => {
    (async () => {
      const timestamp = await AsyncStorage.getItem(timestampKey);
      const isInfoBannerWhiteList = await isInWhiteListRealtime(ExperimentName.CIMB_INFO_BANNER);
      const shouldShowBanner = content && enable && isInfoBannerWhiteList;
      if (timestamp) {
        const timeSinceHidden = getDiffTime(toNumber(timestamp), new Date().getTime(), hiddenTimeUnit);
        if (timeSinceHidden > hiddenTimeAmount && shouldShowBanner) {
          setIsHidden(false);
        }
      } else {
        setIsHidden(!shouldShowBanner);
      }
    })();
  }, [hiddenTimeAmount, hiddenTimeUnit, timestampKey]);

  if (isHidden) {
    return null;
  }

  return (
    <View testID="cimb-info-banner" style={[styles.root, style]}>
      <View style={styles.row}>
        <AppText style={StyleUtils.flexOne}>{content}</AppText>
        <CloseButton
          onPress={async () => {
            setIsHidden(true);
            await AsyncStorage.setItem(CIMB_INFO_BANNER_TIMESTAMP_KEY, new Date().getTime().toString());
          }}
          size={16}
          tintColor={AppColors.disabled}
          style={styles.closeButton}
        />
      </View>
      <LinkButton
        onPress={() => {
          launchInAppWebView(avoidCacheImageUrl(url));
        }}>
        Tìm hiểu thêm
      </LinkButton>
    </View>
  );
};

const styles = StyleSheet.create({
  root: {
    backgroundColor: AppColors.primary2,
    borderRadius: 8,
    paddingVertical: 10,
    paddingStart: 16,
  },
  row: {
    flexDirection: 'row',
    flex: 1,
    alignItems: 'flex-start',
  },
  closeButton: {
    paddingHorizontal: 16,
  },
});

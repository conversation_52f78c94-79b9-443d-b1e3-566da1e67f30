import React, { FC, useEffect, useRef, useState } from 'react';
import { Platform, ScrollView, StyleProp, Text, TouchableWithoutFeedback, View, ViewStyle } from 'react-native';
import { StyleSheet } from 'bnpl-shared/src/shared/styles/StyleSheet';

export type CarouselProps = {
  autoplay?: boolean;
  delay?: number;
  currentPage?: number;
  style?: StyleProp<ViewStyle>;
  pageStyle?: StyleProp<ViewStyle>;
  contentContainerStyle?: StyleProp<ViewStyle>;
  onAnimateNextPage?: (currentPage: number) => void;
  onPageBeingChanged?: (nextPage: number) => void;
  swipe?: boolean;
  isLooped?: boolean;
  testID?: string;
};

/**
 * Reference from https://github.com/phil-r/react-native-looped-carousel
 */
export const AppCarousel: FC<CarouselProps> = ({
  autoplay = false,
  delay = 5000,
  currentPage = 0,
  style,
  pageStyle,
  contentContainerStyle,
  onAnimateNextPage,
  onPageBeingChanged,
  swipe = true,
  isLooped = false,
  children,
  testID,
}) => {
  const [size, setSize] = useState<{ width: number; height: number }>({ width: 0, height: 0 });
  const [childrenLength] = useState<number>(React.Children.count(children));
  const [innerCurrentPage, setInnerCurrentPage] = useState<number>(currentPage);
  const timerRef = useRef<any>();
  const offsetRef = useRef(0);
  const scrollViewRef = useRef<ScrollView>(null);
  const nextPageRef = useRef(0);

  useEffect(() => {
    if (childrenLength) {
      _setUpTimer();
    }
    return () => {
      _clearTimer();
    };
  }, []);

  const _setUpPages = () => {
    const childrenArr = React.Children.toArray(children);
    const pages = [];
    if (children && childrenArr.length > 1) {
      // add all pages
      pages.push(...childrenArr);
      // We want to make infinite pages structure like this: 1-2-3-1-2
      // so we add first and second page again to the end
      if (isLooped) {
        pages.push(childrenArr[0]);
        pages.push(childrenArr[1]);
      }
    } else if (childrenArr) {
      pages.push(childrenArr[0]);
    } else {
      pages.push(
        <View>
          <Text>You are supposed to add children inside Carousel</Text>
        </View>,
      );
    }
    return pages.map((page, i) => (
      <TouchableWithoutFeedback style={[{ ...size }, pageStyle]} key={`page${i}`}>
        {page}
      </TouchableWithoutFeedback>
    ));
  };

  const _setCurrentPage = (currentPage: number) => {
    setInnerCurrentPage((_prevState: number) => {
      if (onAnimateNextPage) {
        // FIXME: called twice on ios with auto-scroll
        onAnimateNextPage(currentPage);
      }
      return currentPage;
    });
  };

  const _onScrollBegin = () => {
    _clearTimer();
  };

  const _onScrollEnd = (event: any) => {
    const offset = { ...event.nativeEvent.contentOffset };
    const page = _calculateCurrentPage(offset.x);
    _placeCritical(page);
    _setCurrentPage(page);
    _setUpTimer();
  };

  const _onScroll = (event: any) => {
    const currentOffset = event.nativeEvent.contentOffset.x;
    const direction = currentOffset > offsetRef.current ? 'right' : 'left';
    offsetRef.current = currentOffset;
    const nextPage = _calculateNextPage(direction);
    if (nextPageRef.current !== nextPage) {
      nextPageRef.current = nextPage;
      if (onPageBeingChanged) {
        onPageBeingChanged(nextPageRef.current);
      }
    }
  };

  const _clearTimer = () => {
    clearTimeout(timerRef.current);
  };

  const _setUpTimer = () => {
    // only for cycling
    if (autoplay && React.Children.count(children) > 1) {
      _clearTimer();
      timerRef.current = setTimeout(_animateNextPage, delay);
    }
  };

  const _scrollTo = (options: { offset: number; animated: boolean; nofix?: boolean }) => {
    if (scrollViewRef) {
      scrollViewRef.current?.scrollTo?.({ y: 0, x: options.offset, animated: options.animated });

      // Fix bug #50
      if (!options.nofix && Platform.OS === 'android' && !options.animated) {
        scrollViewRef.current?.scrollTo?.({ y: 0, x: options.offset, animated: true });
      }
    }
  };

  const _animateNextPage = () => {
    const nextPage = _normalizePageNumber(innerCurrentPage + 1);

    // prevent from looping
    if (!isLooped && nextPage < innerCurrentPage) {
      return;
    }
    animateToPage(nextPage);
  };

  const animateToPage = (page: number) => {
    const nextPage = _normalizePageNumber(page);
    _clearTimer();
    if (nextPage === innerCurrentPage) {
      // pass
    } else if (nextPage === 0) {
      if (isLooped) {
        // animate properly based on direction
        if (currentPage !== childrenLength - 1) {
          _scrollTo({
            offset: (childrenLength + 2) * size.width,
            animated: false,
            nofix: true,
          });
        }
        _scrollTo({ offset: childrenLength * size.width, animated: true });
      } else {
        _scrollTo({ offset: 0, animated: true });
      }
    } else if (nextPage === 1) {
      // To properly animate from the first page we need to move view
      // to its original position first (not needed if not looped)
      if (currentPage === 0 && isLooped) {
        _scrollTo({ offset: 0, animated: false, nofix: true });
      }
      _scrollTo({ offset: size.width, animated: true });
    } else {
      // Last page is allowed to jump to the first through the "border"
      if (currentPage === 0 && nextPage !== childrenLength - 1) {
        _scrollTo({ offset: 0, animated: false, nofix: true });
      }
      _scrollTo({ offset: nextPage * size.width, animated: true });
    }
    _setCurrentPage(nextPage);
    _setUpTimer();
  };

  const _placeCritical = (page: number) => {
    let offset = 0;
    // if page number is bigger than length - something is incorrect
    if (page < childrenLength) {
      if (page === 0 && isLooped) {
        // in "looped" scenario first page shold be placed after the last one
        offset = childrenLength * size.width;
      } else {
        offset = page * size.width;
      }
    }

    _scrollTo({ offset, animated: false });
  };

  const _normalizePageNumber = (page: number) => {
    if (page === childrenLength) {
      return 0;
    } else if (page > childrenLength) {
      return 1;
    } else if (page < 0) {
      return childrenLength - 1;
    }
    return page;
  };

  const _calculateCurrentPage = (offset: number) => {
    const page = Math.round(offset / size.width);
    return _normalizePageNumber(page);
  };

  const _calculateNextPage = (direction: string) => {
    const ratio = offsetRef.current / size.width;
    const page = direction === 'right' ? Math.ceil(ratio) : Math.floor(ratio);
    return _normalizePageNumber(page);
  };

  return (
    <View
      style={style}
      onLayout={event => {
        const { height, width } = event.nativeEvent.layout;
        setSize({ width, height });
        setTimeout(() => _placeCritical(innerCurrentPage), 0);
      }}>
      <ScrollView
        testID={testID}
        ref={scrollViewRef}
        onScrollBeginDrag={_onScrollBegin}
        onMomentumScrollEnd={_onScrollEnd}
        onScroll={_onScroll}
        alwaysBounceHorizontal={false}
        alwaysBounceVertical={false}
        contentInset={{ top: 0 }}
        automaticallyAdjustContentInsets={false}
        showsHorizontalScrollIndicator={false}
        horizontal
        pagingEnabled
        bounces={false}
        scrollEnabled={swipe}
        contentContainerStyle={[
          styles.horizontalScroll,
          contentContainerStyle,
          {
            width: size.width * (childrenLength + (childrenLength > 1 && isLooped ? 2 : 0)),
            height: size.height,
          },
        ]}>
        {_setUpPages()}
      </ScrollView>
    </View>
  );
};

const styles = StyleSheet.create({
  horizontalScroll: {
    position: 'absolute',
  },
  pageInfoBottomContainer: {
    position: 'absolute',
    bottom: 20,
    left: 0,
    right: 0,
    backgroundColor: 'transparent',
  },
  pageInfoContainer: {
    alignItems: 'center',
    justifyContent: 'center',
    backgroundColor: 'transparent',
  },
  pageInfoPill: {
    width: 80,
    height: 20,
    borderRadius: 10,
    alignItems: 'center',
    justifyContent: 'center',
  },
  pageInfoText: {
    textAlign: 'center',
  },
  bullets: {
    position: 'absolute',
    left: 0,
    right: 0,
    bottom: 10,
    height: 30,
    backgroundColor: 'transparent',
    alignItems: 'center',
    justifyContent: 'center',
    flexDirection: 'row',
  },
  arrows: {
    position: 'absolute',
    left: 0,
    right: 0,
    bottom: 0,
    top: 0,
    backgroundColor: 'transparent',
  },
  arrowsContainer: {
    flex: 1,
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
  },
  chosenBullet: {
    margin: 10,
    width: 10,
    height: 10,
    borderRadius: 20,
    backgroundColor: 'white',
  },
  bullet: {
    margin: 10,
    width: 10,
    height: 10,
    borderRadius: 20,
    backgroundColor: 'transparent',
    borderColor: 'white',
    borderWidth: 1,
  },
});

import React, { FC } from 'react';

import { Linking, Platform, View } from 'react-native';

import { images } from 'bnpl-shared/src/res';
import { UMSource } from 'bnpl-shared/src/types';
import { AppColors } from 'bnpl-shared/src/constants';
import { AppModal } from 'bnpl-shared/src/local/AppModal';
import { getBottomSafe } from 'bnpl-shared/src/utils';
import { Spacer } from 'bnpl-shared/src/components/Spacer';
import { StyleSheet } from 'bnpl-shared/src/shared/styles/StyleSheet';
import { BottomSheetLayout } from 'bnpl-shared/src/shared/BottomSheet/BottomSheetLayout';
import { AppButton, AppImage, AppText } from 'bnpl-shared/src/shared/react-native-customized';
import { closeApp, launchChangePhoneDeeplink } from 'bnpl-shared/src/shared/ZaloPayModules';
import { CIMB_TEL } from 'bnpl-shared/src/constants/PublicUrls';

type RequestChangePhoneDialogProps = {
  visible?: boolean;
  setVisible?: (value: boolean) => void;
};

export const RequestChangePhoneDialog: FC<RequestChangePhoneDialogProps> = ({ visible, setVisible }) => {
  const closeModal = () => setVisible?.(false);

  const handleUsingNewPhone = async () => {
    closeModal();
    //ZLP-iOS & ZPI: need to close our mini-app first, then open the deeplink.
    //If not, it would close the deeplink app instead
    if (Platform.OS !== 'android') {
      closeApp();
    }
    launchChangePhoneDeeplink(UMSource.CHANGE_PHONE_FROM_BNPL);
    //ZLP-Android: if we close our app before the deeplink is opened,
    //the logic which handling deeplink in native code would not be triggered.
    //ref: repo app-android: vn/com/vng/zalopay/react/iap/ZaloPayNativeModule.java:883
    if (Platform.OS === 'android') {
      closeApp();
    }
  };

  const handleContactCIMB = () => {
    closeModal();
    Linking.openURL(`tel:${CIMB_TEL}`);
  };

  const wording =
    'Bạn cần làm 3 bước để dùng số điện thoại mới.\n 1. Nhấn “Thay đổi số điện thoại” và nhập số mới của bạn.\n 2. Liên hệ CIMB để hủy hồ sơ đăng ký hiện tại.\n 3. Quay lại Tài Khoản Trả Sau và làm lại quá trình đăng ký với số mới.';

  return (
    <AppModal transparent testID="change-phone-number-modal" visible={visible} onRequestClose={closeModal}>
      <BottomSheetLayout
        onRequestClose={closeModal}
        content={
          <>
            <AppImage height={125} width={375} source={images.ImageChangePhoneNumber} />
            <View style={styles.root}>
              <Spacer height={16} />
              <AppText size={16} height={20} bold center>
                Bạn muốn sử dụng số mới?
              </AppText>
              <Spacer height={8} />
              <AppText style={{ textAlign: 'left' }} height={18} center color={AppColors.dark300}>
                {wording}
              </AppText>
              <Spacer height={22} />
              <AppButton
                buttonStyle={styles.button}
                title="Thay đổi số điện thoại"
                onPress={handleUsingNewPhone}
                testID="req-change-phone-btn"
              />
              <Spacer height={16} />
              <AppButton
                variant={'outlined'}
                buttonStyle={styles.button}
                title="Liên hệ CIMB"
                onPress={handleContactCIMB}
                testID="contact-cimb-btn"
              />
            </View>
          </>
        }
      />
    </AppModal>
  );
};

const styles = StyleSheet.create({
  root: {
    alignItems: 'center',
    justifyContent: 'center',
    paddingHorizontal: 16,
    paddingBottom: getBottomSafe() + 16,
  },
  button: {
    width: '100%',
  },
  buttonSecondTitle: {
    fontWeight: '400',
  },
  tutorial: {
    paddingHorizontal: 18,
  },
});

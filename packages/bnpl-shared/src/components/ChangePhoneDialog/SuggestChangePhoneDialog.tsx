import React, { useContext, useEffect, useRef, useState } from 'react';

import deepEqual from 'deep-equal';
import { View } from 'react-native';

import { images } from 'bnpl-shared/src/res';
import { ScreenKey } from 'bnpl-shared/src/constants';
import { AppModal } from 'bnpl-shared/src/local/AppModal';
import { getBottomSafe } from 'bnpl-shared/src/utils';
import { Spacer } from 'bnpl-shared/src/components/Spacer';
import { BindingStatus } from 'bnpl-shared/src/types';
import { useAppSelector } from 'bnpl-shared/src/redux/store';
import { StyleSheet } from 'bnpl-shared/src/shared/styles/StyleSheet';
import { NavigationContext } from 'bnpl-shared/src/utils/NavigationContext';
import { bindingPhoneSelector } from 'bnpl-shared/src/redux/bindingSelector';
import { useChangePhoneHandler } from 'bnpl-shared/src/hooks/useChangePhoneHandler';
import { BottomSheetLayout } from 'bnpl-shared/src/shared/BottomSheet/BottomSheetLayout';
import { AppButton, AppImage, AppText } from 'bnpl-shared/src/shared/react-native-customized';
import { getBindingInfoApi } from 'bnpl-shared/src/api/getBindingInfoApi';
import { revealLastFourOnly } from 'bnpl-shared/src/utils/revealLastFourOnly';

export const SuggestChangePhoneDialog = () => {
  const firstTimeRef = useRef(false);
  const navigator = useContext(NavigationContext);
  const [visible, setVisible] = useState(false);
  const { resetDueToChangePhone } = useChangePhoneHandler();
  const bindingStatus = useAppSelector(state => state.binding.bindingStatus);
  const bindingPhone = useAppSelector(bindingPhoneSelector, deepEqual);

  useEffect(() => {
    if (firstTimeRef.current) {
      return;
    }
    if (bindingStatus !== BindingStatus.processing) {
      return;
    }
    if (!bindingPhone.isPhoneChanged) {
      return;
    }
    setVisible(true);
    firstTimeRef.current = true;
  }, [bindingStatus, bindingPhone]);

  const closeModal = () => setVisible(false);

  const handleChangePhone = async () => {
    try {
      await resetDueToChangePhone();
      //fetch new binding info to update with latest data from server
      //which avoid using deprecated phone_number data
      await getBindingInfoApi();
      closeModal();
      navigator.navigate(ScreenKey.OnboardingScreen);
    } catch (error) {}
  };

  const newPhoneNumber = bindingPhone.newPhoneNum ? `${bindingPhone.newPhoneNum}?` : '';

  return (
    <AppModal transparent testID="change-phone-number-modal" visible={visible} onRequestClose={closeModal}>
      <BottomSheetLayout
        onRequestClose={closeModal}
        content={
          <>
            <AppImage height={125} width={375} source={images.ImageChangePhoneNumber} />
            <View style={styles.root}>
              <Spacer height={16} />
              <AppText testID={'title'} size={16} height={20} bold center>
                Bạn muốn dùng SĐT mới {newPhoneNumber}
              </AppText>
              <Spacer height={8} />
              <AppText height={18} center>
                Số điện thoại của bạn trên Zalopay đã thay đổi. Nhấn “Dùng số mới” để cập nhật thông tin đăng ký với số
                mới.
              </AppText>
              <Spacer height={22} />
              <AppButton buttonStyle={styles.button} title="Dùng số mới" onPress={handleChangePhone} />
              <Spacer height={16} />
              <AppButton
                variant="naked"
                onPress={closeModal}
                title={`Giữ số cũ ${revealLastFourOnly(bindingPhone.curPhoneNum)}`}
                titleStyle={styles.buttonSecondTitle}
              />
            </View>
          </>
        }
      />
    </AppModal>
  );
};

const styles = StyleSheet.create({
  root: {
    alignItems: 'center',
    justifyContent: 'center',
    paddingHorizontal: 16,
    paddingBottom: getBottomSafe() + 16,
  },
  button: {
    width: '100%',
  },
  buttonSecondTitle: {
    fontWeight: '400',
  },
});

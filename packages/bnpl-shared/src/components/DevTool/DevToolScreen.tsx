import React, { useCallback, useRef, useState } from 'react';
import { ScrollView, StyleSheet, TouchableWithoutFeedback, View } from 'react-native';
import { TabSelector } from 'bnpl-shared/src/components/TabSelector';
import { NetworkConfig } from './components/NetworkConfig';
import { DevtoolConfig } from './components/DevtoolConfig';
import { windowHeight, windowWidth } from 'bnpl-shared/src/shared/styles/StyleUtils';
import { AppColors } from 'bnpl-shared/src/constants';
import RepaymentScreenConfig from './components/RepaymentScreenConfig';

const TAB_DATA = [
  { tabValue: 0, tabName: 'Network' },
  { tabValue: 1, tabName: 'Setting config' },
  { tabValue: 2, tabName: 'RepaymentScreen' },
];
const TAB_ITEM_WIDTH = TAB_DATA?.length < 4 ? windowWidth / TAB_DATA?.length : 65;
const DevToolScreen = () => {
  const [activeIndex, setActiveTabIndex] = useState(0);
  const tabBarRef = useRef<any>(null);

  const onValueChange = useCallback(index => {
    setActiveTabIndex(index);
  }, []);

  const renderTab = useCallback(() => {
    switch (activeIndex) {
      case 0:
        return <NetworkConfig />;
      case 1:
        return <DevtoolConfig />;
      case 2:
        return <RepaymentScreenConfig />;
      default:
        return <View />;
    }
  }, [activeIndex]);

  return (
    <View testID="devtool-screen" style={{ zIndex: 2 }}>
      <View style={styles.tabbar}>
        <TabSelector
          scrollEnabled={true}
          ref={tabBarRef}
          values={TAB_DATA.map(item => item.tabName)}
          onValueChange={onValueChange}
          tabStyle={[styles.tab, { width: TAB_ITEM_WIDTH }]}
          itemActiveStyle={styles.tabActive}
          itemInactiveStyle={styles.inactiveTab}
          tabTextStyle={styles.tabText}
          inactiveTextColor={AppColors.dark300}
          activeTextColor={AppColors.primary}
          initialIndex={activeIndex}
        />
      </View>
      <TouchableWithoutFeedback>
        <ScrollView style={{ flex: 1 }}>
          <View style={styles.container}>{renderTab()}</View>
        </ScrollView>
      </TouchableWithoutFeedback>
    </View>
  );
};

export default DevToolScreen;

const styles = StyleSheet.create({
  switchWrapper: { alignSelf: 'flex-end', paddingRight: 16, marginTop: 4, flexDirection: 'row', alignItems: 'center' },
  container: {
    backgroundColor: AppColors.background2,
    flex: 1,
    justifyContent: 'space-between',
    maxHeight: windowHeight * 0.8,
  },
  containerStyle: {
    height: windowHeight * 0.5,
  },
  tabText: {
    fontWeight: 'bold',
  },
  tab: {
    borderRadius: 0,
    borderWidth: 0,
    marginRight: 0,
    height: 48,
  },
  inactiveTab: {
    backgroundColor: AppColors.white,
    borderBottomWidth: 1,
    borderColor: AppColors.divider,
  },
  tabActive: {
    backgroundColor: AppColors.white,
    borderWidth: 0,
    borderBottomWidth: 2,
    borderColor: AppColors.primary,
  },
  tabbar: {
    borderTopWidth: 1,
    borderTopColor: AppColors.divider,
  },
});

import React from 'react';
import { useDispatch } from 'react-redux';
import { useAppSelector } from 'bnpl-shared/src/redux/store';
import { AppText } from 'bnpl-shared/src/shared/react-native-customized';
import { View } from 'react-native';
import { styleUtils } from 'bnpl-shared/src/shared/styleUtils';
import { Spacer } from '../../Spacer';
import { setEnableDevMode } from 'bnpl-shared/src/redux/globalReducer';
import ToggleButton from 'bnpl-shared/src/shared/ToggleButton/ToggleButton';

const DevtoolConfig = () => {
  const dispatch = useDispatch();
  const isEnableDevMode = useAppSelector(state => state.globalReducer.isEnableDevMode);

  const onChangeDebugMode = (value: boolean) => {
    dispatch(setEnableDevMode(value));
  };

  return (
    <View>
      <Spacer height={16} />
      <View style={[styleUtils.flexRow, styleUtils.verticallyCentered, { paddingHorizontal: 16 }]}>
        <AppText bold>DebugMode</AppText>
        <Spacer width={16} />
        <ToggleButton onChangeValue={value => onChangeDebugMode(value)} value={isEnableDevMode} />
      </View>
      <Spacer height={16} />
    </View>
  );
};

export { DevtoolConfig };

import { AsyncStorageKey } from 'bnpl-shared/src/constants';
import ToggleButton from 'bnpl-shared/src/shared/ToggleButton/ToggleButton';
import { useStorageState } from 'bnpl-shared/src/shared/utils/useStorageState';
import React, { memo } from 'react';
import { View, Text, StyleSheet } from 'react-native';

interface RepaymentConfigs {
  newUI: boolean;
  enableAnimation: boolean;
}

const RepaymentScreenConfig: React.FC = () => {
  const [configs, setConfigs] = useStorageState<any>(AsyncStorageKey.BNPL_DEVTOOL_REPAYMENTSCREEN_CONFIG, {
    newUI: false,
    enableAnimation: false,
  });

  const updateSetting = (key: keyof RepaymentConfigs, value: boolean) => {
    const newValue = { ...configs, [key]: value };
    setConfigs(newValue);
  };

  return (
    <View style={styles.container}>
      <Text style={styles.label}>Enable New UI</Text>
      <ToggleButton value={configs ? configs.newUI : false} onChangeValue={value => updateSetting('newUI', value)} />
      <Text style={styles.label}>Enable Animation</Text>
      <ToggleButton
        value={configs ? configs.enableAnimation : false}
        onChangeValue={value => updateSetting('enableAnimation', value)}
      />
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    padding: 20,
  },
  label: {
    fontSize: 16,
    marginVertical: 10,
  },
});

export default memo(RepaymentScreenConfig);

import { DevTool } from './';
import { Environment } from 'bnpl-shared/src/shared/environment';
import { renderWithRedux } from 'bnpl-shared/src/jest/renderWithRedux';
import React from 'react';
import { Platform } from 'react-native';
import { store } from 'bnpl-shared/src/redux/store';
import { setEnableDevMode } from 'bnpl-shared/src/redux/globalReducer';
import * as spyRemoteConfigHook from 'bnpl-shared/src/remote_configs/useRemoteConfigs';
import { fireEvent, waitFor } from '@testing-library/react-native';
import InfoModal from 'bnpl-shared/src/components/InfoModal';
import { infoModalRef } from 'bnpl-shared/src/services';

const mockGetConfig = jest.fn();

jest.spyOn(spyRemoteConfigHook, 'useRemoteConfigs').mockReturnValue({
  fetchConfigs: jest.fn(),
  getConfig: mockGetConfig,
  getConfigWithType: jest.fn(),
});

const mockGetUserInfo = jest.fn();

jest.mock('bnpl-shared/src/shared/ZaloPayModules', () => ({
  getUserInfo: jest.fn().mockResolvedValue({ userid: '5678' }),
  getZLPToken: jest.fn(),
}));

describe(DevTool.name, () => {
  beforeEach(() => {
    Platform.OS = 'ios';
    mockGetUserInfo.mockClear();
  });

  it('show dev tool screen as expected', () => {
    store.dispatch(setEnableDevMode(true));
    const { queryByTestId, getByTestId } = renderWithRedux(
      <>
        <DevTool env={Environment.QC_SANDBOX} />
        <InfoModal ref={infoModalRef} />
      </>,
    );
    expect(queryByTestId('devtool-icon')).toBeTruthy();
    fireEvent.press(getByTestId('devtool-icon'));
    expect(queryByTestId('devtool-screen')).toBeTruthy();
  });

  describe('should render', () => {
    it('on web', () => {
      Platform.OS = 'web';
      const { queryByTestId } = renderWithRedux(<DevTool env={Environment.QC_SANDBOX} />);
      expect(queryByTestId('devtool-icon')).toBeTruthy();
    });

    it('when dev mode is not enabled', () => {
      store.dispatch(setEnableDevMode(false));
      const { queryByTestId } = renderWithRedux(<DevTool env={Environment.QC_SANDBOX} />);
      expect(queryByTestId('devtool-icon')).toBeFalsy();
    });

    it('when userid is not in whitelisted on PROD env', () => {
      mockGetUserInfo.mockResolvedValue({ userid: '5678' });
      mockGetConfig.mockReturnValue('["123456"]');
      store.dispatch(setEnableDevMode(true));
      const { queryByTestId } = renderWithRedux(<DevTool env={Environment.PRODUCTION} />);
      expect(queryByTestId('devtool-icon')).toBeFalsy();
    });
  });

  describe('should render', () => {
    it('when dev mode is enabled and env is NOT PROD', () => {
      store.dispatch(setEnableDevMode(true));
      const { queryByTestId } = renderWithRedux(<DevTool env={Environment.QC_SANDBOX} />);
      expect(queryByTestId('devtool-icon')).toBeTruthy();
    });

    it('when userid is in whitelisted on PROD env', async () => {
      mockGetConfig.mockReturnValue('["5678"]');
      store.dispatch(setEnableDevMode(true));
      const { queryByTestId } = renderWithRedux(<DevTool env={Environment.PRODUCTION} />);
      await waitFor(() => expect(queryByTestId('devtool-icon')).toBeTruthy());
    });
  });
});

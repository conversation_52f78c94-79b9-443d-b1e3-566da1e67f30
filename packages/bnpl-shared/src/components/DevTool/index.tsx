import React, { FC, useEffect, useState } from 'react';
import { StyleSheet, TouchableOpacity } from 'react-native';
import DevToolScreen from './DevToolScreen';
import { InfoModalService, ModalType } from 'bnpl-shared/src/services';
import { FloatingView } from 'bnpl-shared/src/shared/animated-wrappers';
import { images } from 'bnpl-shared/src/res';
import { windowWidth } from 'bnpl-shared/src/shared/styles/StyleUtils';
import { useRemoteConfigs } from 'bnpl-shared/src/remote_configs/useRemoteConfigs';
import { Environment } from 'bnpl-shared/src/shared/environment';
import { useAppSelector } from 'bnpl-shared/src/redux/store';
import { getUserInfo } from 'bnpl-shared/src/shared/ZaloPayModules';
import { AppImage } from 'bnpl-shared/src/shared/react-native-customized';

export const DevTool: FC<{ env: Environment }> = ({ env }) => {
  const isEnableDevMode = useAppSelector(state => state.globalReducer.isEnableDevMode);
  const [enable, setEnable] = useState(false);
  const { getConfig } = useRemoteConfigs();
  const initializePosition = React.useMemo(() => ({ x: windowWidth - 300, y: 100 }), []);

  useEffect(() => {
    if (isEnableDevMode) {
      if (env !== Environment.PRODUCTION) {
        setEnable(true);
      } else {
        try {
          const whitelistedUserId: string[] = JSON.parse(getConfig('dev_tool_whitelist') || '');
          if (whitelistedUserId.length) {
            (async () => {
              const userInfo = await getUserInfo();

              if (userInfo.userid && whitelistedUserId.includes(userInfo.userid)) {
                setEnable(true);
              }
            })();
          }
        } catch (e) {
          console.log(e);
        }
      }
    } else {
      setEnable(false);
    }
  }, [isEnableDevMode]);

  if (!enable) {
    return null;
  }

  return (
    <>
      <FloatingView initializePosition={initializePosition} autoSlideToRightSide={true}>
        <TouchableOpacity
          testID={'devtool-icon'}
          style={styles.floatingSettingContainer}
          onPress={() => {
            InfoModalService.showModal({
              screen: <DevToolScreen />,
              type: ModalType.BOTTOM_SHEET,
            });
          }}>
          <AppImage width={48} height={48} source={images.IconAppLogo} style={styles.logo} />
        </TouchableOpacity>
      </FloatingView>
    </>
  );
};
const styles = StyleSheet.create({
  floatingSettingContainer: {
    width: 48,
    height: 48,
    borderRadius: 48,
  },

  logo: {
    flex: 1,
  },
});

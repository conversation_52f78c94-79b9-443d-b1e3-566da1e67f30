import { AppImage } from 'bnpl-shared/src/shared/react-native-customized';
import { StyleSheet, transformScalableFields } from 'bnpl-shared/src/shared/styles/StyleSheet';
import { Colors, StyleUtils } from 'bnpl-shared/src/shared/styles/StyleUtils';
import React from 'react';
import { View, ViewStyle } from 'react-native';
import { images } from 'bnpl-shared/src/res';

type DividerProps = {
  thick?: number;
  color?: string;
  variant?: 'line' | 'dotted';
  style?: ViewStyle;
  marginVertical?: number;
};

export const Divider = ({
  style,
  variant = 'line',
  thick = 1,
  color = Colors.divider,
  marginVertical = 0,
}: DividerProps) => {
  if (variant === 'line') {
    return (
      <View
        style={[
          styles.divider,
          transformScalableFields({
            borderColor: color,
            borderBottomWidth: thick,
            marginVertical,
          }),
          style,
        ]}
      />
    );
  }

  // Currently we don't support color and thickness here.
  return (
    <View
      style={[
        styles.divider,
        StyleUtils.fullWidth,
        transformScalableFields({
          marginVertical,
          height: 2,
        }),
        style,
      ]}>
      <View style={StyleUtils.flexRow}>
        <AppImage source={images.IconDotted} height={2} width={311} />
        <AppImage source={images.IconDotted} height={2} width={311} />
      </View>
    </View>
  );
};

const styles = StyleSheet.create({
  divider: {
    position: 'relative',
    overflow: 'hidden',
  },
});

import { RenewOverDraftBanner } from 'bnpl-shared/src/features/renew_overdraft';
import { LayoutChangeEvent } from 'react-native';
import { DynamicBanner } from 'bnpl-shared/src/components/DynamicBanner/index';
import React from 'react';
import { getRenewOverDraftStatusApi } from 'bnpl-shared/src/api/renew_overdraft';
import { store } from 'bnpl-shared/src/redux/store';
import { ABTestingGroup, ExperimentName } from 'bnpl-shared/src/types';

export class RenewDynamicBanner extends DynamicBanner {
  render = (onLayout?: (event: LayoutChangeEvent) => void): any => {
    return <RenewOverDraftBanner onLayout={onLayout} />;
  };

  shouldRender = async (): Promise<boolean> => {
    const abTesting = store.getState().abTesting;
    const isInWhiteList =
      abTesting[ExperimentName.RENEW_OVERDRAFT]?.toLowerCase() !== ABTestingGroup.Control_Group.toLowerCase();
    const resp = await getRenewOverDraftStatusApi();
    return isInWhiteList && resp.description !== '';
  };
}

import { UpdateNfcDynamicBanner } from './UpdateNfcDynamicBanner';
import { RoutingInfoMapper } from 'bnpl-shared/src/features/multipartner_integration/helpers';
import { FakePartnerCIMB } from 'bnpl-shared/src/jest/fakeData';

const mockGetChosenPartner = jest.fn();

const mockPromptFaceChallengeFlow = jest.fn();

jest.mock('bnpl-shared/src/shared/ZaloPayModules', () => ({
  showLoading: jest.fn(),
  hideLoading: jest.fn(),
  usePromptAuthChallengeFlow: () => ({
    promptAuthChallengeFlow: mockPromptFaceChallengeFlow,
  }),
}));

jest.mock('react-redux', () => ({
  useDispatch: jest.fn(),
}));

jest.mock('bnpl-shared/src/features/multipartner_integration/helpers', () => ({
  ...jest.requireActual('bnpl-shared/src/features/multipartner_integration/helpers'),
  usePartnerData: () => ({
    getChosenPartner: mockGetChosenPartner,
  }),
}));

const mockIsInWhitelist = jest.fn();

jest.mock('bnpl-shared/src/hooks/useABTesting', () => ({
  useABTesting: () => ({
    fetchABTestResult: jest.fn(),
    isInWhiteList: mockIsInWhitelist,
    isInWhiteListRealtime: jest.fn(),
    getABTestResultByKey: jest.fn(),
  }),
}));

jest.mock('bnpl-shared/src/hooks/useUtmHandler', () => ({
  useUtmHandler: () => ({
    utmCampaign: '',
    markUtmCampaignUsed: jest.fn(),
  }),
}));

describe(UpdateNfcDynamicBanner.name, () => {
  beforeAll(() => {
    mockGetChosenPartner.mockReturnValue(new RoutingInfoMapper(FakePartnerCIMB).toPartnerData());
  });

  it('should render UpdateNfcBanner', () => {});
});

import { DynamicBanner } from 'bnpl-shared/src/components/DynamicBanner/index';
import { UpdateNfcBanner } from 'bnpl-shared/src/features/update_nfc/components/UpdateNfcBanner';
import React from 'react';
import { getNfcStatusApi } from 'bnpl-shared/src/api/update_nfc';
import { ABTestingGroup, ExperimentName, NfcStatus } from 'bnpl-shared/src/types';
import { store } from 'bnpl-shared/src/redux/store';
import { setNfcStatus, setNfcStatusCTA } from 'bnpl-shared/src/redux/updateNfcReducer';
import { LayoutChangeEvent } from 'react-native';

export const SHOULD_RENDER_STATUS = [
  NfcStatus.MISSING_DATA,
  NfcStatus.SYNC_TO_PARTNER,
  NfcStatus.WAITING_APPROVAL,
  NfcStatus.REJECTED,
];

export class UpdateNfcDynamicBanner extends DynamicBanner {
  render = (onLayout?: (event: LayoutChangeEvent) => void): any => {
    return <UpdateNfcBanner navigation={this.navigation} onLayout={onLayout} />;
  };

  shouldRender = async (): Promise<boolean> => {
    const abTesting = store.getState().abTesting;
    const isInWhiteList =
      abTesting[ExperimentName.UPDATE_NFC]?.toLowerCase() !== ABTestingGroup.Control_Group.toLowerCase();
    if (!isInWhiteList) {
      return false;
    }
    try {
      const resp = await getNfcStatusApi();
      store.dispatch(setNfcStatus(resp.status));
      if (resp.cta) {
        store.dispatch(setNfcStatusCTA(resp.cta));
      }
      return SHOULD_RENDER_STATUS.includes(resp.status);
    } catch (e: any) {
      return false;
    }
  };
}

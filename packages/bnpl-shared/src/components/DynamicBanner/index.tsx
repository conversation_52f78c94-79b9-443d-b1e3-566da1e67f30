import React, { FC, useEffect, useState } from 'react';
import { windowWidth } from 'bnpl-shared/src/shared/styles/StyleUtils';
import { Animated, Easing, LayoutChangeEvent, Platform, View } from 'react-native';
import { SwiperComponent } from 'bnpl-shared/src/shared/swiper';
import { calculateScaleDimension, StyleSheet } from 'bnpl-shared/src/shared/styles/StyleSheet';
import { useAppSelector } from 'bnpl-shared/src/redux/store';
import { RELOADABLE_COMPONENTS } from 'bnpl-shared/src/redux/reloadableComponentsReducer';

export abstract class DynamicBanner {
  protected navigation: any;
  constructor(navigation?: any) {
    this.navigation = navigation;
  }
  abstract render(onLayout?: (event: LayoutChangeEvent) => void): any;
  abstract shouldRender(): Promise<boolean>;
}

type BannerInstance = new () => DynamicBanner;

const createInstance = (classType: new (navigation: any) => DynamicBanner, navigation: any): DynamicBanner => {
  return new classType(navigation);
};

export const DynamicBannerContainer: FC<{ banners: BannerInstance[]; navigation: any }> = ({ banners, navigation }) => {
  const [renderBanners, setRenderBanners] = useState<DynamicBanner[]>([]);
  const [swiperHeight, setSwiperHeight] = useState<{ [key: number]: number }>();
  const [activeHeight, setActiveHeight] = useState<number>(0);
  const reloadIndicator: number | undefined = useAppSelector(
    state => state.reloadableComponents[RELOADABLE_COMPONENTS.DYNAMIC_BANNER],
  );
  const [animatedHeight] = useState(new Animated.Value(0));

  const padding = renderBanners.length > 1 ? 16 : 0;

  const updateSwiperHeight = (index: number, event: LayoutChangeEvent) => {
    const height = event.nativeEvent?.layout?.height;
    if (!height) {
      return;
    }
    if (!swiperHeight || Object.keys(swiperHeight).length < renderBanners.length) {
      setSwiperHeight(prevState => {
        return { ...prevState, [index]: height + padding };
      });
      if (index === 0) {
        setActiveHeight(height + padding);
      }
    }
  };

  const loadBanner = async (banners: BannerInstance[]) => {
    const tempRenderBanners: DynamicBanner[] = [];
    const mappedBanner = banners.map(banner => createInstance(banner, navigation));
    for (let i = 0; i < mappedBanner.length; i++) {
      const banner = mappedBanner[i];
      const shouldRender = await banner.shouldRender();
      if (shouldRender) {
        tempRenderBanners.push(banner);
      }
    }
    setRenderBanners(tempRenderBanners);
  };

  useEffect(() => {
    (async () => {
      await loadBanner(banners);
    })();
  }, []);

  useEffect(() => {
    (async () => {
      if (reloadIndicator !== undefined) {
        try {
          await loadBanner(banners);
        } catch (e) {}
      }
    })();
  }, [reloadIndicator]);

  if (renderBanners.length === 0) {
    return null;
  }

  const animatedContentHeightChange = (value: number) => {
    Animated.timing(animatedHeight, {
      toValue: value,
      duration: 300,
      useNativeDriver: false,
    }).start();
  };

  let animatedSwiperHeight;
  if (swiperHeight && Object.keys(swiperHeight).length > 0) {
    const keys = [-1];
    const values = [0];
    animatedSwiperHeight = animatedHeight.interpolate({
      inputRange: keys.concat(Object.keys(swiperHeight).map(Number)),
      outputRange: values.concat(Object.values(swiperHeight).map(Number)),
      easing: Easing.in(Easing.ease),
      extrapolate: 'clamp',
    });
  }

  const activeHeightWithoutPadding = activeHeight - padding;

  return (
    <Animated.View style={{ height: animatedSwiperHeight || 0, marginBottom: 16 }}>
      <SwiperComponent
        loop={false}
        webStyle={{ width: windowWidth - 60, height: activeHeightWithoutPadding }}
        appPaginationStyle={styles.pagingStyle}
        suppressTitleWarning
        slidesPerView={1}
        onActiveIndexChange={(index: number) => {
          if (swiperHeight) {
            setActiveHeight(swiperHeight[index]);
            animatedContentHeightChange(index);
          }
        }}
        showsPagination={true}
        spaceBetween={Platform.OS === 'web' ? 32 : 0}
        scrollEnabled>
        {renderBanners.map((banner, index) => (
          <View key={index} style={styles.sliderContainer}>
            {banner.render((event: LayoutChangeEvent) => {
              updateSwiperHeight(index, event);
            })}
          </View>
        ))}
      </SwiperComponent>
    </Animated.View>
  );
};

const styles = StyleSheet.create({
  pagingStyle: { bottom: 0 },
  sliderContainer: { marginStart: Platform.OS === 'web' ? 0 : 16 },
});

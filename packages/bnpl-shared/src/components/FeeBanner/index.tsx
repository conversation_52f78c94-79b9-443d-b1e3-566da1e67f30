import React, { FC, useContext, useEffect, useState } from 'react';
import { useRemoteConfigs } from 'bnpl-shared/src/remote_configs/useRemoteConfigs';
import get from 'lodash/get';
import { StyleSheet } from 'bnpl-shared/src/shared/styles/StyleSheet';
import { Platform, StyleProp, View, ViewStyle } from 'react-native';
import { AppText } from 'bnpl-shared/src/shared/react-native-customized';
import { LinkButton } from 'bnpl-shared/src/shared/LinkButton';
import { AppColors, ScreenKey } from 'bnpl-shared/src/constants';
import { CloseButton } from 'bnpl-shared/src/shared/CloseButton';
import { StyleUtils } from 'bnpl-shared/src/shared/styles/StyleUtils';
import AsyncStorage from '@react-native-community/async-storage';
import { toNumber } from 'lodash';
import { getDiffTime } from 'bnpl-shared/src/utils/getDiffTime';
import { TimeUnit } from 'bnpl-shared/src/types';
import { useNavigation } from 'bnpl-shared/src/shared/navigation/useNavigation';
import { NavigationContext } from 'bnpl-shared/src/utils/NavigationContext';
import { avoidCacheImageUrl } from 'bnpl-shared/src/shared/utils';
import { ContentType } from 'bnpl-shared/src/screens/Webview/Webview';
import { launchDeepLink, launchInAppWebView } from 'bnpl-shared/src/shared/ZaloPayModules';

export const FeeBanner: FC<{
  style?: StyleProp<ViewStyle>;
  hiddenTimeAmount?: number;
  hiddenTimeUnit?: TimeUnit;
}> = ({ style, hiddenTimeAmount = 24, hiddenTimeUnit = TimeUnit.HOURS }) => {
  const [isHidden, setIsHidden] = useState(true);
  const { getConfig } = useRemoteConfigs();
  const feeBanner = getConfig('fee_banner');
  const content = get(feeBanner, 'content');
  const enable = get(feeBanner, 'enable');
  const url = get(feeBanner, 'url');

  useEffect(() => {
    (async () => {
      const timestamp = await AsyncStorage.getItem('fee_banner_timestamp');
      const shouldShowBanner = content && enable;
      if (timestamp) {
        const timeSinceHidden = getDiffTime(toNumber(timestamp), new Date().getTime(), hiddenTimeUnit);
        if (timeSinceHidden > hiddenTimeAmount && shouldShowBanner) {
          setIsHidden(false);
        }
      } else {
        setIsHidden(!shouldShowBanner);
      }
    })();
  }, [hiddenTimeAmount, hiddenTimeUnit]);

  if (isHidden) {
    return null;
  }

  return (
    <View testID="fee-banner" style={[styles.root, style]}>
      <View style={styles.row}>
        <AppText style={StyleUtils.flexOne}>{content}</AppText>
        <CloseButton
          onPress={async () => {
            setIsHidden(true);
            await AsyncStorage.setItem('fee_banner_timestamp', new Date().getTime().toString());
          }}
          size={16}
          tintColor={AppColors.disabled}
          style={styles.closeButton}
        />
      </View>
      <LinkButton
        onPress={() => {
          launchInAppWebView(avoidCacheImageUrl(url));
        }}>
        Tìm hiểu thêm
      </LinkButton>
    </View>
  );
};

const styles = StyleSheet.create({
  root: {
    backgroundColor: AppColors.primary2,
    borderRadius: 8,
    paddingVertical: 10,
    paddingStart: 16,
  },
  row: {
    flexDirection: 'row',
    flex: 1,
    alignItems: 'flex-start',
  },
  closeButton: {
    paddingHorizontal: 16,
  },
});

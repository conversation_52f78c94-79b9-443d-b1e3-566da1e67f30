import React, { FC, useEffect } from 'react';
import { PDFDocumentProxy } from 'pdfjs-dist/types/src/display/api';
import { captureMessage } from 'bnpl-shared/src/shared/sentry';
import { Severity } from '@sentry/types';

const pdfjsLib = require('pdfjs-dist');
pdfjsLib.GlobalWorkerOptions.workerSrc =
  'https://simg.zalopay.com.vn/fs/bnpl/document/pdfjs-dist@3.9.179_pdf.worker.min.js';

export const PdfFileWebViewer: FC<{ fileUrl: string; options?: { headers?: any; password?: string } }> = ({
  fileUrl,
  options,
}) => {
  const [pdfResource, setPdfResource] = React.useState<PDFDocumentProxy>();
  useEffect(() => {
    if (fileUrl) {
      const documentLoadTask = pdfjsLib.getDocument({
        url: fileUrl,
        httpHeaders: options?.headers,
        password: options?.password,
      });
      documentLoadTask.promise
        .then((pdf: PDFDocumentProxy) => {
          setPdfResource(pdf);
        })
        .catch((e: any) => {
          captureMessage('Fail to load file', {
            level: Severity.Error,
            tags: {
              scope: 'file_viewer',
            },
            extra: {
              ...e,
              file: { type: 'pdf', url: fileUrl },
            },
          });
        });
    }
  }, [fileUrl]);

  const renderPages = (pdfResource: PDFDocumentProxy) => {
    if (!pdfResource.numPages) {
      return null;
    }
    return Array(pdfResource.numPages)
      .fill(0)
      .map((v, i) => <PdfPage key={i} pdf={pdfResource} index={i + 1} />);
  };

  if (!pdfResource) {
    return null;
  }

  return <div>{renderPages(pdfResource)}</div>;
};

const PdfPage: FC<{ pdf: PDFDocumentProxy; index: number }> = ({ index, pdf }) => {
  useEffect(() => {
    if (pdf) {
      pdf.getPage(index).then((page: any) => {
        const desiredWidth = document.documentElement.clientWidth;
        const viewport = page.getViewport({ scale: 1 });
        const scale = desiredWidth / viewport.width;
        const scaledViewport = page.getViewport({ scale: scale });
        const canvas = document.getElementById(`pdf-page-${index}`) as any;
        const context = canvas.getContext('2d');
        canvas.height = scaledViewport.height;
        canvas.width = scaledViewport.width;
        const renderContext = {
          canvasContext: context,
          viewport: scaledViewport,
        };
        page.render(renderContext);
      });
    }
  }, [pdf, index]);

  return <canvas id={`pdf-page-${index}`} width="670" height="870" />;
};

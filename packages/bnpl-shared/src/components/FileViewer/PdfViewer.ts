const renderPdfHtml = (source: string) => {
  return `
  <head>
  <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0" />
  <script src="https://cdn.jsdelivr.net/npm/pdfjs-dist@2.14.305/legacy/build/pdf.min.js"></script>
</head>
<body>
  <style>
    .canvas {
      width: 100%;
    }
    body {
      padding: 0;
      margin: 0;
    }
  </style>

  <div id="canvases"></div>

  <!-- PDF renderer -->
  <script>
    document.documentElement.addEventListener(
      'gesturestart',
      function (event) {
        event.preventDefault();
      },
      false,
    );
    const createDocumentSizeMessage = () =>
      JSON.stringify({
        type: 'DOCUMENT_SIZE',
        height: document.getElementById('canvases').scrollHeight,
        width: document.getElementById('canvases').scrollWidth,
      });

    const createErrorMessage = error => JSON.stringify({ type: 'ERROR', error: { message: error.message } });

    const renderPage = (pdf, page) => {
      var pageNumber = page;
      return new Promise(resolve => {
        pdf.getPage(pageNumber).then(function (page) {
          var viewport = page.getViewport({ scale: 2 });
          // Prepare canvas using PDF page dimensions
          var canvas = document.getElementById('canvas-' + pageNumber);
          var context = canvas.getContext('2d');
          canvas.height = viewport.height;
          canvas.width = viewport.width;

          // Render PDF page into canvas context
          var renderContext = {
            canvasContext: context,
            viewport: viewport,
          };
          var renderTask = page.render(renderContext);
          renderTask.promise.then(resolve);
        });
      });
    };

    const createCanvases = amount => {
      for (let i = 1; i <= amount; i++) {
        const canvas = document.createElement('CANVAS');
        canvas.id = 'canvas-' + i;
        canvas.className = 'canvas';
        document.getElementById('canvases').appendChild(canvas);
      }
    };

    function renderPdf(dataURL) {
      const [, base64] = dataURL.split(';base64,');
      var binary = window.atob(base64);
      var length = binary.length;
      var bytes = new Uint8Array(length);
      for (var i = 0; i < length; i++) {
        bytes[i] = binary.charCodeAt(i);
      }
      var pdfjsLib = window['pdfjs-dist/build/pdf'];
      pdfjsLib.GlobalWorkerOptions.workerSrc = 'https://cdn.jsdelivr.net/npm/pdfjs-dist@2.14.305/legacy/build/pdf.worker.js';
      var loadingTask = pdfjsLib.getDocument(bytes);
      loadingTask.promise.then(function (pdf) {
        const numPages = pdf._pdfInfo.numPages;
        createCanvases(numPages);
        Promise.all(Array.from({ length: numPages }).map((_, i) => renderPage(pdf, i + 1))).then(onRendered);
      }, onError);
    }

    function sendMessageToRN(data) {
      if (window.ReactNativeWebView) {
        window.ReactNativeWebView.postMessage(data);
      } else if (window.parent) {
        window.parent.postMessage(data, '*');
      } else {
        window.postMessage(data, '*');
      }
    }
  </script>

  <!-- Params & handlers -->
  <script>
    const onRendered = () => {
      sendMessageToRN(createDocumentSizeMessage());
    };

    const onError = error => {
      sendMessageToRN(createErrorMessage(error));
    };

    /**
     * We stopped passing URL, instead passing dataURL because of CORS.
     */
    const doRender = dataURL => renderPdf(dataURL);
    doRender('${source}');
  </script>  
</body>
  `;
};

export default renderPdfHtml;

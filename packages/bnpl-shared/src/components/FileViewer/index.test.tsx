import { render, waitFor } from '@testing-library/react-native';
import { createMockFetchResponse } from 'bnpl-shared/src/shared/jest/helpers';
import React from 'react';
import { FileViewer } from './index';

describe(FileViewer.name, () => {
  it('renders the HTML string', async () => {
    (global.fetch as jest.Mock).mockImplementationOnce((url: string) => {
      if (url === 'https://www.example.com') {
        return createMockFetchResponse('<div>This is a sample HTML string</div>');
      }
    });
    const { findByTestId } = render(<FileViewer url="https://www.example.com" type="html" />);
    const webview = await findByTestId('webview-html');
    expect(webview.props.source.html).toContain('<div>This is a sample HTML string</div>');
    webview.props.onMessage({ nativeEvent: { data: 'asdf' } });
    webview.props.onMessage({ nativeEvent: { data: '{"type":"DOCUMENT_SIZE","height":1,"width":1}' } });
    webview.props.onMessage({ nativeEvent: { data: '{"type":"DOCUMENT_SIZE","height":"asdf","width":1}' } });
  });

  it('renders the PDF file', async () => {
    (global.fetch as jest.Mock).mockImplementationOnce((url: string) => {
      if (url === 'https://www.example.com/file.pdf') {
        return createMockFetchResponse('awelfhalwej is a random PDF binary string');
      }
    });
    const { findByTestId } = render(<FileViewer url="https://www.example.com/file.pdf" type="pdf" />);
    const webview = await findByTestId('webview-pdf');
    webview.props.onMessage({ nativeEvent: { data: 'asdf' } });
  });

  it('renders', async () => {
    (global.fetch as jest.Mock).mockImplementationOnce((url: string) => {
      if (url === 'https://www.example.com') {
        return createMockFetchResponse('<div>This is a sample HTML string</div>');
      }
    });
    const { getByTestId } = render(<FileViewer url="https://www.example.com" type="html" />);
    await waitFor(() => getByTestId('file-viewer-root').props.onLayout({ nativeEvent: { layout: { width: 100 } } }));
  });

  it('throws error if cannot fetch the file', async () => {
    (global.fetch as jest.Mock).mockImplementationOnce((url: string) => {
      if (url === 'https://www.example.com') {
        return createMockFetchResponse('<div>This is a sample HTML string</div>', false);
      }
    });
    render(<FileViewer url="https://www.example.com" type="html" />);
  });
});

import { StyleSheet } from 'bnpl-shared/src/shared/styles/StyleSheet';
import { HTMLBuilder } from 'bnpl-shared/src/shared/webview';
import React, { FC, useEffect, useRef, useState } from 'react';
import { View } from 'react-native';
import { WebView } from 'react-native-webview';
import renderPdfHtml from './PdfViewer';
import { captureMessage } from 'bnpl-shared/src/shared/sentry';
import { Severity } from '@sentry/types';

const WEBVIEW_SIZING_SNIPPET = `
  // Disable user pinch to zoom gesture
  const meta = document.createElement('meta');
  meta.setAttribute('content', 'width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=0');
  meta.setAttribute('name', 'viewport');
  document.getElementsByTagName('head')[0].appendChild(meta);

  // Helper to send JSON string message back to React Native.
  function sendMessageToRN(e) {
    window.ReactNativeWebView ? window.ReactNativeWebView.postMessage(e) : (window.parent || window).postMessage(e, '*');
  }

  function createDocumentSizeMessage() { return JSON.stringify({ type: 'DOCUMENT_SIZE', height: document.documentElement.scrollHeight, width: document.documentElement.scrollWidth }) };
  sendMessageToRN(createDocumentSizeMessage())
  true;
`;

type DocumentSizeEvent = {
  type: 'DOCUMENT_SIZE';
  height: number;
  width: number;
};

type FileViewerErrorEvent = {
  type: 'ERROR';
  error: {
    message: string;
  };
};

type FileViewerEvent = DocumentSizeEvent | FileViewerErrorEvent;

export const FileViewer: FC<{ url: string; type: 'html' | 'pdf' }> = ({ url, type }) => {
  const styles = fileViewerStyles;
  const [height, setHeight] = useState<number>(500);
  const [width, setWidth] = useState<number>(0);
  const ref = useRef<WebView>(null);

  const [file, setFile] = useState<string>();
  useEffect(() => {
    (async () => {
      try {
        // Wow, fetching file in RN layer doesn't affected by CORS.
        // So I decided to bring file fetching logic here instead of putting it in the viewers.
        const response = await fetch(url);
        if (response.ok) {
          if (type === 'html') {
            setFile(new HTMLBuilder(await response.text()).withAppend(WEBVIEW_SIZING_SNIPPET).build());
            return;
          }
          const buffer = await response.blob();
          setFile(await blobToBase64(buffer));
        } else {
          // Temporary trigger a crash here.
        }
      } catch (e: any) {
        captureMessage('Fail to load file url', {
          level: Severity.Error,
          tags: {
            scope: 'file_viewer',
          },
          extra: {
            ...e,
            file: { type, url },
          },
        });
      }
    })();
  }, [url, type]);

  const handleEvent = (data: string) => {
    let event: FileViewerEvent;
    try {
      event = JSON.parse(data);
    } catch (e) {
      // We simple return if event is not a valid JSON string.
      return;
    }

    if (event.type === 'DOCUMENT_SIZE') {
      const nextHeight = (width * event.height) / event.width;
      setHeight(isNaN(nextHeight) ? 0 : nextHeight);
    }
    if (event.type === 'ERROR') {
      console.log(event.error.message);
    }
  };

  if (!file) {
    return null;
  }

  return (
    // This View is not redundant. On web, WebView doesn't have onLayout. That's sad.
    <View
      testID="file-viewer-root"
      onLayout={event => setWidth(event.nativeEvent.layout.width)}
      style={[styles.container, { height }]}>
      {type === 'pdf' && (
        <WebView
          testID="webview-pdf"
          scrollEnabled={false}
          source={{ html: renderPdfHtml(file) }}
          style={styles.root}
          onMessage={e => handleEvent(e.nativeEvent.data)}
        />
      )}
      {type === 'html' && (
        <WebView
          ref={ref}
          testID="webview-html"
          scrollEnabled={false}
          source={{ html: file }}
          style={[styles.root, { height }]}
          onMessage={e => handleEvent(e.nativeEvent.data)}
        />
      )}
    </View>
  );
};

const fileViewerStyles = StyleSheet.create({
  root: { borderRadius: 8 },
  container: { overflow: 'hidden' },
});

/**
 * We pretty sure there is no error in this stage...
 */
const blobToBase64 = (blob: any): Promise<string> => {
  return new Promise(resolve => {
    var reader = new FileReader();
    reader.onloadend = function () {
      resolve(String(reader.result));
    };
    reader.readAsDataURL(blob);
  });
};

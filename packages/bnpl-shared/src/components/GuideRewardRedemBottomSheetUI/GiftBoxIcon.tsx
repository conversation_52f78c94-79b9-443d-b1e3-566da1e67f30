import * as React from 'react';
import Svg, { Path } from 'react-native-svg';

const GiftBoxIcon = (props: any) => (
  <Svg width={40} height={40} viewBox="0 0 40 40" fill="none" xmlns="http://www.w3.org/2000/svg" {...props}>
    <Path
      fillRule="evenodd"
      clipRule="evenodd"
      d="M23.2844 4.46919L20 9.75482L16.7156 4.46919C14.3395 0.645207 8.68016 0.82961 6.5633 4.80417C5.19046 7.38179 5.96663 10.4989 8.22333 12.1771C5.6789 13.5353 3.91452 16.1332 3.66814 19.1667C3.62768 19.6648 3.592 20.1658 3.56153 20.6698H18.75V12.1753H21.25V20.6698H36.438C36.4076 20.1658 36.3719 19.6648 36.3314 19.1667C36.085 16.1333 34.3208 13.5355 31.7765 12.1773C34.0333 10.499 34.8096 7.38187 33.4367 4.80417C31.3198 0.82961 25.6605 0.645207 23.2844 4.46919ZM25.4078 5.78864C26.7686 3.59873 30.0202 3.70759 31.2301 5.97939C32.1084 7.62838 31.4409 9.67811 29.7373 10.5024L28.32 11.1882C26.4433 11.0527 24.4175 10.9719 22.2066 10.9405L25.4078 5.78864ZM14.5922 5.78864L17.7934 10.9405C15.5825 10.9719 13.5566 11.0527 11.6799 11.1881L10.2627 10.5024C8.55909 9.67811 7.8916 7.62838 8.76985 5.97939C9.97981 3.70759 13.2314 3.59873 14.5922 5.78864Z"
      fill="#FF8D00"
    />
    <Path
      d="M36.5438 23.1698H21.25V38.4768C23.8484 38.4568 26.1964 38.3727 28.3482 38.2166C32.6416 37.9052 35.9852 34.5028 36.3314 30.2402C36.4781 28.4336 36.5621 26.5897 36.5621 24.7033C36.5621 24.189 36.5558 23.6779 36.5438 23.1698Z"
      fill="#FF8D00"
    />
    <Path
      d="M18.75 38.4768V23.1698H3.45579C3.44374 23.6779 3.4375 24.189 3.4375 24.7033C3.4375 26.5897 3.52142 28.4336 3.66815 30.2402C4.01435 34.5028 7.35793 37.9052 11.6514 38.2166C13.8033 38.3727 16.1514 38.4568 18.75 38.4768Z"
      fill="#FF8D00"
    />
  </Svg>
);
export default GiftBoxIcon;

import React, { useEffect, useRef } from 'react';
import { images } from 'bnpl-shared/src/res';
import { AppImage, AppText } from 'bnpl-shared/src/shared/react-native-customized';
import { Animated, Easing, Platform, TouchableOpacity, View } from 'react-native';
import { Spacer } from 'bnpl-shared/src/components/Spacer';
import { StyleSheet, transformScalableFields } from 'bnpl-shared/src/shared/styles/StyleSheet';
import { getBottomSafe } from 'bnpl-shared/src/utils';
import { AppColors } from 'bnpl-shared/src/constants';
import GiftBoxIcon from './GiftBoxIcon';

const PAYMENT_GUIDE = 'https://simg.zalopay.com.vn/fs/bnpl/animation/payment-guide.json';

const GuideRewardRedemBottomSheetUI = ({ onClose }: { onClose: () => void }) => {
  const translateY = useRef(new Animated.Value(70)).current;
  const customEasing = Easing.bezier(0.22, 1, 0.36, 1); // easeOutQuint

  const animate = () => {
    Animated.timing(translateY, {
      toValue: 0,
      duration: 500,
      easing: customEasing,
      useNativeDriver: Platform.OS !== 'web' ? true : false,
    }).start();
  };

  useEffect(() => {
    animate();
  }, []);

  return (
    <Animated.View style={[styles.content, { transform: [{ translateY }] }]}>
      <TouchableOpacity testID={'close-button'} style={styles.closeButton} onPress={onClose}>
        <AppImage width={16} height={16} source={images.IconClose} tintColor={AppColors.dark300} />
      </TouchableOpacity>
      <Spacer height={24} />
      <View style={styles.giftIcon}>
        <GiftBoxIcon />
      </View>
      <Spacer height={8} />
      <View style={styles.text}>
        <AppText bold size={16} height={20}>
          Thanh toán bằng nguồn tiền Tài khoản trả sau để mở khoá quà tặng
        </AppText>
      </View>
      <Spacer height={12} />
      <View style={styles.guide}>
        <AppImage
          style={transformScalableFields({ width: 308, height: 333 })}
          source={{ uri: PAYMENT_GUIDE }}
          width={308}
          height={333}
        />
      </View>
    </Animated.View>
  );
};

export default GuideRewardRedemBottomSheetUI;

const styles = StyleSheet.create({
  content: {
    paddingBottom: 16 + getBottomSafe(),
    backgroundColor: AppColors.background,
    borderRadius: 20,
    position: 'relative',
  },
  text: {
    paddingHorizontal: 24,
    alignItems: 'center',
  },
  button: {
    width: 340,
    marginTop: 16,
  },
  actionButton: {
    width: '100%',
    marginTop: 16,
  },
  closeButton: {
    zIndex: 1,
    position: 'absolute',
    backgroundColor: AppColors.background2,
    padding: 8,
    top: 24,
    right: 24,
    borderRadius: 99,
  },
  giftIcon: {
    paddingHorizontal: 24,
  },
  guide: {
    alignItems: 'center',
  },
});

import React from 'react';
import { render, fireEvent } from '@testing-library/react-native';
import { AlertComponent } from './AlertModal';

describe('AlertModal', () => {
  const mockOnBackdropPress = jest.fn();
  const mockOnConfirmPress = jest.fn();
  const mockOnCancelPress = jest.fn();

  const defaultProps = {
    title: 'Test Title',
    message: 'Test Message',
    onBackdropPress: mockOnBackdropPress,
    confirmButton: {
      title: 'Confirm',
      onPress: mockOnConfirmPress,
    },
    cancelButton: {
      title: 'Cancel',
      onPress: mockOnCancelPress,
    },
  };

  afterEach(() => {
    jest.clearAllMocks();
  });

  test('renders correctly', () => {
    const { getByText } = render(<AlertComponent {...defaultProps} />);

    const titleElement = getByText('Test Title');
    const messageElement = getByText('Test Message');
    const confirmButton = getByText('Confirm');
    const cancelButton = getByText('Cancel');

    expect(titleElement).toBeTruthy();
    expect(messageElement).toBeTruthy();
    expect(confirmButton).toBeTruthy();
    expect(cancelButton).toBeTruthy();
  });

  test('calls appropriate functions on button press', () => {
    const { getByTestId, getByText } = render(<AlertComponent {...defaultProps} />);

    const confirmButton = getByTestId('confirm');
    const cancelButton = getByText('Cancel');

    fireEvent.press(confirmButton);
    fireEvent.press(cancelButton);

    expect(mockOnConfirmPress).toHaveBeenCalledTimes(1);
    expect(mockOnCancelPress).toHaveBeenCalledTimes(1);
    expect(mockOnBackdropPress).toHaveBeenCalledTimes(2);
  });
});

import React from 'react';
import { View, TouchableOpacity, Platform } from 'react-native';
import { AlertModalHandle, AlertParams } from 'bnpl-shared/src/services';
import { AppButton, AppModal, AppText } from 'bnpl-shared/src/shared/react-native-customized';
import { styleUtils } from 'bnpl-shared/src/shared/styleUtils';
import { StyleSheet } from 'bnpl-shared/src/shared/styles/StyleSheet';
import { AppColors } from 'bnpl-shared/src/constants';

type AlertModalParams = {
  title: string;
  message: string;
  onBackdropPress: () => void;
  confirmButton?: {
    title: string;
    onPress: () => void;
  };
  cancelButton?: {
    title: string;
    onPress: () => void;
  };
};

export const AlertComponent = (props: AlertModalParams) => {
  const { title, message, onBackdropPress, confirmButton, cancelButton } = props || {};

  const handleConfirm = () => {
    confirmButton?.onPress?.();
    onBackdropPress?.();
  };
  const handleCancel = () => {
    cancelButton?.onPress?.();
    onBackdropPress?.();
  };
  return (
    <>
      <View style={styles.backdrop}>
        <TouchableOpacity activeOpacity={1} onPress={onBackdropPress} style={styleUtils.flexOne} />
      </View>
      <View style={styles.root}>
        <View style={styles.container}>
          <View style={styles.contentWrapper}>
            <AppText size={16} height={20} bold style={styles.title}>
              {title}
            </AppText>
            <AppText color={AppColors.text} style={styleUtils.centeredText}>
              {message}
            </AppText>
          </View>
          <View style={styles.buttonWrapper}>
            {!!cancelButton && (
              <AppButton
                variant="naked"
                title={cancelButton.title}
                buttonStyle={[styles.button, styles.ignoreButton]}
                titleStyle={styles.buttonTitle}
                onPress={handleCancel}
              />
            )}
            <AppButton
              testID="confirm"
              variant="naked"
              title={confirmButton?.title}
              buttonStyle={styles.button}
              onPress={handleConfirm}
            />
          </View>
        </View>
      </View>
    </>
  );
};

const AlertModal = React.forwardRef<AlertModalHandle>((_, ref) => {
  const [{ component, transparent, position }, setInfo] = React.useState<{
    component: React.ReactElement | null;
    transparent?: boolean;
    position?: 'center' | 'bottom';
  }>({
    component: null,
    transparent: false,
    position: 'center',
  });
  const [visible, setVisible] = React.useState(true);

  React.useImperativeHandle(ref, () => ({
    showAlert: (args: AlertParams) => {
      setVisible(true);
      const { title = '', message = '', cancelButton, confirmButton } = args || {};
      setInfo({
        component: (
          <AlertComponent
            cancelButton={cancelButton}
            confirmButton={confirmButton}
            message={message}
            title={title}
            onBackdropPress={() => handleClose()}
          />
        ),
        transparent: true,
        position: 'center',
      });
    },
    hideAlert: handleClose,
  }));

  React.useEffect(() => {
    if (Platform.OS === 'web') {
      const overflow = component ? 'hidden' : '';
      window.document.body.style.overflow = overflow;
    }
  }, [component]);

  const handleClose = () => {
    setVisible(false);
    setInfo({ component: null, transparent: false, position: 'center' });
  };

  if (!component) {
    return null;
  }

  return (
    <AppModal visible={visible} transparent={true} onRequestClose={handleClose} animationType="fade">
      <View
        style={[
          styles.root,
          transparent && { backgroundColor: AppColors.transparent },
          position === 'bottom' && styles.bottomPopup,
        ]}>
        {component}
      </View>
    </AppModal>
  );
});

export { AlertModal };

const styles = StyleSheet.create({
  // eslint-disable-next-line react-native/no-color-literals
  backdrop: {
    ...StyleSheet.absoluteFillObject,
    backgroundColor: '#000000b5',
    zIndex: 100,
  },
  root: {
    flex: 1,
    justifyContent: 'center',
    zIndex: 100,
    backgroundColor: AppColors.blackOpacity(0.2),
  },
  container: {
    backgroundColor: AppColors.background,
    alignItems: 'center',
    paddingTop: 20,
    borderRadius: 12,
    marginHorizontal: 52,
  },
  contentWrapper: {
    paddingHorizontal: 16,
    paddingBottom: 16,
  },
  title: {
    marginBottom: 8,
    textAlign: 'center',
  },
  buttonWrapper: {
    flexDirection: 'row',
    width: '100%',
    borderTopWidth: 1,
    borderColor: AppColors.border,
  },
  button: {
    flex: 1,
  },
  ignoreButton: {
    borderRightWidth: 1,
    borderRightColor: AppColors.border,
    borderTopRightRadius: 0,
    borderBottomRightRadius: 0,
  },
  buttonTitle: {
    fontWeight: '400',
  },
  bottomPopup: { justifyContent: 'flex-end' },
});

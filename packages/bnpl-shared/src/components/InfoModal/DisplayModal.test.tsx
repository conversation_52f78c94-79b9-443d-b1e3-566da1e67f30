import React from 'react';
import { render, fireEvent } from '@testing-library/react-native';
import { DisplayModal } from './DisplayModal';

describe('DisplayModal', () => {
  const mockOnBackdropPress = jest.fn();
  const mockBody = jest.fn(() => <></>); // Replace with your desired mock body component

  const defaultProps = {
    onBackdropPress: mockOnBackdropPress,
    body: mockBody,
    bodyParams: { param1: 'value1' }, // Replace with your desired body parameters
  };

  afterEach(() => {
    jest.clearAllMocks();
  });

  test('renders correctly', () => {
    const { getByTestId } = render(<DisplayModal {...defaultProps} />);

    const backdrop = getByTestId('backdrop');
    const container = getByTestId('container');

    expect(backdrop).toBeTruthy();
    expect(container).toBeTruthy();
  });

  test('calls onBackdropPress on backdrop press', () => {
    const { getByTestId } = render(<DisplayModal {...defaultProps} />);

    const backdrop = getByTestId('backdrop');

    fireEvent.press(backdrop);

    expect(mockOnBackdropPress).toHaveBeenCalledTimes(1);
  });
});

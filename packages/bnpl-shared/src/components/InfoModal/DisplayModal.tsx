import React from 'react';
import { TouchableOpacity, View } from 'react-native';
import { styleUtils } from 'bnpl-shared/src/shared/styleUtils';
import { StyleSheet } from 'bnpl-shared/src/shared/styles/StyleSheet';
import { AppColors } from 'bnpl-shared/src/constants';

type DisplayProps = {
  onBackdropPress?: () => void;
  body: React.ReactNode | React.ReactElement;
};

const DisplayModal = (props: DisplayProps) => {
  const { onBackdropPress, body } = props;
  return (
    <>
      <View style={styles.backdrop}>
        <TouchableOpacity testID="backdrop" activeOpacity={1} onPress={onBackdropPress} style={styleUtils.flexOne} />
      </View>
      <View style={styles.root}>
        <View style={styles.container} testID="container">
          <View style={styles.contentWrapper}>{body}</View>
        </View>
      </View>
    </>
  );
};
export { DisplayModal };
const styles = StyleSheet.create({
  // eslint-disable-next-line react-native/no-color-literals
  backdrop: {
    ...StyleSheet.absoluteFillObject,
    backgroundColor: '#000000b5',
    zIndex: 100,
  },
  root: {
    flex: 1,
    justifyContent: 'center',
    zIndex: 100,
  },
  container: {
    backgroundColor: AppColors.background,
    alignItems: 'center',
    paddingTop: 20,
    borderRadius: 12,
    marginHorizontal: 24,
  },
  contentWrapper: {
    paddingHorizontal: 16,
    paddingBottom: 16,
  },
});

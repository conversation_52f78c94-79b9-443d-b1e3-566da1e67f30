import React, { useEffect } from 'react';
import { Platform, View, Animated } from 'react-native';
import { LoadingModalHandle } from 'bnpl-shared/src/services';
import { AppModal } from 'bnpl-shared/src/shared/react-native-customized';
import { Path, Svg, G, Circle, Rect, CircleProps } from 'react-native-svg';
import { StyleSheet } from 'bnpl-shared/src/shared/styles/StyleSheet';
import { hideLoading, showLoading } from 'bnpl-shared/src/shared/ZaloPayModules';
import { AppColors } from 'bnpl-shared/src/constants';

class CustomCircle extends React.Component<CircleProps> {
  render() {
    return <Circle {...this.props} />;
  }
}

const AnimatedCircle = Animated.createAnimatedComponent(CustomCircle);

export const ComponentLoading = () => {
  const dotOpacities = React.useRef([new Animated.Value(1), new Animated.Value(1), new Animated.Value(1)]).current;
  React.useEffect(() => {
    const animationSequences = dotOpacities.map((opacity, index) =>
      Animated.sequence([
        Animated.delay(index * 100),
        Animated.timing(opacity, {
          toValue: 0,
          duration: 600,
          useNativeDriver: false,
        }),
        Animated.timing(opacity, {
          toValue: 1,
          duration: 600,
          useNativeDriver: false,
        }),
      ]),
    );

    animationSequences.forEach(animation => {
      Animated.loop(animation).start();
    });

    return () => {
      animationSequences.forEach(animation => {
        Animated.loop(animation).stop();
      });
    };
  }, [dotOpacities]);

  return (
    <View style={styles.root}>
      <View style={styles.container}>
        <View style={styles.contentWrapper}>
          <Svg testID="svg" width={120} height={120} viewBox="0 0 120 120">
            <Rect width={120} height={120} rx={10} opacity={0.9} fill={'#77777a'} />
            <G>
              <Path
                fill={'#ffffff'}
                d="M46.4,47.06a1.42,1.42,0,0,0-1.09-.47,1.39,1.39,0,0,0-1.38.93,3.21,3.21,0,0,0-2.6-1.12A4.36,4.36,0,0,0,38,47.9a5.26,5.26,0,0,0-1.33,3.69A5.45,5.45,0,0,0,38,55.28a4.22,4.22,0,0,0,3.34,1.5,3.25,3.25,0,0,0,2.6-1.1,1.41,1.41,0,0,0,1.38.93,1.5,1.5,0,0,0,1.09-.47,1.44,1.44,0,0,0,.41-1.09V48A1.14,1.14,0,0,0,46.4,47.06Zm-3.11,6.38a1.71,1.71,0,0,1-1.5.7,1.75,1.75,0,0,1-1.5-.7,2.89,2.89,0,0,1-.57-1.78,2.55,2.55,0,0,1,.57-1.73,1.85,1.85,0,0,1,1.5-.69,1.79,1.79,0,0,1,1.5.69,2.86,2.86,0,0,1,.58,1.73A3.49,3.49,0,0,1,43.29,53.44Zm7,2.83a1.45,1.45,0,0,1-1.1.46,1.47,1.47,0,0,1-1.5-1.47V42.5a1.59,1.59,0,0,1,.44-1.1,1.42,1.42,0,0,1,1-.46,1.46,1.46,0,0,1,1.1.47,1.4,1.4,0,0,1,.4,1.09V55.18a1.43,1.43,0,0,1-.4,1.09Zm10-8.3a4.8,4.8,0,0,0-3.69-1.5A5,5,0,0,0,52.91,48a5.35,5.35,0,0,0-1.44,3.75,5.15,5.15,0,0,0,1.44,3.75A5,5,0,0,0,56.6,57a5.06,5.06,0,0,0,3.69-1.5,5.32,5.32,0,0,0,1.39-3.7A4.91,4.91,0,0,0,60.24,48Zm-2.16,5.48a2,2,0,0,1-2.83.17l-.17-.17a2.82,2.82,0,0,1-.58-1.78,2.52,2.52,0,0,1,.58-1.73A2,2,0,0,1,58,49.76l.18.18a2.87,2.87,0,0,1,.57,1.73A3.44,3.44,0,0,1,58.13,53.45Z"
              />
              <Path
                fill={'#ffffff'}
                d="M35,46.82c1-1.38,1.61-2.3,1.61-2.76,0-1.1-.69-1.66-2.07-1.66H27.21a1.78,1.78,0,0,0-1.33.4,1.39,1.39,0,0,0-.46,1,1.27,1.27,0,0,0,.46,1,1.87,1.87,0,0,0,1.33.41h5l-6.45,8.36A2.48,2.48,0,0,0,25.13,55c0,1.21.81,1.79,2.42,1.79h7.58c1.21,0,1.79-.53,1.79-1.5s-.58-1.5-1.79-1.5H29.57l5.42-7Z"
              />
              <Path
                fill={'#ffffff'}
                d="M71.65,44.94H69.77v3.88h1.88A1.75,1.75,0,0,0,73,48.24a2,2,0,0,0,.51-1.36A1.86,1.86,0,0,0,73,45.51,1.64,1.64,0,0,0,71.65,44.94ZM81,48.53a1.69,1.69,0,0,0-1.37.63,2.49,2.49,0,0,0-.51,1.6,2.43,2.43,0,0,0,.51,1.64A1.61,1.61,0,0,0,81,53a1.76,1.76,0,0,0,1.37-.63,2.6,2.6,0,0,0,.51-1.59,2.52,2.52,0,0,0-.51-1.61A1.74,1.74,0,0,0,81,48.53Z"
              />
              <Path
                fill={'#ffffff'}
                d="M94.13,39.4h-27A1.64,1.64,0,0,0,65.43,41V56.85a1.66,1.66,0,0,0,1.66,1.67H88.37a1,1,0,0,1-.34-.75A1.71,1.71,0,0,1,88.2,57l1-2.22-2.79-7a1.76,1.76,0,0,1-.12-.63.81.81,0,0,1,.4-.74,1.11,1.11,0,0,1,.8-.35,1.23,1.23,0,0,1,1.26.92l1.71,4.73L92.29,47a1.27,1.27,0,0,1,1.25-.92,1.11,1.11,0,0,1,.8.35,1,1,0,0,1,.4.74,2.78,2.78,0,0,1-.11.63l-4.11,10a2.39,2.39,0,0,1-.4.69h4a1.67,1.67,0,0,0,1.65-1.66V41.05a1.61,1.61,0,0,0-1.57-1.65ZM75,50a4.54,4.54,0,0,1-3.2,1.14h-2v3a1.27,1.27,0,0,1-.4,1,1.44,1.44,0,0,1-1,.4,1.21,1.21,0,0,1-1-.4,1.51,1.51,0,0,1-.4-1V43.85a1.24,1.24,0,0,1,1-1.45,1.09,1.09,0,0,1,.44,0h3.26a4.78,4.78,0,0,1,3.36,1.2,4.21,4.21,0,0,1,1.32,3.14A4,4,0,0,1,75,50Zm10.55,4.05a1.36,1.36,0,0,1-1.36,1.34h0a1.41,1.41,0,0,1-1.31-.85,3,3,0,0,1-2.4,1,3.92,3.92,0,0,1-3-1.37,5.1,5.1,0,0,1-1.26-3.44,4.78,4.78,0,0,1,1.24-3.34,3.85,3.85,0,0,1,3-1.37,3,3,0,0,1,2.4,1,1.35,1.35,0,0,1,1.31-.86,1.11,1.11,0,0,1,1,.4,1.24,1.24,0,0,1,.4,1Z"
              />
            </G>
            <G id="dots">
              <AnimatedCircle fill={'#ffffff'} cx="49.55" cy="78.1" r="3" opacity={dotOpacities[0]} />
              <AnimatedCircle fill={'#ffffff'} cx="62.55" cy="78.1" r="3" opacity={dotOpacities[1]} />
              <AnimatedCircle fill={'#ffffff'} cx="75.53" cy="78.1" r="3" opacity={dotOpacities[2]} />
            </G>
          </Svg>
        </View>
      </View>
    </View>
  );
};

const LoadingIndicator = React.forwardRef<LoadingModalHandle, {}>((_, ref) => {
  const [{ component, transparent, position }, setInfo] = React.useState<{
    component: React.ReactElement | null;
    transparent?: boolean;
    position?: 'center' | 'bottom';
  }>({
    component: null,
    transparent: false,
    position: 'center',
  });
  const [visible, setVisible] = React.useState(true);

  React.useImperativeHandle(ref, () => ({
    showLoading: () => {
      if (Platform.OS === 'web') {
        setVisible(true);
        setInfo({
          component: <ComponentLoading />,
          transparent: true,
          position: 'center',
        });
        return;
      }
      showLoading();
    },
    hideLoading: () => {
      if (Platform.OS === 'web') {
        handleClose();
        return;
      }
      hideLoading();
    },
  }));

  useEffect(() => {
    if (Platform.OS === 'web') {
      const overflow = component ? 'hidden' : '';
      window.document.body.style.overflow = overflow;
    }
  }, [component]);

  const handleClose = () => {
    setVisible(false);
    setInfo({ component: null, transparent: false, position: 'center' });
  };

  if (!component) {
    return null;
  }

  return (
    <AppModal visible={visible} transparent={true} onRequestClose={handleClose} animationType="fade">
      <View
        style={[
          styles.root,
          transparent && { backgroundColor: AppColors.transparent },
          position === 'bottom' && styles.bottomPopup,
        ]}>
        {component}
      </View>
    </AppModal>
  );
});

const styles = StyleSheet.create({
  bottomPopup: { justifyContent: 'flex-end' },
  root: {
    flex: 1,
    zIndex: 100,
    justifyContent: 'center',
    backgroundColor: AppColors.blackOpacity(0),
  },
  container: {
    backgroundColor: AppColors.transparent,
    alignItems: 'center',
    borderRadius: 12,
    marginHorizontal: 24,
  },
  contentWrapper: {
    paddingHorizontal: 16,
  },
});
export { LoadingIndicator };

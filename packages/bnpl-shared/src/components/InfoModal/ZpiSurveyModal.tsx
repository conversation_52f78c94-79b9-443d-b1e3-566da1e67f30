import React, { FC } from 'react';
import { SurveyTimeToLiveType, SurveyTypeEnum, SurveyWrapper } from '@ce/survey-wrapper';
import { getSurveyWrapperResourceUrlWithEnv } from 'bnpl-shared/src/api/withBaseUrl';

export type SurveyModalParams = {
  surveyId: string;
  timeToLive?: SurveyTimeToLiveType;
  type: SurveyTypeEnum;
  onSubmit?: () => void;
  onCancel?: () => void;
};

const ZpiSurveyModal: FC<SurveyModalParams> = args => {
  if (!args?.surveyId) {
    return null;
  }

  return (
    <SurveyWrapper
      isOpen={true}
      timeToLiveType={args.timeToLive}
      resourceHost={getSurveyWrapperResourceUrlWithEnv()}
      componentName={args.surveyId}
      onSubmit={() => {
        args.onSubmit?.();
      }}
      onCancel={() => {
        args.onCancel?.();
      }}
      type={args.type}
    />
  );
};

export default ZpiSurveyModal;

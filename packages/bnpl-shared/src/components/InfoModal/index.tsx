import React, { useEffect } from 'react';
import { Platform, StyleSheet, TouchableWithoutFeedback, View } from 'react-native';
import { InfoModalHandle, InfoModalService, ModalParams, ModalType } from 'bnpl-shared/src/services';
import { AppModal } from 'bnpl-shared/src/shared/react-native-customized';
import { BottomSheet } from '../layouts/BottomSheet';
import { DisplayModal } from './DisplayModal';
import { AppColors } from 'bnpl-shared/src/constants';

const InfoModal = React.forwardRef<InfoModalHandle, {}>((_, ref) => {
  const [{ component, transparent, position }, setInfo] = React.useState<{
    component: React.ReactElement | React.ReactNode;
    transparent?: boolean;
    position?: 'center' | 'bottom';
  }>({
    component: null,
    transparent: false,
    position: 'center',
  });
  const [visible, setVisible] = React.useState(true);

  const showAsBottomSheet = (args: ModalParams) => {
    setInfo({
      component: (
        <BottomSheet
          {...args?.bottomSheetProps}
          body={
            <TouchableWithoutFeedback>
              <View style={styles.container}>{args.screen}</View>
            </TouchableWithoutFeedback>
          }
          requestClose={() => {
            args?.bottomSheetProps?.requestClose?.();
            InfoModalService.hideModal();
          }}
        />
      ),
      transparent: true,
      position: 'bottom',
    });
  };

  const showAsModal = (args: ModalParams) => {
    setInfo({
      component: <DisplayModal body={args.screen} onBackdropPress={InfoModalService.hideModal} />,
      transparent: true,
      position: 'center',
    });
  };

  React.useImperativeHandle(ref, () => ({
    show: (args: ModalParams) => {
      setVisible(true);
      switch (args.type) {
        case ModalType.BOTTOM_SHEET:
          showAsBottomSheet(args);
          break;
        case ModalType.DISPLAY:
          showAsModal(args);
          break;
        case ModalType.MODAL:
          setInfo({ component: args.screen, transparent: args.options?.transparent, position: args.options?.position });
          break;
      }
    },
    hide: () => {
      setInfo({ component: null, transparent: false, position: 'center' });
      setVisible(false);
    },
  }));

  useEffect(() => {
    if (Platform.OS === 'web') {
      const overflow = component ? 'hidden' : '';
      window.document.body.style.overflow = overflow;
    }
  }, [component]);

  if (!component) {
    return null;
  }

  return (
    <AppModal visible={visible} transparent onRequestClose={InfoModalService.hideModal} animationType="fade">
      <View
        style={[
          styles.root,
          transparent && { backgroundColor: AppColors.transparent },
          position === 'bottom' && styles.bottomPopup,
        ]}>
        {component}
      </View>
    </AppModal>
  );
});
const styles = StyleSheet.create({
  bottomPopup: { justifyContent: 'flex-end' },
  root: {
    flex: 1,
    zIndex: 100,
    justifyContent: 'center',
    backgroundColor: AppColors.blackOpacity(0.2),
  },
  container: { flex: 1 },
});
export default InfoModal;

import React from 'react';
import { View } from 'react-native';
import { StyleSheet } from 'bnpl-shared/src/shared/styles/StyleSheet';
import { AppText } from 'bnpl-shared/src/shared/react-native-customized';
import { AppColors } from 'bnpl-shared/src/constants';

export default function Inform({ children, type = 'info' }: InformProps) {
  return (
    <View style={[styles.root, type === 'info' && styles.infoContainer]}>
      <AppText color={AppColors.dark[500]} size={14} height={20}>{children}</AppText>
    </View>
  );
}

interface InformProps {
  type?: 'info',
  children: React.ReactNode;
}

const styles = StyleSheet.create({
  root: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    paddingVertical: 12,
    paddingHorizontal: 16,
    borderRadius: 8,
  },
  infoContainer: {
    backgroundColor: AppColors.blue[50],
  },
  infoIcon: {
    marginRight: 8,
  },
});

import { render } from '@testing-library/react-native';
import { LockAccountAction } from 'bnpl-shared/src/types';
import { LockAccountActionItem } from './LockAccountActionItem';
import React from 'react';

describe('LockAccountActionItem', () => {
  it('render action pay debt as expected', () => {
    const { queryByText } = render(<LockAccountActionItem action={LockAccountAction.PAY_DEBT} order={2} />);
    expect(queryByText('Thanh toán dư nợ tối thiểu và truy cập lại vào hôm sau')).toBeTruthy();
    expect(queryByText('Thực hiện')).toBeTruthy();
    expect(queryByText('Bước 2')).toBeTruthy();
  });

  it('render action CIMB support as expected', () => {
    const { queryByText } = render(<LockAccountActionItem action={LockAccountAction.CONTACT_CIMB_SUPPORT} order={1} />);
    expect(queryByText('<PERSON><PERSON><PERSON> hệ CIMB để xác minh giao dịch')).toBeTruthy();
    expect(queryByText('Thực hiện')).toBeTruthy();
    expect(queryByText('Bước 1')).toBeTruthy();
  });

  it('render action ZaloPay support as expected', () => {
    const { queryByText } = render(
      <LockAccountActionItem action={LockAccountAction.CONTACT_ZALOPAY_SUPPORT} order={3} />,
    );
    expect(queryByText('Liên hệ CSKH Zalopay để được hỗ trợ. Vui lòng chờ xử lý trong 3 ngày làm việc.')).toBeTruthy();
    expect(queryByText('Thực hiện')).toBeTruthy();
    expect(queryByText('Bước 3')).toBeTruthy();
  });

  it('render action unlock account as expected', () => {
    const { queryByText } = render(<LockAccountActionItem action={LockAccountAction.UNLOCK_ACCOUNT} order={1} />);
    expect(queryByText('Thực hiện')).toBeTruthy();
    expect(queryByText('Mở khóa tài khoản')).toBeTruthy();
    expect(queryByText('Bước 1')).toBeTruthy();
  });

  it('render action pay debt and contact CIMB support  as expected', () => {
    const { queryByText } = render(
      <LockAccountActionItem action={LockAccountAction.PAY_DEBT_AND_CONTACT_CIMB_SUPPORT} order={1} />,
    );
    expect(
      queryByText('Nếu còn nợ, vui lòng trả tối thiểu và thử lại vào ngày mai. Nếu không nợ, liên hệ CIMB.'),
    ).toBeTruthy();
    expect(queryByText('Thực hiện')).toBeTruthy();
    expect(queryByText('Bước 1')).toBeTruthy();
  });
});

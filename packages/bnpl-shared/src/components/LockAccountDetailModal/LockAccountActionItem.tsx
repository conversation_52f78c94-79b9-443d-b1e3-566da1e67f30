import React, { FC } from 'react';
import { LockAccountAction } from 'bnpl-shared/src/types';
import { StyleSheet } from 'bnpl-shared/src/shared/styles/StyleSheet';
import { AppColors } from 'bnpl-shared/src/constants';
import { TouchableOpacity, View } from 'react-native';
import { AppImage, AppText } from 'bnpl-shared/src/shared/react-native-customized';
import { Spacer } from 'bnpl-shared/src/components/Spacer';
import { images } from 'bnpl-shared/src/res';
import { StyleUtils } from 'bnpl-shared/src/shared/styles/StyleUtils';
import { ChevronIcon } from 'bnpl-shared/src/shared/ChevronIcon';
import { Skeleton } from 'bnpl-shared/src/shared/animated-wrappers/Skeleton';

export const LockAccountActionItem: FC<{
  action: LockAccountAction;
  order: number;
  onPressAction?: () => void;
}> = ({ action, onPressAction, order }) => {
  let imageSource: number;
  let actionDescription: string;
  let actionComponent: React.ReactElement;

  switch (action) {
    case LockAccountAction.PAY_DEBT:
      imageSource = images.IconMoneyBag;
      actionDescription = 'Thanh toán dư nợ tối thiểu và truy cập lại vào hôm sau';
      actionComponent = (
        <TouchableOpacity testID="action-pay-debt" onPress={() => onPressAction?.()} style={styles.row}>
          <AppText size={14} height={18} color={AppColors.primary}>
            Thực hiện
          </AppText>
          <Spacer width={4} />
          <ChevronIcon direction="right" />
        </TouchableOpacity>
      );
      break;
    case LockAccountAction.PAY_DEBT_AND_CONTACT_CIMB_SUPPORT:
      imageSource = images.IconMoneyBag;
      actionDescription = 'Nếu còn nợ, vui lòng trả tối thiểu và thử lại vào ngày mai. Nếu không nợ, liên hệ CIMB.';
      actionComponent = (
        <TouchableOpacity testID="action-pay-debt-contact-cimb" onPress={() => onPressAction?.()} style={styles.row}>
          <AppText size={14} height={18} color={AppColors.primary}>
            Thực hiện
          </AppText>
          <Spacer width={4} />
          <ChevronIcon direction="right" />
        </TouchableOpacity>
      );
      break;
    case LockAccountAction.CONTACT_CIMB_SUPPORT:
      imageSource = images.IconLogoCIMBRound;
      actionDescription = 'Liên hệ CIMB để xác minh giao dịch';
      actionComponent = (
        <TouchableOpacity testID="action-cimb-support" onPress={() => onPressAction?.()} style={styles.row}>
          <AppText size={14} height={18} color={AppColors.primary}>
            Thực hiện
          </AppText>
          <Spacer width={4} />
          <ChevronIcon direction="right" />
        </TouchableOpacity>
      );
      break;
    case LockAccountAction.CONTACT_ZALOPAY_SUPPORT:
      imageSource = images.IconZlpSupport;
      actionDescription = 'Liên hệ CSKH Zalopay để được hỗ trợ. Vui lòng chờ xử lý trong 3 ngày làm việc.';
      actionComponent = (
        <TouchableOpacity testID="action-zalopay-support" onPress={() => onPressAction?.()} style={styles.row}>
          <AppText size={14} height={18} color={AppColors.primary}>
            Thực hiện
          </AppText>
          <Spacer width={4} />
          <ChevronIcon direction="right" />
        </TouchableOpacity>
      );
      break;
    case LockAccountAction.UNLOCK_ACCOUNT:
      imageSource = images.IconActionUnlock;
      actionDescription = 'Mở khóa tài khoản';
      actionComponent = (
        <TouchableOpacity testID="action-unlock-account" onPress={() => onPressAction?.()} style={styles.row}>
          <AppText size={14} height={18} color={AppColors.primary}>
            Thực hiện
          </AppText>
          <Spacer width={4} />
          <ChevronIcon direction="right" />
        </TouchableOpacity>
      );
      break;
  }

  return (
    <View testID={`lock-action-item-${action}`} style={styles.root}>
      <AppImage width={40} height={40} source={imageSource} />
      <Spacer width={8} />
      <View style={styles.textContainer}>
        <View style={StyleUtils.flexRow}>
          <View>
            <AppText size={14} height={18} bold>
              Bước {order}
            </AppText>
            <AppText size={12} height={16} numberOfLines={3} style={{ maxWidth: 200 }} color={AppColors.text2}>
              {actionDescription}
            </AppText>
          </View>
          {actionComponent}
        </View>
      </View>
    </View>
  );
};

export const LoadingLockAccountActionItem = () => {
  return (
    <View testID="loading-lock-actions" style={styles.root}>
      <Skeleton width={40} height={40} style={styles.loadingImage} />
      <Spacer width={8} />
      <View style={styles.textContainer}>
        <View style={StyleUtils.flexRow}>
          <View>
            <Skeleton style={styles.loadingText} width={60} height={18} />
            <Spacer height={4} />
            <Skeleton style={styles.loadingText} width={120} height={18} />
          </View>
          <View style={styles.row}>
            <Skeleton style={styles.loadingText} width={80} height={18} />
          </View>
        </View>
      </View>
    </View>
  );
};

const styles = StyleSheet.create({
  root: {
    flexDirection: 'row',
    marginBottom: 16,
    alignItems: 'center',
  },
  textContainer: {
    borderRadius: 8,
    borderColor: AppColors.background4,
    borderWidth: 1,
    flex: 1,
    padding: 8,
  },
  button: {
    marginTop: 24,
  },
  iconTitle: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  warningText: {
    flex: 1,
  },
  iconLock: {
    marginStart: 4,
  },
  loadingImage: {
    borderRadius: 8,
  },
  loadingText: {
    borderRadius: 4,
  },
  loadingToggle: {
    borderRadius: 25.5,
  },
  row: { flexDirection: 'row', justifyContent: 'flex-end', alignSelf: 'center', alignItems: 'flex-end', flex: 1 },
});

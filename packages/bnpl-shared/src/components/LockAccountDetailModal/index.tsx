import React, { FC, useEffect } from 'react';
import { AppButton, AppImage, AppText } from 'bnpl-shared/src/shared/react-native-customized';
import { images } from 'bnpl-shared/src/res';
import { Spacer } from 'bnpl-shared/src/components/Spacer';
import { View } from 'react-native';
import { AppColors } from 'bnpl-shared/src/constants';
import { StyleSheet } from 'bnpl-shared/src/shared/styles/StyleSheet';
import { getBottomSafe } from 'bnpl-shared/src/utils';
import {
  LoadingLockAccountActionItem,
  LockAccountActionItem,
} from 'bnpl-shared/src/components/LockAccountDetailModal/LockAccountActionItem';
import { LockAction } from 'bnpl-shared/src/types';
import { trackLockDetailCTAPress, trackLockDetailShow } from 'bnpl-shared/src/screens/HomeScreen/tracking';
import { InfoModalService } from 'bnpl-shared/src/services';

const LockAccountDetailModal: FC<{ lockActions: LockAction[] | undefined, handleLockAction: (action: LockAction) => void }> = ({ lockActions, handleLockAction }) => {
  const handleClose = () => {
    InfoModalService.hideModal();
  };

  const renderLoadingSkeleton = () => {
    return (
      <>
        <LoadingLockAccountActionItem />
        <LoadingLockAccountActionItem />
      </>
    );
  };

  const handleAction = async (action: LockAction) => {
    handleClose();
    trackLockDetailCTAPress('do_now', action.useCaseCode);
    handleLockAction(action);
  };

  useEffect(() => {
    trackLockDetailShow();
  }, []);

  const lockActionComponents = lockActions
    ? lockActions?.map((item, index) => (
        <LockAccountActionItem
          key={item.type}
          onPressAction={() => handleAction(item)}
          action={item.type}
          order={index + 1}
        />
      ))
    : renderLoadingSkeleton();

  return (
    <>
      <AppImage height={125} width={375} source={images.ImageLockAccount} />
      <View testID="lock-detail-modal" style={styles.root}>
        <Spacer height={16} />
        <AppText height={20} bold>
          Để mở khóa, vui lòng thực hiện các bước sau:
        </AppText>
        <Spacer height={16} />
        {lockActionComponents}
        <AppButton
          buttonStyle={styles.button}
          title="Đóng"
          onPress={() => {
            trackLockDetailCTAPress('close');
            handleClose();
          }}
        />
      </View>
    </>
  );
};

const styles = StyleSheet.create({
  root: {
    paddingHorizontal: 16,
    paddingBottom: getBottomSafe() + 16,
    backgroundColor: AppColors.background,
  },
  button: {
    marginTop: 24,
  },
  iconTitle: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  warningText: {
    flex: 1,
  },
  iconLock: {
    marginStart: 4,
  },
});

export default LockAccountDetailModal;

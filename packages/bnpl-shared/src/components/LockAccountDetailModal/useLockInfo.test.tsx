import { getAccountLockInfoApi } from 'bnpl-shared/src/api/getAccountLockInfoApi';
import { getAccountLockInfoApiBuilder } from 'bnpl-shared/src/api/__mocks__/getAccountLockInfoApi';
import { useLockInfo } from 'bnpl-shared/src/components/LockAccountDetailModal/useLockInfo';
import { LockAccountAction, LockAccountUseCase } from 'bnpl-shared/src/types';
import { waitFor } from '@testing-library/react-native';
import { store } from 'bnpl-shared/src/redux/store';
import { setChosenPartner, setPartnerData } from 'bnpl-shared/src/redux/multipartnerReducer';
import { PartnerCode } from 'bnpl-shared/src/constants';
import { RoutingInfoMapper } from 'bnpl-shared/src/features/multipartner_integration/helpers';
import { FakePartnerCIMB } from 'bnpl-shared/src/jest/fakeData';
import { renderHookWithRedux } from 'bnpl-shared/src/jest/renderWithRedux';
import { FAKE_NAVIGATION } from "bnpl-shared/src/jest/constants";

const mockUnlockAccount = jest.fn();
const mockNavigateTo = jest.fn();

jest.mock('bnpl-shared/src/features/lock_account_by_user', () => ({
  useLockAccountByUser: () => ({
    unlockAccount: mockUnlockAccount,
    lockAccount: jest.fn(),
  }),
}));

// Mock dependencies for _handleLockAction tests
jest.mock('bnpl-shared/src/utils/openLink', () => ({
  openLink: jest.fn(),
}));

jest.mock('bnpl-shared/src/shared/ZaloPayModules/ZaloPayModules', () => ({
  getAppInfo: jest.fn(),
  launchDeepLink: jest.fn(),
}));

jest.mock('bnpl-shared/src/api/buildFaqUrl', () => ({
  buildFaqUrl: jest.fn(),
}));

jest.mock('bnpl-shared/src/features/multipartner_integration/helpers', () => ({
  ...jest.requireActual('bnpl-shared/src/features/multipartner_integration/helpers'),
  useMultiPartnerNavigate: () => ({
    navigateTo: mockNavigateTo,
  }),
}));

// Import the mocked functions
import { openLink } from 'bnpl-shared/src/utils/openLink';
import { getAppInfo, launchDeepLink } from 'bnpl-shared/src/shared/ZaloPayModules/ZaloPayModules';
import { buildFaqUrl } from 'bnpl-shared/src/api/buildFaqUrl';

describe('useLockInfo', () => {
  beforeAll(() => {
    store.dispatch(
      setPartnerData({
        partnerCode: PartnerCode.CIMB,
        data: new RoutingInfoMapper(FakePartnerCIMB).toPartnerData(),
      }),
    );
    store.dispatch(setChosenPartner(PartnerCode.CIMB));
  });

  beforeEach(() => {
    (getAccountLockInfoApi as jest.Mock).mockClear();
  });

  it('should return resolveUserLockState function', () => {
    const { result } = renderHookWithRedux(() => useLockInfo(FAKE_NAVIGATION));
    expect(result.current).toHaveProperty('resolveUserLockState');
    expect(typeof result.current.resolveUserLockState).toBe('function');
  });

  it('should automatically fetch lock info on mount', async () => {
    (getAccountLockInfoApi as jest.Mock).mockResolvedValue(getAccountLockInfoApiBuilder.build());
    
    renderHookWithRedux(() => useLockInfo(FAKE_NAVIGATION));
    
    await waitFor(() => {
      expect(getAccountLockInfoApi).toHaveBeenCalledTimes(1);
    });
  });

  it('should map lock info to lock actions and sort correctly', async () => {
    (getAccountLockInfoApi as jest.Mock).mockResolvedValue(getAccountLockInfoApiBuilder.build());
    
    const { result } = renderHookWithRedux(() => useLockInfo(FAKE_NAVIGATION));
    
    await waitFor(() => {
      expect(result.current.lockActions).toEqual([
        {
          type: LockAccountAction.PAY_DEBT_AND_CONTACT_CIMB_SUPPORT,
          order: 1,
          useCaseCode: LockAccountUseCase.PARTNER_LOCK,
        },
        { type: LockAccountAction.PAY_DEBT, order: 2, useCaseCode: LockAccountUseCase.PARTNER_DPD },
        {
          type: LockAccountAction.CONTACT_CIMB_SUPPORT,
          order: 3,
          useCaseCode: LockAccountUseCase.PARTNER_FRAUD_DETECTION,
        },
        { type: LockAccountAction.CONTACT_ZALOPAY_SUPPORT, order: 4, useCaseCode: expect.anything() },
      ]);
    });
  });

  it('should deduplicate lock actions', async () => {
    (getAccountLockInfoApi as jest.Mock).mockResolvedValue(getAccountLockInfoApiBuilder.build('duplicate_use_case'));
    
    const { result } = renderHookWithRedux(() => useLockInfo(FAKE_NAVIGATION));
    
    await waitFor(() => {
      expect(result.current.lockActions).toEqual([
        {
          type: LockAccountAction.PAY_DEBT_AND_CONTACT_CIMB_SUPPORT,
          order: 1,
          useCaseCode: LockAccountUseCase.PARTNER_LOCK,
        },
        { type: LockAccountAction.CONTACT_ZALOPAY_SUPPORT, order: 4, useCaseCode: expect.anything() },
      ]);
    });
  });

  // Test mapUseCaseToAction functionality by testing different use cases
  describe('use case mapping tests', () => {
    it('should map ACCOUNT use case to UNLOCK_ACCOUNT action', async () => {
      (getAccountLockInfoApi as jest.Mock).mockResolvedValue({
        is_lock: true,
        locks: [{ use_case_code: LockAccountUseCase.ACCOUNT }]
      });

      const { result } = renderHookWithRedux(() => useLockInfo(FAKE_NAVIGATION));

      await waitFor(() => {
        expect(result.current.lockActions).toEqual([
          expect.objectContaining({
            type: LockAccountAction.UNLOCK_ACCOUNT,
            order: 1,
            useCaseCode: LockAccountUseCase.ACCOUNT
          })
        ]);
      });
    });

    it('should map PARTNER_LOCK use case to PAY_DEBT_AND_CONTACT_CIMB_SUPPORT action', async () => {
      (getAccountLockInfoApi as jest.Mock).mockResolvedValue({
        is_lock: true,
        locks: [{ use_case_code: LockAccountUseCase.PARTNER_LOCK }]
      });

      const { result } = renderHookWithRedux(() => useLockInfo(FAKE_NAVIGATION));

      await waitFor(() => {
        expect(result.current.lockActions).toEqual([
          expect.objectContaining({
            type: LockAccountAction.PAY_DEBT_AND_CONTACT_CIMB_SUPPORT,
            order: 1,
            useCaseCode: LockAccountUseCase.PARTNER_LOCK
          })
        ]);
      });
    });

    it('should map PARTNER_DPD use case to PAY_DEBT action', async () => {
      (getAccountLockInfoApi as jest.Mock).mockResolvedValue({
        is_lock: true,
        locks: [{ use_case_code: LockAccountUseCase.PARTNER_DPD }]
      });

      const { result } = renderHookWithRedux(() => useLockInfo(FAKE_NAVIGATION));

      await waitFor(() => {
        expect(result.current.lockActions).toEqual([
          expect.objectContaining({
            type: LockAccountAction.PAY_DEBT,
            order: 2,
            useCaseCode: LockAccountUseCase.PARTNER_DPD
          })
        ]);
      });
    });

    it('should map PARTNER_FRAUD_DETECTION use case to CONTACT_CIMB_SUPPORT action', async () => {
      (getAccountLockInfoApi as jest.Mock).mockResolvedValue({
        is_lock: true,
        locks: [{ use_case_code: LockAccountUseCase.PARTNER_FRAUD_DETECTION }]
      });

      const { result } = renderHookWithRedux(() => useLockInfo(FAKE_NAVIGATION));

      await waitFor(() => {
        expect(result.current.lockActions).toEqual([
          expect.objectContaining({
            type: LockAccountAction.CONTACT_CIMB_SUPPORT,
            order: 3,
            useCaseCode: LockAccountUseCase.PARTNER_FRAUD_DETECTION
          })
        ]);
      });
    });

    it('should map CE use case to CONTACT_ZALOPAY_SUPPORT action', async () => {
      (getAccountLockInfoApi as jest.Mock).mockResolvedValue({
        is_lock: true,
        locks: [{ use_case_code: LockAccountUseCase.CE }]
      });

      const { result } = renderHookWithRedux(() => useLockInfo(FAKE_NAVIGATION));

      await waitFor(() => {
        expect(result.current.lockActions).toEqual([
          expect.objectContaining({
            type: LockAccountAction.CONTACT_ZALOPAY_SUPPORT,
            order: 4,
            useCaseCode: LockAccountUseCase.CE
          })
        ]);
      });
    });

    it('should map various use cases to CONTACT_ZALOPAY_SUPPORT action', async () => {
      const testCases = [
        LockAccountUseCase.FS_ADMIN,
        LockAccountUseCase.RISK, 
        LockAccountUseCase.UNSPECIFIED,
        LockAccountUseCase.UM
      ];

      for (const useCase of testCases) {
        (getAccountLockInfoApi as jest.Mock).mockResolvedValue({
          is_lock: true,
          locks: [{ use_case_code: useCase }]
        });

        const { result } = renderHookWithRedux(() => useLockInfo(FAKE_NAVIGATION));

        await waitFor(() => {
          expect(result.current.lockActions).toEqual([
            expect.objectContaining({
              type: LockAccountAction.CONTACT_ZALOPAY_SUPPORT,
              order: 4,
              useCaseCode: useCase
            })
          ]);
        });
      }
    });
  });

  // Test resolveUserLockState functionality
  describe('resolveUserLockState functionality', () => {
    it('should handle empty lock actions correctly', async () => {
      (getAccountLockInfoApi as jest.Mock).mockResolvedValue({
        is_lock: false,
        locks: []
      });

      const { result } = renderHookWithRedux(() => useLockInfo(FAKE_NAVIGATION));

      await waitFor(() => {
        expect(result.current.lockActions).toEqual([]);
      });

      // resolveUserLockState should handle empty actions gracefully
      expect(() => result.current.resolveUserLockState()).not.toThrow();
    });

    it('should handle single unlock action', async () => {
      (getAccountLockInfoApi as jest.Mock).mockResolvedValue({
        is_lock: true,
        locks: [{ use_case_code: LockAccountUseCase.ACCOUNT }]
      });

      const { result } = renderHookWithRedux(() => useLockInfo(FAKE_NAVIGATION));

      await waitFor(() => {
        expect(result.current.lockActions).toHaveLength(1);
        expect(result.current.lockActions?.[0].type).toBe(LockAccountAction.UNLOCK_ACCOUNT);
      });

      // Should handle single action case
      expect(() => result.current.resolveUserLockState()).not.toThrow();
    });

    it('should handle multiple lock actions', async () => {
      (getAccountLockInfoApi as jest.Mock).mockResolvedValue({
        is_lock: true,
        locks: [
          { use_case_code: LockAccountUseCase.PARTNER_DPD },
          { use_case_code: LockAccountUseCase.CE }
        ]
      });

      const { result } = renderHookWithRedux(() => useLockInfo(FAKE_NAVIGATION));

      await waitFor(() => {
        expect(result.current.lockActions).toHaveLength(2);
      });

      // Should handle multiple actions case
      expect(() => result.current.resolveUserLockState()).not.toThrow();
    });
  });

  // Test edge cases and error handling
  describe('error handling and edge cases', () => {
    it('should handle API errors gracefully', async () => {
      (getAccountLockInfoApi as jest.Mock).mockRejectedValue(new Error('API Error'));

      const { result } = renderHookWithRedux(() => useLockInfo(FAKE_NAVIGATION));
      
      // Should not crash when API fails
      expect(result.current).toHaveProperty('resolveUserLockState');
    });

    it('should sort and deduplicate lock actions correctly', async () => {
      (getAccountLockInfoApi as jest.Mock).mockResolvedValue({
        is_lock: true,
        locks: [
          { use_case_code: LockAccountUseCase.CE }, // order 4
          { use_case_code: LockAccountUseCase.PARTNER_DPD }, // order 2
          { use_case_code: LockAccountUseCase.ACCOUNT }, // order 1
          { use_case_code: LockAccountUseCase.PARTNER_DPD }, // duplicate
        ]
      });

      const { result } = renderHookWithRedux(() => useLockInfo(FAKE_NAVIGATION));

      await waitFor(() => {
        const actions = result.current.lockActions;
        expect(actions).toHaveLength(3); // Should deduplicate
        
        // Should be sorted by order ascending (1, 2, 4)
        expect(actions?.[0].order).toBe(1);
        expect(actions?.[1].order).toBe(2);  
        expect(actions?.[2].order).toBe(4);
      });
    });

    it('should handle null/empty locks array', async () => {
      (getAccountLockInfoApi as jest.Mock).mockResolvedValue({
        is_lock: false,
        locks: null
      });

      const { result } = renderHookWithRedux(() => useLockInfo(FAKE_NAVIGATION));

      await waitFor(() => {
        expect(result.current.lockActions).toEqual(expect.any(Array));
      });
    });
  });

    // Tests for _handleLockAction function (tested through resolveUserLockState behavior)
  describe('_handleLockAction function behavior', () => {
    let originalConsoleLog: typeof console.log;

    beforeAll(() => {
      originalConsoleLog = console.log;
      console.log = jest.fn(); // Mock console.log to avoid noise in tests
    });

    afterAll(() => {
      console.log = originalConsoleLog;
    });

    beforeEach(() => {
      // Clear all mocks
      jest.clearAllMocks();
      
      // Setup default mocks
      (getAppInfo as jest.Mock).mockResolvedValue({ platform: 'OTHER' });
      (buildFaqUrl as jest.Mock).mockReturnValue('https://faq.example.com');
    });

    describe('UNLOCK_ACCOUNT action - single action scenario', () => {
      it('should unlock account and navigate to home on success', async () => {
        console.log('UNCLOKC',mockUnlockAccount)
        mockUnlockAccount.mockResolvedValue(true);
        // Mock API to return single unlock action
        (getAccountLockInfoApi as jest.Mock).mockResolvedValue({
          is_lock: true,
          locks: [{ use_case_code: LockAccountUseCase.ACCOUNT }]
        });
        
        const { result } = renderHookWithRedux(() => useLockInfo(FAKE_NAVIGATION));
        
        // Wait for the hook to fetch and set lock actions
        await waitFor(() => {
          expect(result.current.lockActions).toHaveLength(1);
          expect(result.current.lockActions?.[0].type).toBe(LockAccountAction.UNLOCK_ACCOUNT);
        });
        
        // Trigger resolveUserLockState which should call _handleLockAction for single unlock action
        result.current.resolveUserLockState();

        // Wait for async operations to complete
        await waitFor(() => {
          expect(mockUnlockAccount).toHaveBeenCalled();
        });

        expect(mockNavigateTo).toHaveBeenCalledWith('home');
      });

      it('should not navigate to home when unlock fails', async () => {
        mockUnlockAccount.mockResolvedValue(false);
        (getAccountLockInfoApi as jest.Mock).mockResolvedValue({
          is_lock: true,
          locks: [{ use_case_code: LockAccountUseCase.ACCOUNT }]
        });
        
        const { result } = renderHookWithRedux(() => useLockInfo(FAKE_NAVIGATION));
        
        await waitFor(() => {
          expect(result.current.lockActions).toHaveLength(1);
        });
        
        result.current.resolveUserLockState();

        await waitFor(() => {
          expect(mockUnlockAccount).toHaveBeenCalled();
        });

        expect(mockNavigateTo).not.toHaveBeenCalledWith('home');
      });

      it('should handle unlock account error gracefully', async () => {
        const mockError = new Error('Network error');
        mockUnlockAccount.mockRejectedValue(mockError);

        (getAccountLockInfoApi as jest.Mock).mockResolvedValue({
          is_lock: true,
          locks: [{ use_case_code: LockAccountUseCase.ACCOUNT }]
        });
        
        const { result } = renderHookWithRedux(() => useLockInfo(FAKE_NAVIGATION));
        
        await waitFor(() => {
          expect(result.current.lockActions).toHaveLength(1);
        });
        
        result.current.resolveUserLockState();

        await waitFor(() => {
          expect(mockUnlockAccount).toHaveBeenCalled();
        });

        // Should log the error but not crash
        expect(console.log).toHaveBeenCalledWith("Error unlocking account:", mockError);
        expect(mockNavigateTo).not.toHaveBeenCalledWith('home');
      });
    });

    describe('Multiple action scenario - modal behavior', () => {
      it('should show modal with multiple actions and pass handleLockAction function', async () => {
        const mockShowModal = jest.fn();
        require('bnpl-shared/src/services').InfoModalService.showModal = mockShowModal;
        
        // Mock API to return multiple lock actions
        (getAccountLockInfoApi as jest.Mock).mockResolvedValue({
          is_lock: true,
          locks: [
            { use_case_code: LockAccountUseCase.PARTNER_DPD },
            { use_case_code: LockAccountUseCase.CE }
          ]
        });
        
        const { result } = renderHookWithRedux(() => useLockInfo(FAKE_NAVIGATION));
        
        await waitFor(() => {
          expect(result.current.lockActions?.length).toBeGreaterThan(1);
        });
        
        result.current.resolveUserLockState();
        
        // Should show modal for multiple actions
        expect(mockShowModal).toHaveBeenCalled();
        
        const modalCall = mockShowModal.mock.calls[0];
        expect(modalCall[0].screen.props.lockActions).toEqual(result.current.lockActions);
        expect(typeof modalCall[0].screen.props.handleLockAction).toBe('function');
      });
    });

    // Test individual action types by mocking the handleLockAction callback
    describe('Action types behavior', () => {
      let mockHandleLockAction: jest.Mock;
      
      beforeEach(() => {
        // Mock InfoModalService to capture the handleLockAction function
        const mockShowModal = jest.fn().mockImplementation(({ screen }) => {
          mockHandleLockAction = screen.props.handleLockAction;
        });
        require('bnpl-shared/src/services').InfoModalService.showModal = mockShowModal;
      });

             it('should handle CONTACT_CIMB_SUPPORT action correctly', async () => {
         (getAccountLockInfoApi as jest.Mock).mockResolvedValue({
           is_lock: true,
           locks: [
             { use_case_code: LockAccountUseCase.PARTNER_FRAUD_DETECTION },
             { use_case_code: LockAccountUseCase.CE }
           ]
         });
         
         const { result } = renderHookWithRedux(() => useLockInfo(FAKE_NAVIGATION));
         
         await waitFor(() => {
           expect(result.current.lockActions?.length).toBeGreaterThan(1);
         });
         
         result.current.resolveUserLockState();
         
         // Simulate clicking on CONTACT_CIMB_SUPPORT action
         const action = {
           type: LockAccountAction.CONTACT_CIMB_SUPPORT,
           order: 3,
           useCaseCode: LockAccountUseCase.PARTNER_FRAUD_DETECTION
         };
         
         mockHandleLockAction(action);
         
         // Wait for the async IIFE in _handleLockAction to complete
         await waitFor(() => {
           expect(openLink).toHaveBeenCalledWith('tel:**********');
         });
       });

      it('should handle CONTACT_ZALOPAY_SUPPORT action with ZPI platform', async () => {
        (getAppInfo as jest.Mock).mockResolvedValue({ platform: 'ZPI' });
        (buildFaqUrl as jest.Mock).mockReturnValue('https://faq.zalopay.vn');
        
        (getAccountLockInfoApi as jest.Mock).mockResolvedValue({
          is_lock: true,
          locks: [
            { use_case_code: LockAccountUseCase.CE },
            { use_case_code: LockAccountUseCase.PARTNER_DPD }
          ]
        });
        
        const { result } = renderHookWithRedux(() => useLockInfo(FAKE_NAVIGATION));
        
        await waitFor(() => {
          expect(result.current.lockActions?.length).toBeGreaterThan(1);
        });
        
        result.current.resolveUserLockState();
        
        const action = {
          type: LockAccountAction.CONTACT_ZALOPAY_SUPPORT,
          order: 4,
          useCaseCode: LockAccountUseCase.CE
        };
        
        mockHandleLockAction(action);
        
        // Wait for async function to complete
        await waitFor(() => {
          expect(launchDeepLink).toHaveBeenCalledWith(
            'https://faq.zalopay.vn/integrate?target=web/faq-entry/***********&source=2&provider=Paylater&issueId=6'
          );
        });
      });

      it('should handle CONTACT_ZALOPAY_SUPPORT action with non-ZPI platform', async () => {
        (getAppInfo as jest.Mock).mockResolvedValue({ platform: 'ZALOPAY' });
        
        (getAccountLockInfoApi as jest.Mock).mockResolvedValue({
          is_lock: true,
          locks: [
            { use_case_code: LockAccountUseCase.CE },
            { use_case_code: LockAccountUseCase.PARTNER_DPD }
          ]
        });
        
        const { result } = renderHookWithRedux(() => useLockInfo(FAKE_NAVIGATION));
        
        await waitFor(() => {
          expect(result.current.lockActions?.length).toBeGreaterThan(1);
        });
        
        result.current.resolveUserLockState();
        
        const action = {
          type: LockAccountAction.CONTACT_ZALOPAY_SUPPORT,
          order: 4,
          useCaseCode: LockAccountUseCase.CE
        };
        
        mockHandleLockAction(action);
        
        await waitFor(() => {
          expect(launchDeepLink).toHaveBeenCalledWith(
            'zalopay://launch/app/-68?target=web/faq-entry/***********&source=1&provider=Paylater&issueId=6'
          );
        });
      });

             it('should handle PAY_DEBT_AND_CONTACT_CIMB_SUPPORT action', async () => {
         const mockNavigateTo = jest.fn();
         require('bnpl-shared/src/features/multipartner_integration/helpers').useMultiPartnerNavigate = () => ({
           navigateTo: mockNavigateTo,
         });
         
         (getAccountLockInfoApi as jest.Mock).mockResolvedValue({
           is_lock: true,
           locks: [
             { use_case_code: LockAccountUseCase.PARTNER_LOCK },
             { use_case_code: LockAccountUseCase.CE }
           ]
         });
         
         const { result } = renderHookWithRedux(() => useLockInfo(FAKE_NAVIGATION));
         
         await waitFor(() => {
           expect(result.current.lockActions?.length).toBeGreaterThan(1);
         });
         
         result.current.resolveUserLockState();
         
         const action = {
           type: LockAccountAction.PAY_DEBT_AND_CONTACT_CIMB_SUPPORT,
           order: 1,
           useCaseCode: LockAccountUseCase.PARTNER_LOCK
         };
         
         mockHandleLockAction(action);
         
         // Wait for the async IIFE in _handleLockAction to complete
         await waitFor(() => {
           expect(mockNavigateTo).toHaveBeenCalledWith('repay');
         });
       });

             it('should handle PAY_DEBT action', async () => {
         const mockNavigateTo = jest.fn();
         require('bnpl-shared/src/features/multipartner_integration/helpers').useMultiPartnerNavigate = () => ({
           navigateTo: mockNavigateTo,
         });
         
         (getAccountLockInfoApi as jest.Mock).mockResolvedValue({
           is_lock: true,
           locks: [
             { use_case_code: LockAccountUseCase.PARTNER_DPD },
             { use_case_code: LockAccountUseCase.CE }
           ]
         });
         
         const { result } = renderHookWithRedux(() => useLockInfo(FAKE_NAVIGATION));
         
         await waitFor(() => {
           expect(result.current.lockActions?.length).toBeGreaterThan(1);
         });
         
         result.current.resolveUserLockState();
         
         const action = {
           type: LockAccountAction.PAY_DEBT,
           order: 2,
           useCaseCode: LockAccountUseCase.PARTNER_DPD
         };
         
         mockHandleLockAction(action);
         
         // Wait for the async IIFE in _handleLockAction to complete
         await waitFor(() => {
           expect(mockNavigateTo).toHaveBeenCalledWith('repay');
         });
       });
    });
  });
});

import { getAccountLockInfoApi } from 'bnpl-shared/src/api/getAccountLockInfoApi';
import React, { useEffect, useState } from 'react';
import { LockAccountAction, LockAccountUseCase, LockAction } from 'bnpl-shared/src/types';
import { useMultiPartnerLockStatus, useMultiPartnerNavigate, usePartnerData } from 'bnpl-shared/src/features/multipartner_integration/helpers';
import { INVALID_ACCOUNT_ID } from 'bnpl-shared/src/constants';
import { CIMB_TEL } from 'bnpl-shared/src/constants/PublicUrls';
import { openLink } from 'bnpl-shared/src/utils/openLink';
import { getAppInfo, launchDeepLink } from 'bnpl-shared/src/shared/ZaloPayModules/ZaloPayModules';
import { buildFaqUrl } from 'bnpl-shared/src/api/buildFaqUrl';
import { useLockAccountByUser } from 'bnpl-shared/src/features/lock_account_by_user';
import { TUseNavigation } from 'bnpl-shared/src/shared/navigation';
import { InfoModalService } from 'bnpl-shared/src/services';
import LockAccountDetailModal from '.';
import { ModalType } from 'bnpl-shared/src/services';
import { useDispatch } from 'react-redux';
import { setAppToast, ToastType } from 'bnpl-shared/src/redux/appToastReducer';

export const useLockInfo = (navigation: TUseNavigation) => {
  const { getChosenPartner } = usePartnerData();
  const { unlockAccount } = useLockAccountByUser();
  const { setLockStatus } = useMultiPartnerLockStatus();
  const dispatch = useDispatch();
  const mpNavigator = useMultiPartnerNavigate(navigation);
  const chosenPartnerAccountId = getChosenPartner()?.account_id;
  const [lockActions, setLockActions] = useState<LockAction[]>();

  useEffect(() => {
    _fetchLockInfos();
  }, []);

  const resolveUserLockState = () =>{
    if(!lockActions || lockActions.length === 0){
      return;
    }
    if(lockActions && lockActions.length === 0){
      mpNavigator.navigateTo('home');
      setLockStatus(false);
      dispatch(
        setAppToast({
          message: 'Đã mở khóa tài khoản',
          type: ToastType.SUCCESS,
          duration: 5000,
        }),
      );
      return;
    }

    const shouldQuickUnlock = lockActions.length == 1 && lockActions[0].type == LockAccountAction.UNLOCK_ACCOUNT
    if(shouldQuickUnlock){
      _handleLockAction(lockActions[0]);
    }else{
      InfoModalService.showModal({
        screen: <LockAccountDetailModal lockActions={lockActions} handleLockAction={_handleLockAction} />,
        type: ModalType.BOTTOM_SHEET,
          bottomSheetProps: {
            title: 'Tài khoản tạm khóa chi tiêu',
          },
      });
    }
  }

  const _mapUseCaseToAction = (useCaseCode: string): LockAction => {
    switch (useCaseCode) {
      case LockAccountUseCase.ACCOUNT:
        return { type: LockAccountAction.UNLOCK_ACCOUNT, order: 1, useCaseCode };
      case LockAccountUseCase.PARTNER_LOCK:
        return { type: LockAccountAction.PAY_DEBT_AND_CONTACT_CIMB_SUPPORT, order: 1, useCaseCode };
      case LockAccountUseCase.PARTNER_DPD:
        return { type: LockAccountAction.PAY_DEBT, order: 2, useCaseCode };
      default:
      case LockAccountUseCase.CE:
      case LockAccountUseCase.FS_ADMIN:
      case LockAccountUseCase.RISK:
      case LockAccountUseCase.UNSPECIFIED:
      case LockAccountUseCase.UM:
        return { type: LockAccountAction.CONTACT_ZALOPAY_SUPPORT, order: 4, useCaseCode };
      case LockAccountUseCase.PARTNER_FRAUD_DETECTION:
        return { type: LockAccountAction.CONTACT_CIMB_SUPPORT, order: 3, useCaseCode };
    }
  };

  const _handleLockAction = (action: LockAction) => {
    (async () => {
      const appInfo = await getAppInfo();
      switch (action.type) {
        case LockAccountAction.CONTACT_CIMB_SUPPORT:
          openLink(`tel:${CIMB_TEL}`);
          break;
        case LockAccountAction.CONTACT_ZALOPAY_SUPPORT:
          if (appInfo.platform === 'ZPI') {
            const faqUrl = `${buildFaqUrl()}/integrate?target=web/faq-entry/***********&source=2&provider=Paylater&issueId=6`;
            launchDeepLink(faqUrl);
          } else {
            launchDeepLink(
                'zalopay://launch/app/-68?target=web/faq-entry/***********&source=1&provider=Paylater&issueId=6',
            );
          }
          break;
        case LockAccountAction.PAY_DEBT_AND_CONTACT_CIMB_SUPPORT:
        case LockAccountAction.PAY_DEBT:
          mpNavigator.navigateTo('repay');
          break;
        case LockAccountAction.UNLOCK_ACCOUNT:
          try {
            const result = await unlockAccount();
            if (result) {
              mpNavigator.navigateTo('home');
            }
          } catch (e: any) {
            console.log("Error unlocking account:", e);
          }
          break;
      }
    })()
  }

  const _fetchLockInfos = () => {
    (async () => {
      if (chosenPartnerAccountId && chosenPartnerAccountId !== INVALID_ACCOUNT_ID) {
        const response = await getAccountLockInfoApi(chosenPartnerAccountId);
        if (response.is_lock && response.locks.length) {
          const actions: LockAction[] = [];
          response.locks.forEach(item => {
            //map use case to action
            let mapActionItem = _mapUseCaseToAction(item.use_case_code);
            //only add action if it's not exist
            if (!actions.find(item => item.type === mapActionItem.type)) {
              actions.push(mapActionItem);
            }
          });
          //sort action ascending by order
          setLockActions(actions.sort((a, b) => (b.order - a.order > 0 ? -1 : 1)));
        } else {
          setLockActions([]);
        }
      }
    })();
  };
  return { resolveUserLockState, lockActions };
};

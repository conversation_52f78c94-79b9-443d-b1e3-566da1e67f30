import React from 'react';
import { TouchableOpacity, View } from 'react-native';
import { StyleSheet } from 'bnpl-shared/src/shared/styles/StyleSheet';
import { useHistory, useLocation } from 'react-router-dom';
import { AppText } from 'bnpl-shared/src/shared/react-native-customized';
import { AppColors } from 'bnpl-shared/src/constants';
import GeneralBackIc24 from 'bnpl-shared/src/components/SVGComponents/GeneralBackIc24';

export function NavigationBar({
  background = AppColors.primary,
  color = AppColors.white,
  title = '',
  height,
}: NavigationBarProps) {
  const { key } = useLocation();
  const history = useHistory();
  // not check history.length !== 1 because the history always has at least two screens because of the splash screen
  const backButton = key && key !== 'default' && history.length > 2;
  return (
    <View style={[styles.container, { backgroundColor: background, height: height }]}>
      <View style={styles.navigationBar}>
        {backButton && (
          <TouchableOpacity activeOpacity={1} style={styles.button} onPress={history.goBack}>
            <GeneralBackIc24 color={color} />
          </TouchableOpacity>
        )}
        <AppText bold size={16} height={20} color={color}>
          {title}
        </AppText>
      </View>
    </View>
  );
}

interface NavigationBarProps {
  className?: string;
  background?: string;
  color?: string;
  title?: string;
  height: number;
}

const styles = StyleSheet.create({
  container: {
    flexDirection: 'row',
    alignItems: 'flex-end',
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    zIndex: 999,
  },
  navigationBar: {
    height: 60,
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'flex-start',
    paddingHorizontal: 16,
    zIndex: 50,
  },
  button: {
    width: 24,
    height: 24,
    marginRight: 8,
  },
  title: {
    fontSize: 16,
    fontWeight: 'bold',
    color: AppColors.white,
  },
});

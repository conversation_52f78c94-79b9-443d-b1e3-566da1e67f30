import React, { useEffect, useRef } from 'react';
import { HTMLAttributes } from 'react';
import styled, { css } from 'styled-components';
import { calculateScaleDimension, StyleSheet } from 'bnpl-shared/src/shared/styles/StyleSheet';
import { ScrollMaskConfig } from './types';
import { Animated } from 'react-native';

export function ScrollMask({
  opacity,
  background = 'linear-gradient(180deg, white, rgba(255, 255, 255, 0.6), transparent)',
  height = 50,
}: ScrollMaskProps) {
  const animationOpacity = useRef(new Animated.Value(opacity || 0)).current;
  useEffect(() => {
    Animated.timing(animationOpacity, {
      toValue: opacity || 0,
      duration: 300,
      useNativeDriver: true,
    }).start();
  }, [opacity]);
  return (
    <Animated.View style={[styles.mask, { backgroundColor: background, opacity: animationOpacity, height: calculateScaleDimension(height) }]} />
  )
}

const styles = StyleSheet.create({
  mask: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    width: '100%',
    zIndex: 100,
    // pointerEvents: 'none',
  },
});

interface ScrollMaskProps extends ScrollMaskConfig {
  enable?: boolean;
  opacity?: number;
  fullHeightNavigationBar?: boolean;
}

const Mask = styled.div<HTMLAttributes<HTMLButtonElement> & ScrollMaskProps>`
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  width: 100%;
  transition: opacity 0.3s ease;
  pointer-events: none;
  z-index: 100;
  ${({ opacity, height, background }) => css`
    opacity: ${opacity};
    height: ${height}px;
    background: ${background};
  `}
`;

import React from 'react';
import { createContext, ReactNode, useContext, useReducer, useRef } from 'react';
import { ScrollMaskConfig } from './types';
import { AppColors } from 'bnpl-shared/src/constants';

const initialState: NavigationState = {
  fullScreenData: null,
  title: 'Tài khoản trả sau',
  navigationOptions: {
    background: AppColors.blue[500],
    color: AppColors.white,
  },
  customNavigationOptions: {},
};

type NavigationAction =
  | {
      type: 'SET_TITLE';
      payload: string;
    }
  | {
      type: 'SET_CUSTOM_NAVIGATION_OPTIONS';
      payload: {
        path: string;
        options: CustomNavigationOptions;
      };
    }
  | {
      type: 'SET_FULLSCREEN_DATA';
      payload: {
        fullScreen: boolean;
        navigationBarHeight: number;
      };
    };

interface NavigationState {
  fullScreenData?: {
    fullScreen: boolean;
    navigationBarHeight: number;
  } | null;
  title: string;
  navigationOptions?: DefaultNavigationOptions;
  customNavigationOptions: Partial<Record<string, CustomNavigationOptions>>;
}

export interface DefaultNavigationOptions {
  className?: string;
  background?: string;
  color?: string;
  isWhiteStatusBar?: boolean;
}

export interface CustomNavigationOptions extends DefaultNavigationOptions {
  noNavigationBar?: boolean;
  overlappingPageContent?: boolean;
  scrollMask?: ScrollMaskConfig & {
    enable?: boolean;
    fullHeightNavigationBar?: boolean;
  };
}

// Reducer function
function navigationReducer(state: NavigationState, action: NavigationAction): NavigationState {
  switch (action.type) {
    case 'SET_TITLE':
      return { ...state, title: action.payload };
    case 'SET_CUSTOM_NAVIGATION_OPTIONS': {
      const { payload } = action;
      return {
        ...state,
        customNavigationOptions: {
          ...state.customNavigationOptions,
          [payload.path]: {
            ...state.customNavigationOptions[payload.path],
            ...payload.options,
          },
        },
      };
    }
    case 'SET_FULLSCREEN_DATA':
      return {
        ...state,
        fullScreenData: action.payload,
      };
    default:
      return state;
  }
}

export const NavigationContext = createContext<
  | {
      state: NavigationState;
      dispatch: React.Dispatch<NavigationAction>;
    }
  | undefined
>(undefined);

interface AppProviderProps {
  children: ReactNode;
  navigationOptions?: DefaultNavigationOptions;
  customNavigationOptions?: Record<string, CustomNavigationOptions>;
}

export function NavigationProvider({ children, ...otherProps }: AppProviderProps) {
  const initialStateData = useRef({ ...initialState, ...otherProps }).current;
  const [state, dispatch] = useReducer(navigationReducer, initialStateData);
  return (
    <NavigationContext.Provider
      value={{
        state,
        dispatch,
      }}
    >
      {children}
    </NavigationContext.Provider>
  );
}

// Custom hook to access the context
export const useNavigationContext = (): {
  state: NavigationState;
  dispatch: React.Dispatch<NavigationAction>;
} => {
  const context = useContext(NavigationContext);
  if (!context) {
    throw new Error('useNavigationContext must be used within an NavigationProvider');
  }
  return context;
};

export const useCurrentNavigationOptions = (pathName: string): (DefaultNavigationOptions & CustomNavigationOptions) => {
  const { state } = useNavigationContext();
  const customOptionKey = Object.keys(state.customNavigationOptions).find(key => {
    return pathName.includes(key);
  });
  if (customOptionKey && state.customNavigationOptions[customOptionKey]) {
    return {
      ...state.navigationOptions,
      ...state.customNavigationOptions[customOptionKey],
    };
  }
  return {
    ...state.navigationOptions,
  }
};

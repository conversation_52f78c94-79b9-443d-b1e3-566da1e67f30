import React from 'react';
import { NavigationBar } from './NavigationBar';
import { ReactNode, useEffect, useRef, useState } from 'react';
import styled from 'styled-components';
import {
  DefaultNavigationOptions,
  CustomNavigationOptions,
  NavigationProvider,
  useCurrentNavigationOptions,
  useNavigationContext,
} from './context';
import { useLocation } from 'react-router-dom';

import { ScrollMask } from './ScrollMask';
import useFullScreen from "bnpl-shared/src/hooks/useFullScreen";
import { View } from 'react-native';
import { toast } from '../Toaster';
import { calculateScaleDimension } from 'bnpl-shared/src/shared/styles/StyleSheet';
export * from './context';

export function NavigationWrapper({ children, navigationOptions, customNavigationOptions = {} }: NavigationWrapperProps) {
  return (
    <NavigationProvider navigationOptions={navigationOptions} customNavigationOptions={customNavigationOptions}>
      <NavigationConsumer>{children}</NavigationConsumer>
    </NavigationProvider>
  );
}

interface NavigationWrapperProps {
  children?: ReactNode;
  navigationOptions?: DefaultNavigationOptions;
  customNavigationOptions?: Record<string, CustomNavigationOptions>;
}

function NavigationConsumer({ className, contentClassName, children }: NavigationConsumerProps) {
  const { pathname } = useLocation();
  const { state, dispatch } = useNavigationContext();
  const fullScreenData = useFullScreen();
  const scrollRef = useRef<HTMLDivElement | null>(null);
  const [maskOpacity, setMaskOpacity] = useState(0);
  const currentNavigationOptions = useCurrentNavigationOptions(pathname);
  const availableFullScreen = fullScreenData?.fullScreen;
  const showNavigationBar = !currentNavigationOptions.noNavigationBar && availableFullScreen;
  const showScrollMask = currentNavigationOptions?.scrollMask?.enable && availableFullScreen;
  const navigationBarOverlappingPageContent = currentNavigationOptions?.overlappingPageContent || !showNavigationBar;
  useEffect(() => {
    if (state.title) document.title = state.title;
  }, [state.title]);

  useEffect(() => {
    (window as any)?.zlpSdk?.UI.changeStatusBarTextColor({
      isWhiteText: currentNavigationOptions?.isWhiteStatusBar ?? true,
    });
  }, [currentNavigationOptions?.isWhiteStatusBar]);

  useEffect(() => {
    if (fullScreenData) {
      dispatch({
        type: 'SET_FULLSCREEN_DATA',
        payload: fullScreenData,
      });
      toast.setNavigationBarHeight(calculateScaleDimension(fullScreenData?.navigationBarHeight ?? 0));
    }
  }, [dispatch, fullScreenData]);

  function handleScroll() {
    if (!scrollRef.current) return;
    if (scrollRef.current.scrollTop > 15) {
      setMaskOpacity(1);
      return;
    }
    setMaskOpacity(0);
  }

  return (
    <Wrapper className={className}>
      {showNavigationBar && state.fullScreenData?.navigationBarHeight && (
        <NavigationBar
          {...currentNavigationOptions}
          title={state.title}
          height={state.fullScreenData?.navigationBarHeight}
        />
      )}
      <div style={{ height: '100%', overflow: 'hidden' }}>
        <Content ref={scrollRef} onScroll={handleScroll} className={contentClassName}>
          {showScrollMask && (
            <ScrollMask
              {...currentNavigationOptions?.scrollMask}
              opacity={maskOpacity}
              height={
                currentNavigationOptions?.scrollMask?.fullHeightNavigationBar
                  ? state.fullScreenData?.navigationBarHeight
                  : currentNavigationOptions?.scrollMask?.height
              }
            />
          )}
          {!navigationBarOverlappingPageContent ? (
            <>
              <View style={{ height: state.fullScreenData?.navigationBarHeight ?? 0, flexShrink: 0 }} />
              {children}
            </>
          ) : (
            children
          )}
        </Content>
      </div>
    </Wrapper>
  );
}

interface NavigationConsumerProps {
  className?: string;
  contentClassName?: string;
  navigationLeftButton?: {
    icon?: ReactNode;
    onClick?: () => void;
  };
  children?: ReactNode;
}

const Wrapper = styled.div`
  height: 100vh;
  overflow: hidden;
  display: flex;
  flex-direction: column;
  position: relative;
`;

const Content = styled.div`
  display: flex;
  flex-direction: column;
  overflow-y: auto;
  height: 100%;
  overscroll-behavior-y: none;
  &::-webkit-scrollbar {
    display: none;
  }
  -ms-overflow-style: none;
  scrollbar-width: none;
`;

import { default as PersonalExtraDataInput } from './';
import { renderWithRedux } from 'bnpl-shared/src/jest/renderWithRedux';
import React, { FC } from 'react';
import { fireEvent, waitFor } from '@testing-library/react-native';
import { capitalize } from 'lodash';
import InfoModal from 'bnpl-shared/src/components/InfoModal';
import { infoModalRef, InfoModalService } from 'bnpl-shared/src/services';
import { Item } from 'bnpl-shared/src/types';

const mockGetValueForResourceType = jest.fn();
const mockSetValueForResource = jest.fn();
const mockFetchChoicesMock = jest.fn();

jest.mock('bnpl-shared/src/screens/onboarding-flow/PersonalExtraDataScreen/usePersonalExtraDataController', () => ({
  usePersonalExtraDataController: () => ({
    fetchChoices: mockFetchChoicesMock,
    getChoicesForResourceType: jest.fn().mockReturnValue([
      { id: '0', value: 'value_0' },
      { id: '1', value: 'value_1' },
      { id: '2', value: 'value_2' },
      { id: '3', value: 'value_3' },
      { id: '4', value: 'value_11' },
    ]),
    getValueForResourceType: mockGetValueForResourceType,
    setValueForResource: mockSetValueForResource,
  }),
}));

/**
 * written with GitHub CoPilot support
 */
describe(PersonalExtraDataInput.name, () => {
  beforeEach(() => {
    InfoModalService.hideModal();
    (mockGetValueForResourceType as jest.Mock).mockReset();
  });

  const Subject: FC<{ onPicked?: (item: Item) => void }> = ({ onPicked }) => {
    return (
      <>
        <PersonalExtraDataInput onPicked={onPicked} resourceType="OCCUPATION" />
        <InfoModal ref={infoModalRef} />
      </>
    );
  };

  it('render component default state as expected', () => {
    const { queryByText } = renderWithRedux(<Subject />);
    expect(queryByText('Chọn')).toBeTruthy();
  });
  it('verify picker popup render as expected', () => {
    const { getByText, queryByText } = renderWithRedux(<Subject />);
    fireEvent.press(getByText('Chọn'));
    expect(queryByText('value_0')).toBeTruthy();
    expect(queryByText('value_1')).toBeTruthy();
    expect(queryByText('value_2')).toBeTruthy();
    expect(queryByText('value_3')).toBeTruthy();
    expect(queryByText('value_11')).toBeTruthy();
  });
  it('verify filter input work as expected', async () => {
    const { getByText, queryByText, getByPlaceholderText, queryByPlaceholderText } = renderWithRedux(<Subject />);
    fireEvent.press(getByText('Chọn'));
    await waitFor(() => expect(queryByPlaceholderText('Tìm kiếm tên công việc')).toBeTruthy());
    fireEvent.changeText(getByPlaceholderText('Tìm kiếm tên công việc'), 'value_1');
    expect(queryByText('value_0')).toBeFalsy();
    expect(queryByText('value_1')).toBeTruthy();
    expect(queryByText('value_2')).toBeFalsy();
    expect(queryByText('value_3')).toBeFalsy();
    expect(queryByText('value_11')).toBeTruthy();
  });
  it('verify select item work as expected', async () => {
    const onPicked = jest.fn();
    const { getByText, queryByText } = renderWithRedux(<Subject onPicked={onPicked} />);
    fireEvent.press(getByText('Chọn'));
    await waitFor(() => expect(queryByText('value_1')).toBeTruthy());
    fireEvent.press(getByText('value_1'));
    expect(mockSetValueForResource).toHaveBeenCalledWith('OCCUPATION', '1');
    await waitFor(() => expect(queryByText(capitalize('tên công việc'))).toBeFalsy());
    expect(onPicked).toHaveBeenCalledWith({ id: '1', value: 'value_1' });
  });
  it('verify render picked value as expected', async () => {
    (mockGetValueForResourceType as jest.Mock).mockImplementation(params => {
      if (params === 'OCCUPATION') {
        return '4';
      }
    });
    const { queryByText, getByText, queryByTestId } = renderWithRedux(<Subject />);
    expect(queryByText('value_11')).toBeTruthy();
    fireEvent.press(getByText('value_11'));
    await waitFor(() => expect(queryByTestId('picker-item-4')?.findByProps({ testID: 'check-icon' })).toBeTruthy());
  });
  it('verify PickBottomSheet is closed as expected', async () => {
    const { getByText, queryByText, getByTestId } = renderWithRedux(<Subject />);
    fireEvent.press(getByText('Chọn'));
    await waitFor(() => expect(queryByText(capitalize('tên công việc'))).toBeTruthy());
    fireEvent.press(getByTestId('close-button'));
    await waitFor(() => expect(queryByText(capitalize('tên công việc'))).toBeFalsy());
  });
});

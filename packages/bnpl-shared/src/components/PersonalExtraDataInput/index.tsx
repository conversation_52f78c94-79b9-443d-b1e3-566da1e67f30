//#region PersonalExtraDataInput
import React, { forwardRef, useCallback, useEffect, useImperativeHandle, useState } from 'react';
import { usePersonalExtraDataController } from 'bnpl-shared/src/screens/onboarding-flow/PersonalExtraDataScreen/usePersonalExtraDataController';
import { StyleProp, TouchableOpacity, ViewStyle } from 'react-native';
import { AppText } from 'bnpl-shared/src/shared/react-native-customized';
import { Spacer } from 'bnpl-shared/src/components/Spacer';
import { ChevronIcon } from 'bnpl-shared/src/shared/ChevronIcon';
import { PickerBottomSheet } from 'bnpl-shared/src/screens/onboarding-flow/PersonalExtraDataScreen/PickerBottomSheet/PickerBottomSheet';
import { capitalize, isEmpty } from 'lodash';
import { StyleSheet } from 'bnpl-shared/src/shared/styles/StyleSheet';
import { Item, ResourceType } from 'bnpl-shared/src/types';
import {
  trackBod2RequireInfoModalDisplay,
  trackBod2RequireInfoModalItemSelect,
  trackBod2SelectRequireInfo,
} from 'bnpl-shared/src/screens/onboarding-flow/AddInformationScreen/tracking';
import { InfoModalService, ModalType } from 'bnpl-shared/src/services';
import { windowHeight } from 'bnpl-shared/src/shared/styles/StyleUtils';

type Props = {
  resourceType: ResourceType;
  formatter?: (value: string) => string;
  priorityIds?: string[];
  style?: StyleProp<ViewStyle>;
  onPicked?: (item: Item) => void;
};

export type PersonalExtraDataInputRef = {
  showPicker: () => void;
  isInputEmpty: () => boolean;
};

const PersonalExtraDataInput = forwardRef<PersonalExtraDataInputRef | undefined, Props>(
  ({ resourceType, formatter, priorityIds, style, onPicked }, ref) => {
    const { getChoicesForResourceType, getValueForResourceType, setValueForResource } =
      usePersonalExtraDataController();
    const choices = getChoicesForResourceType(resourceType).map(choice => ({
      ...choice,
      value: formatter ? formatter(choice.value) : choice.value,
    }));
    const value = getValueForResourceType(resourceType);
    const name = choices.find(({ id }) => id === value);
    const styles = personalExtraDataInputStyles;
    const handleClose = () => {
      InfoModalService.hideModal();
    };

    const [pickedValue, setPickedValue] = useState<{ item: Item | undefined; source: 'props' | 'state' }>();

    //Apply the value from props, which is updated from redux store
    useEffect(() => {
      if (name && pickedValue?.item?.id !== name?.id) {
        setPickedValue({ item: name, source: 'props' });
      }
    }, [name]);

    //Handle when state is updated, set the value to redux store and call onPicked callback
    useEffect(() => {
      if (pickedValue?.item && pickedValue.source === 'state') {
        setValueForResource(resourceType, pickedValue.item.id);
        onPicked?.(pickedValue.item);
      }
    }, [pickedValue]);

    const showPickerModal = useCallback(() => {
      InfoModalService.hideModal();
      InfoModalService.showModal({
        screen: (
          <PickerBottomSheet
            testID={`picker-bottom-sheet-${resourceType.toLowerCase()}`}
            priorityIds={priorityIds}
            onRequestClose={handleClose}
            items={choices}
            value={value}
            placeholder={`Tìm kiếm ${mapResourceTypeToTitle(resourceType)}`}
            resourceType={resourceType}
            onChange={item => {
              trackBod2RequireInfoModalItemSelect(resourceType.toLowerCase(), item.value);
              handleClose();
              setPickedValue({ item, source: 'state' });
            }}
          />
        ),
        type: ModalType.BOTTOM_SHEET,
        bottomSheetProps: {
          title: capitalize(mapResourceTypeToTitle(resourceType)),
          containerStyle: { height: windowHeight * 0.7 },
          enableKeyboardAvoidingView: false,
        },
      });
    }, [resourceType, priorityIds, choices, value, setValueForResource]);

    const isInputEmpty = useCallback(() => {
      return pickedValue?.item === undefined;
    }, [pickedValue]);

    useImperativeHandle(
      ref,
      () => ({
        showPicker: () => {
          trackBod2RequireInfoModalDisplay(resourceType.toLowerCase());
          showPickerModal();
        },
        isInputEmpty,
      }),
      [name, showPickerModal],
    );

    const displayValue = pickedValue?.item?.value;

    const onPress = () => {
      trackBod2SelectRequireInfo();
      showPickerModal();
    };

    return (
      <TouchableOpacity disabled={choices.length === 0} style={[styles.root, style]} onPress={onPress}>
        <AppText testID="value-text" style={styles.text}>
          {isEmpty(displayValue) ? 'Chọn' : displayValue}
        </AppText>
        <Spacer width={12} />
        <ChevronIcon onPress={onPress} direction="down" />
      </TouchableOpacity>
    );
  },
);

const personalExtraDataInputStyles = StyleSheet.create({
  root: { flexDirection: 'row', alignItems: 'center', paddingLeft: 8, flex: 1 },
  text: {
    flex: 1,
    textAlign: 'right',
  },
});

const mapResourceTypeToTitle = (resourceType: ResourceType) => {
  switch (resourceType) {
    case 'OCCUPATION':
      return 'tên công việc';
    case 'EMPLOYMENT_STATUS':
      return 'tình trạng công việc';
    case 'JOB_TITLE':
      return 'chức danh';
    case 'SOURCE_OF_FUND':
      return 'nguồn thu nhập';
    case 'INCOME':
      return 'thu nhập';
    case 'EDUCATION':
      return 'trình độ học vấn';
    case 'CITY':
      return 'nơi sống';
    case 'LOAN_PURPOSE':
      return 'mục đích vay';
    case 'STATEMENT_DATE':
      return 'ngày sao kê';
  }
};

export default PersonalExtraDataInput;
//#endregion

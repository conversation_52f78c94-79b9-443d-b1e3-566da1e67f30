/**
 * <PERSON><PERSON> from MMF project
 * Source code is <NAME_EMAIL>,
 */
import React from 'react';
import { fireEvent, render } from '@testing-library/react-native';
import PinInput from '.';

describe(PinInput.name, () => {
  it('render and submit pin', () => {
    const handleChangePin = jest.fn();
    const { getByTestId } = render(<PinInput value="" onChange={handleChangePin} />);
    expect(getByTestId('pin-input')).toBeTruthy();
    fireEvent.changeText(getByTestId('pin-input'), '123456');
    expect(handleChangePin).toBeCalledWith('123456');
  });

  it('show error wrong pin', () => {
    const { queryByText } = render(
      <PinInput value="" onChange={jest.fn()} error="Mật khẩu không đúng. Bạn còn 4 lần thử" />,
    );
    expect(queryByText('Mật khẩu không đúng. Bạn còn 4 lần thử')).toBeTruthy();
  });
});

/**
 * <PERSON><PERSON> from MMF project
 * Source code is <NAME_EMAIL>,
 */

import React, { useEffect, useRef } from 'react';
import { StyleProp, TextInput, TouchableWithoutFeedback, View, ViewStyle } from 'react-native';
import { AppText, AppTextInput } from 'bnpl-shared/src/shared/react-native-customized';
import { AppColors } from 'bnpl-shared/src/constants';
import { StyleSheet } from 'bnpl-shared/src/shared/styles/StyleSheet';

require('./styles.css');

type PinInputProps = {
  style?: StyleProp<ViewStyle>;
  error?: string;
  value: string;
  onChange: (value: string) => void;
};

const PinInput = ({ style, value, onChange, error }: PinInputProps) => {
  const numberInputRef = useRef<TextInput>(null);

  useEffect(() => {
    setTimeout(() => {
      numberInputRef?.current?.focus?.();
    }, 500);
  }, []);

  return (
    <div className="pin-input">
      <View style={style}>
        <TouchableWithoutFeedback onPress={() => numberInputRef?.current?.focus?.()}>
          <View style={styles.layerMask}>
            <View style={[styles.pinItem, value.length > 0 && styles.pinInputed]} />
            <View style={[styles.pinItem, value.length > 1 && styles.pinInputed]} />
            <View style={[styles.pinItem, value.length > 2 && styles.pinInputed]} />
            <View style={[styles.pinItem, value.length > 3 && styles.pinInputed]} />
            <View style={[styles.pinItem, value.length > 4 && styles.pinInputed]} />
            <View style={[styles.pinItem, value.length > 5 && styles.pinInputed]} />
          </View>
        </TouchableWithoutFeedback>
        <AppTextInput
          ref={numberInputRef}
          testID="pin-input"
          autoFocus={false}
          style={styles.input}
          textStyle={styles.textInput}
          borderStyle={styles.borderInput}
          keyboardType="number-pad"
          maxLength={6}
          value={value}
          onChangeText={onChange}
          selectionColor={AppColors.white}
        />
        {error ? (
          <AppText style={styles.pinWrong} size={16} height={24} color={AppColors.transactionFailed}>
            {error}
          </AppText>
        ) : null}
      </View>
    </div>
  );
};

const styles = StyleSheet.create({
  layerMask: {
    justifyContent: 'center',
    flexDirection: 'row',
  },
  pinItem: {
    width: 20,
    height: 20,
    borderRadius: 10,
    borderColor: AppColors.primary,
    borderWidth: 2,
    backgroundColor: AppColors.white,
    marginLeft: 8,
    marginRight: 8,
  },
  pinInputed: {
    backgroundColor: AppColors.primary,
  },
  pinWrong: {
    marginTop: 20,
  },
  input: {
    position: 'absolute',
    zIndex: -1,
    top: -6,
    width: '100%',
  },
  textInput: {
    color: AppColors.white,
  },
  borderInput: {
    borderWidth: 0,
  },
});

export default PinInput;

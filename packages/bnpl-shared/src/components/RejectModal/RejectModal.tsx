import * as React from 'react';
import { useEffect } from 'react';
import { AppImage, AppModal, AppText } from 'bnpl-shared/src/shared/react-native-customized';
import { Platform, View } from 'react-native';
import { Colors, size, StyleUtils } from 'bnpl-shared/src/shared/styles/StyleUtils';
import { StyleSheet } from 'bnpl-shared/src/shared/styles/StyleSheet';
import { images } from 'bnpl-shared/src/res';
import { Spacer } from 'bnpl-shared/src/components/Spacer';
import BottomButton from 'bnpl-shared/src/shared/BottomButton';
import { BottomSheetLayout } from 'bnpl-shared/src/shared/BottomSheet/BottomSheetLayout';
import {
  trackOnboardingDialogCta,
  trackOnboardingDialogVisible,
} from 'bnpl-shared/src/screens/onboarding-flow/OnboardingScreen/tracking';
import { Bod1SubmitStatus } from 'bnpl-shared/src/types';
import { closeMiniApp } from 'bnpl-shared/src/utils/closeMiniApp';

export type TRejectModalParams = Parameters<
  (title: string, message: string, location?: string, callback?: () => void) => void
>;

export type TRejectModalHandle = {
  showDialog: (...args: TRejectModalParams) => void;
  hideDialog: () => void;
};

const RejectModal = React.forwardRef<TRejectModalHandle, {}>((_, ref) => {
  const [visible, setVisible] = React.useState(false);
  const [params, setParams] =
    React.useState<{ title: string; desc: string; location?: string; callback?: () => void }>();

  React.useImperativeHandle(ref, () => ({
    showDialog: (...args: TRejectModalParams) => {
      setParams({ title: args[0], desc: args[1], location: args[2], callback: args[3] });
      setVisible(true);
    },
    hideDialog: handleClose,
  }));

  const handleClose = () => {
    setVisible(false);
  };

  useEffect(() => {
    if (visible) {
      trackOnboardingDialogVisible({
        result: Bod1SubmitStatus.REJECTED,
        reject_code: '',
        location: params?.location || '',
      });
    }
  }, [visible]);

  return (
    <AppModal visible={visible} transparent={true} onRequestClose={handleClose} animationType="fade">
      <BottomSheetLayout
        content={
          <View style={styles.root}>
            <AppImage {...size(180)} source={images.ImageLockoutRejected} />
            <Spacer height={24} />
            <AppText bold size={16} height={20}>
              {params?.title}
            </AppText>
            <Spacer height={8} />
            <AppText center color={Colors.text2}>
              {params?.desc}
            </AppText>
            <Spacer height={64} />
            <BottomButton
              style={StyleUtils.fullWidth}
              title="Trở về"
              onPress={() => {
                trackOnboardingDialogCta({ button_name: 'back', location: params?.location || '' });
                setVisible(false);
                if (params?.callback) {
                  params?.callback();
                } else {
                  closeMiniApp();
                }
              }}
            />
          </View>
        }
      />
    </AppModal>
  );
});

const styles = StyleSheet.create({
  root: {
    height: 400,
    justifyContent: 'center',
    alignItems: 'center',
    padding: 16,
  },
  message: {
    textAlign: 'center',
    lineHeight: Platform.OS === 'web' ? 18 : 1.25,
    marginTop: 12,
    marginBottom: 16,
    paddingHorizontal: 24,
  },
  button: {
    borderRadius: 0,
    borderBottomRightRadius: 4,
    borderBottomLeftRadius: 4,
    alignItems: 'center',
    paddingVertical: 14,
    paddingHorizontal: 16,
    backgroundColor: '#008FE5',
  },
  icon: {
    alignItems: 'center',
    marginTop: 22,
  },
});

export default RejectModal;

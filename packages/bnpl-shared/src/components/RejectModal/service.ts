import * as React from 'react';
import { TRejectModalHandle, TRejectModalParams } from 'bnpl-shared/src/components/RejectModal/RejectModal';

export const rejectModalRef = React.createRef<TRejectModalHandle>();

const showDialog = (...args: TRejectModalParams) => {
  rejectModalRef.current?.showDialog?.(...args);
};

const hideDialog = () => {
  rejectModalRef.current?.hideDialog?.();
};

export const rejectModalService = {
  showDialog,
  hideDialog,
};

import { Platform, ScrollView, StyleProp, ViewStyle } from 'react-native';
import { AppColors } from 'bnpl-shared/src/constants';
import { windowHeight, windowWidth } from 'bnpl-shared/src/shared/styles/StyleUtils';
import React, { FC, ReactNode, useEffect, useRef, useState } from 'react';
import { FullScreenFormLayout, FullScreenFormLayoutRef } from 'bnpl-shared/src/components/layouts/FullScreenFormLayout';
import { StyleSheet } from 'bnpl-shared/src/shared/styles/StyleSheet';
import { getBottomSafe } from 'bnpl-shared/src/utils';
import AppRefreshControl from 'bnpl-shared/src/components/AppRefreshControl';
import { AppImage } from 'bnpl-shared/src/shared/react-native-customized';
import { images } from 'bnpl-shared/src/res';

type RepayScreenContainerProps = {
  action: ReactNode;
  onRefreshing(): Promise<void>;
  onScroll?: ScrollView['props']['onScroll'];
  enableBackground?: boolean;
  actionsStyles?: StyleProp<ViewStyle>;
  scrollEnable?: boolean;
};

export const RepayScreenContainer: FC<RepayScreenContainerProps> = ({
  onScroll,
  action,
  children,
  onRefreshing,
  enableBackground = true,
  scrollEnable = true,
  actionsStyles,
}) => {
  const [refreshing, setRefreshing] = useState(false);
  const layoutRef = useRef<FullScreenFormLayoutRef>(null);
  const handleOnRefreshing = async () => {
    setRefreshing(true);
    await onRefreshing?.();
    setRefreshing(false);
  };

  useEffect(() => {
    if (!scrollEnable) {
      layoutRef.current?.setScrollEnabled(false);
    }
  }, [scrollEnable]);

  return (
    <FullScreenFormLayout
      ref={layoutRef}
      offsetDisabled={false}
      onScroll={onScroll}
      contentStyles={styles.container}
      actions={action}
      actionsStyles={actionsStyles}
      content={
        <>
          {enableBackground ? (
            <AppImage
              style={styles.bgImage}
              width={windowWidth}
              height={700}
              resizeMode="stretch"
              source={images.BackgroundRepayScreen}
            />
          ) : null}
          <ScrollView
            keyboardShouldPersistTaps="handled"
            testID="scroll-container"
            showsVerticalScrollIndicator={false}
            style={{ overflow: 'visible' }}
            refreshControl={<AppRefreshControl onRefresh={handleOnRefreshing} refreshing={refreshing} />}>
            {children}
          </ScrollView>
        </>
      }
    />
  );
};

const styles = StyleSheet.create({
  bgImage: { position: 'absolute', top: 0, left: 0, right: 0, bottom: 0 },
  textInput: { fontSize: 24, paddingTop: 12, paddingBottom: 12 },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginTop: 20,
  },
  inputText: {},
  wrapperInput: { marginTop: 24 },
  coverBackgroundImg: { width: 375, height: 220, position: 'absolute', top: 0, left: 0, right: 0 },
  container: Platform.select({
    web: { height: windowHeight, paddingHorizontal: 0 },
    default: { flex: 1, overflow: 'visible', paddingHorizontal: 0 },
  }),
  button: Platform.select({
    web: { position: 'absolute', left: 16, right: 16, bottom: 16 + getBottomSafe() },
    default: {},
  }),
  closeButton: {
    zIndex: 0,
  },
  inputContainer: {
    overflow: 'hidden',
    backgroundColor: AppColors.white,
    borderTopLeftRadius: 20,
    borderTopRightRadius: 20,
  },
});

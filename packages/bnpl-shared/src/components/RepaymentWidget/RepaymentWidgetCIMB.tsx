import { useUserBalanceController } from 'bnpl-shared/src/utils/useUserBalanceController';
import { useCIMBStatementController } from 'bnpl-shared/src/utils/statement_controller/useCIMBStatementController';
import { UIRules, useStatementRuleCheck } from 'bnpl-shared/src/hooks/useStatementRuleCheck';
import { useAppSelector } from 'bnpl-shared/src/redux/store';
import { RELOADABLE_COMPONENTS } from 'bnpl-shared/src/redux/reloadableComponentsReducer';
import React, { FC, useEffect } from 'react';
import { ResourceState, StatementSummaryType, CIMBStatementConditions } from 'bnpl-shared/src/types';
import { View } from 'react-native';
import { Skeleton } from 'bnpl-shared/src/shared/animated-wrappers/Skeleton';
import { Fade } from 'bnpl-shared/src/shared/animated-wrappers';
import RepaymentWidgetLayout, { THEME_CONFIG } from 'bnpl-shared/src/components/RepaymentWidget/RepaymentWidgetLayout';
import { formatCurrency } from 'bnpl-shared/src/shared/utils/formatCurrency';
import { repayWidgetStyles } from './index';
import { PartnerCode } from 'bnpl-shared/src/constants';

type Props = {
  isPreviewMode: boolean;
  onAction?: (actionName: string) => void;
};

export const RepaymentWidgetCIMB: FC<Props> = ({ isPreviewMode, onAction }) => {
  const { balanceSummary } = useUserBalanceController();
  const { stmConditions, userStatement, fetchUserStatement, resourceState } = useCIMBStatementController();
  const { checkUIRule } = useStatementRuleCheck(PartnerCode.CIMB);
  const reloadIndicator: number | undefined = useAppSelector(
    state => state.reloadableComponents[RELOADABLE_COMPONENTS.REPAYMENT_WIDGET],
  );

  useEffect(() => {
    (async () => {
      if (!isPreviewMode) {
        fetchUserStatement();
      }
    })();
  }, []);

  useEffect(() => {
    (async () => {
      if (reloadIndicator !== undefined && !isPreviewMode) {
        fetchUserStatement();
      }
    })();
  }, [reloadIndicator]);

  if (isPreviewMode) {
    return null;
  }
  if (
    resourceState === ResourceState.INIT ||
    resourceState === ResourceState.LOADING ||
    balanceSummary.state === ResourceState.LOADING ||
    balanceSummary.state === ResourceState.INIT
  ) {
    return (
      <View testID="repayment-widget-skeleton" style={repayWidgetStyles.skeletonRoot}>
        <View style={repayWidgetStyles.info}>
          <Skeleton width={90} height={18} style={repayWidgetStyles.skeletonTextSmall} />
          <Skeleton width={95} height={36} style={repayWidgetStyles.skeletonTextLarge} />
        </View>
        <Skeleton width={67} height={70} style={[repayWidgetStyles.dueDay]} />
        <Skeleton width={120} height={30} style={repayWidgetStyles.paymentBtn} />
      </View>
    );
  } else {
    if (checkUIRule(UIRules.ShowHomeRepayWidget)) {
      return <Fade>{prepareWidgetLayout(stmConditions, userStatement, isPreviewMode, onAction)}</Fade>;
    } else {
      return null;
    }
  }
};

const prepareWidgetLayout = (
  conditions: CIMBStatementConditions,
  statement: StatementSummaryType | null | undefined,
  isPreviewMode: boolean,
  onAction?: (actionName: string) => void,
): React.ReactElement | null => {
  const minPayAmount = statement
    ? isNaN(statement.suggested_min_amount)
      ? 0
      : Math.abs(statement.suggested_min_amount)
    : 0;

  if (conditions.isBeforeDue) {
    const beforeDueCountDay = statement
      ? isNaN(statement.days_to_maturity)
        ? 0
        : Math.abs(statement.days_to_maturity)
      : 0;
    return (
      <RepaymentWidgetLayout
        theme={THEME_CONFIG.nomal}
        title={'Sắp đến hạn'}
        description={`Thanh toán tối thiểu ${formatCurrency(minPayAmount)}`}
        dayTitle="Còn"
        day={beforeDueCountDay}
        onAction={() => onAction?.('open-repayment')}
      />
    );
  }
  if (conditions.isOverDue) {
    const countDay = statement ? (isNaN(statement.days_to_maturity) ? 0 : Math.abs(statement.days_to_maturity)) : 0;
    return (
      <RepaymentWidgetLayout
        theme={THEME_CONFIG.danger}
        title={'Trễ hạn'}
        description={`Thanh toán tối thiểu ${formatCurrency(minPayAmount)}`}
        dayTitle="Trễ hạn"
        day={countDay}
        onAction={() => onAction?.('open-repayment')}
      />
    );
  }
  if (conditions.isOnDue) {
    return (
      <RepaymentWidgetLayout
        theme={THEME_CONFIG.warning}
        title={'Hôm nay là ngày đến hạn'}
        description={`Thanh toán tối thiểu ${formatCurrency(minPayAmount)}`}
        dayTitle="Còn"
        day={1}
        onAction={() => onAction?.('open-repayment')}
      />
    );
  }

  return <></>;
};

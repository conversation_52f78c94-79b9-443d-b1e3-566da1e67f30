import React from 'react';
import { View, TouchableOpacity } from 'react-native';
import LinearGradient from 'react-native-linear-gradient';
import { Spacer } from 'bnpl-shared/src/components/Spacer';
import { AppColors } from 'bnpl-shared/src/constants';
import { images } from 'bnpl-shared/src/res';
import { AppImage, AppText } from 'bnpl-shared/src/shared/react-native-customized';
import { StyleSheet } from 'bnpl-shared/src/shared/styles/StyleSheet';
import { StyleUtils } from 'bnpl-shared/src/shared/styles/StyleUtils';
import RepaymentWidgetBackground from './RepaymentWidgetBackground';
import { styleUtils } from 'bnpl-shared/src/shared/styleUtils';

export const THEME_CONFIG = {
  nomal: {
    colors: ['#E8FCF3', '#F4F6E8', '#FAF2DD'],
    textColor: '#000000',
  },
  warning: {
    colors: ['#F49729', '#F49729', '#FAFF00'],
    textColor: '#FFFFFF',
  },
  danger: {
    colors: ['#EE1D4F', '#EE1D4F', '#F0F29A'],
    textColor: '#FFFFFF',
  },
};

const RepaymentWidgetLayout = ({
  theme = THEME_CONFIG.nomal,
  title,
  description,
  dayTitle = 'Còn',
  day,
  count,
  countUnit = 'ngày',
  onAction,
}: {
  theme?: any;
  title?: string;
  description?: string | React.ReactNode;
  dayTitle?: string;
  day?: number;
  count?: number;
  countUnit?: string;
  onAction?: () => void;
}) => {
  return (
    <TouchableOpacity onPress={onAction} testID="button-repayment">
      <View style={styles.root}>
        <LinearGradient style={styles.container} colors={theme.colors}>
          <>
            <RepaymentWidgetBackground />
            <View style={styles.content}>
              <View style={styles.description}>
                <View style={[StyleUtils.flexRow]}>
                  <AppImage tintColor={theme.textColor} source={images.IconNotification} width={24} height={24} />
                  <Spacer width={8} />
                  <AppText bold color={theme.textColor}>
                    {title}
                  </AppText>
                </View>
                <Spacer height={6} />
                {description ? <AppText color={theme.textColor}>{description}</AppText> : null}
              </View>
              {day ? (
                <View style={[styles.remaining, styleUtils.shadow]}>
                  <AppText size={12} color={AppColors.dark300}>
                    {dayTitle}
                  </AppText>
                  <AppText size={16} bold>
                    {day}
                  </AppText>
                  <AppText size={12}>ngày</AppText>
                </View>
              ) : count ? (
                <View style={[styles.remaining, styleUtils.shadow]}>
                  <AppText size={12} color={AppColors.dark300}>
                    {dayTitle}
                  </AppText>
                  <AppText size={16} bold>
                    {count}
                  </AppText>
                  <AppText size={12}>{countUnit}</AppText>
                </View>
              ) : null}
            </View>
            {onAction ? (
              <View style={styles.footer}>
                <View style={styles.action}>
                  <AppText size={12} height={16} color={AppColors.primary}>
                    Thanh toán ngay
                  </AppText>
                  <Spacer width={8} />
                  <AppImage source={images.IconArrowNext} width={16} height={16} />
                </View>
              </View>
            ) : null}
          </>
        </LinearGradient>
      </View>
    </TouchableOpacity>
  );
};

const styles = StyleSheet.create({
  root: {
    position: 'relative',
    justifyContent: 'space-around',
    width: 343,
    height: 114,
    borderRadius: 12,
    borderColor: AppColors.primary2,
    marginBottom: 16,
  },
  container: { borderRadius: 12, width: '100%', height: '100%' },
  content: {
    position: 'absolute',
    top: 0,
    left: 0,
    width: '100%',
    flexDirection: 'row',
    justifyContent: 'space-between',
    paddingHorizontal: 15,
    paddingTop: 11,
  },
  remaining: {
    backgroundColor: AppColors.background,
    borderRadius: 8,
    padding: 4,
    width: 67,
    height: 60,
    justifyContent: 'center',
    alignItems: 'center',
  },
  footer: {
    position: 'absolute',
    bottom: 1,
    left: 1,
    width: 341,
    height: 32,
    paddingHorizontal: 16,
    paddingVertical: 8,
    backgroundColor: AppColors.background,
    borderBottomEndRadius: 12,
    borderBottomStartRadius: 12,
  },
  action: {
    flexDirection: 'row',
    justifyContent: 'flex-start',
    alignItems: 'center',
  },
  description: {
    flexShrink: 1,
  },
});

export default RepaymentWidgetLayout;

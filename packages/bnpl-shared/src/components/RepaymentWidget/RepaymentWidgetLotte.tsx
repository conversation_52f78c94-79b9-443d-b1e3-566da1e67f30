import React, { FC, useEffect } from 'react';
import { View } from 'react-native';
import { useUserBalanceController } from 'bnpl-shared/src/utils/useUserBalanceController';
import { UIRules, useStatementRuleCheck } from 'bnpl-shared/src/hooks/useStatementRuleCheck';
import { PartnerCode } from 'bnpl-shared/src/constants';
import { useAppSelector } from 'bnpl-shared/src/redux/store';
import { RELOADABLE_COMPONENTS } from 'bnpl-shared/src/redux/reloadableComponentsReducer';
import { LotteStatementConditions, AccountStatementType, ResourceState } from 'bnpl-shared/src/types';
import { repayWidgetStyles } from 'bnpl-shared/src/components';
import { Skeleton } from 'bnpl-shared/src/shared/animated-wrappers/Skeleton';
import { Fade } from 'bnpl-shared/src/shared/animated-wrappers';
import RepaymentWidgetLayout, { THEME_CONFIG } from 'bnpl-shared/src/components/RepaymentWidget/RepaymentWidgetLayout';
import { formatCurrency } from 'bnpl-shared/src/shared/utils/formatCurrency';
import { useLotteStatementController } from 'bnpl-shared/src/utils/statement_controller/useLotteStatementController';
import { AppText } from 'bnpl-shared/src/shared/react-native-customized';
import { trackingRepayWidgetClick, trackingRepayWidgetShow } from 'bnpl-shared/src/screens/HomeScreen/tracking';

type Props = {
  isPreviewMode: boolean;
  onAction?: (actionName: string) => void;
};

export const RepaymentWidgetLotte: FC<Props> = ({ isPreviewMode, onAction }) => {
  const { balanceSummary } = useUserBalanceController();
  const { stmConditions, userStatement, fetchUserStatement, resourceState } = useLotteStatementController();
  const { checkUIRule } = useStatementRuleCheck(PartnerCode.LOTTE);
  const reloadIndicator: number | undefined = useAppSelector(
    state => state.reloadableComponents[RELOADABLE_COMPONENTS.REPAYMENT_WIDGET],
  );
  const handleAction = (actionName: string) => {
    if (userStatement) {
      trackingRepayWidgetClick({ status: userStatement.repayment_status });
    }
    onAction?.(actionName);
  };

  useEffect(() => {
    (async () => {
      if (!isPreviewMode) {
        fetchUserStatement();
      }
    })();
  }, []);

  useEffect(() => {
    (async () => {
      if (reloadIndicator !== undefined && !isPreviewMode) {
        fetchUserStatement();
      }
    })();
  }, [reloadIndicator]);

  if (isPreviewMode) {
    return null;
  }
  if (
    resourceState === ResourceState.INIT ||
    resourceState === ResourceState.LOADING ||
    balanceSummary.state === ResourceState.LOADING ||
    balanceSummary.state === ResourceState.INIT
  ) {
    return (
      <View testID="repayment-widget-skeleton" style={repayWidgetStyles.skeletonRoot}>
        <View style={repayWidgetStyles.info}>
          <Skeleton width={90} height={18} style={repayWidgetStyles.skeletonTextSmall} />
          <Skeleton width={95} height={36} style={repayWidgetStyles.skeletonTextLarge} />
        </View>
        <Skeleton width={67} height={70} style={[repayWidgetStyles.dueDay]} />
        <Skeleton width={120} height={30} style={repayWidgetStyles.paymentBtn} />
      </View>
    );
  }
  if (checkUIRule(UIRules.ShowHomeRepayWidget)) {
    return <Fade>{prepareWidgetLayout(stmConditions, userStatement, isPreviewMode, handleAction)}</Fade>;
  }
  return null;
};

const prepareWidgetLayout = (
  conditions: LotteStatementConditions,
  statement: AccountStatementType | null | undefined,
  isPreviewMode: boolean,
  onAction?: (actionName: string) => void,
): React.ReactElement | null => {
  if (statement) {
    trackingRepayWidgetShow({ status: statement.repayment_status });
  }

  let payAmount = 0;
  if (statement && !isNaN(Number(statement?.statement_remaining_total_outstanding_balance))) {
    payAmount = Math.abs(Number(statement.statement_remaining_total_outstanding_balance));
  }

  const renderDesc = (textColor: string) => {
    return (
      <AppText color={textColor}>
        Thanh toán dư nợ đến hạn{' '}
        <AppText color={textColor} bold>
          {formatCurrency(payAmount)}
        </AppText>
      </AppText>
    );
  };

  const renderDescOverDue = (textColor: string) => {
    return (
      <AppText color={textColor}>
        Thanh toán dư nợ đến hạn{' '}
        <AppText color={textColor} bold>
          {formatCurrency(payAmount)}
        </AppText>
      </AppText>
    );
  };

  if (conditions.isBeforeDue) {
    let beforeDueCountDay = 0;
    if (statement && !isNaN(statement.days_to_maturity)) {
      beforeDueCountDay = Math.abs(statement.days_to_maturity);
    }

    return (
      <RepaymentWidgetLayout
        theme={THEME_CONFIG.nomal}
        title={'Sắp đến hạn'}
        description={renderDesc(THEME_CONFIG.nomal.textColor)}
        dayTitle="Còn"
        day={beforeDueCountDay}
        onAction={() => onAction?.('open-repayment')}
      />
    );
  }
  if (conditions.isOverDue) {
    let countDay = 0;
    if (statement && !isNaN(statement.days_to_maturity)) {
      countDay = Math.abs(statement.days_to_maturity);
    }

    return (
      <RepaymentWidgetLayout
        theme={THEME_CONFIG.danger}
        title={'Trễ hạn'}
        description={renderDescOverDue(THEME_CONFIG.danger.textColor)}
        dayTitle="Trễ hạn"
        day={countDay}
        onAction={() => onAction?.('open-repayment')}
      />
    );
  }
  if (conditions.isOnDue) {
    return (
      <RepaymentWidgetLayout
        theme={THEME_CONFIG.warning}
        title={'Hôm nay là ngày đến hạn'}
        description={renderDesc(THEME_CONFIG.warning.textColor)}
        dayTitle="Còn"
        day={1}
        onAction={() => onAction?.('open-repayment')}
      />
    );
  }

  return <></>;
};

import React, { FC, useEffect } from 'react';
import { View } from 'react-native';
import { useUserBalanceController } from 'bnpl-shared/src/utils/useUserBalanceController';
import { UIRules, useStatementRuleCheck } from 'bnpl-shared/src/hooks/useStatementRuleCheck';
import { PartnerCode } from 'bnpl-shared/src/constants';
import { useAppSelector } from 'bnpl-shared/src/redux/store';
import { RELOADABLE_COMPONENTS } from 'bnpl-shared/src/redux/reloadableComponentsReducer';
import { ResourceState, MiniBnplStatementConditions, MiniBnplTotalOutstandingState } from 'bnpl-shared/src/types';
import { repayWidgetStyles } from 'bnpl-shared/src/components';
import { Skeleton } from 'bnpl-shared/src/shared/animated-wrappers/Skeleton';
import { Fade } from 'bnpl-shared/src/shared/animated-wrappers';
import RepaymentWidgetLayout, { THEME_CONFIG } from 'bnpl-shared/src/components/RepaymentWidget/RepaymentWidgetLayout';
import { formatCurrency } from 'bnpl-shared/src/shared/utils/formatCurrency';
import { useMiniBnplStatementController } from 'bnpl-shared/src/utils/statement_controller/useMiniBnplStatementController';
import { AppImage, AppText } from 'bnpl-shared/src/shared/react-native-customized';
import { StyleSheet } from 'bnpl-shared/src/shared/styles/StyleSheet';
import { images } from "bnpl-shared/src/res";
import LinearGradient from "react-native-linear-gradient";

type Props = {
  isPreviewMode: boolean;
  onAction?: (actionName: string) => void;
};

export const RepaymentWidgetMiniBnpl: FC<Props> = ({ isPreviewMode, onAction }) => {
  const { balanceSummary } = useUserBalanceController();
  const { stmConditions, fetchUserStatement, resourceState, totalOutstandingData } = useMiniBnplStatementController();
  const { checkUIRule } = useStatementRuleCheck(PartnerCode.MINI_BNPL);
  const reloadIndicator: number | undefined = useAppSelector(
    state => state.reloadableComponents[RELOADABLE_COMPONENTS.REPAYMENT_WIDGET],
  );
  const handleAction = (actionName: string) => {
    onAction?.(actionName);
  };

  useEffect(() => {
    (async () => {
      if (!isPreviewMode) {
        fetchUserStatement();
      }
    })();
  }, []);

  useEffect(() => {
    (async () => {
      if (reloadIndicator !== undefined && !isPreviewMode) {
        fetchUserStatement();
      }
    })();
  }, [reloadIndicator]);

  if (isPreviewMode) {
    return null;
  }
  if (
    resourceState === ResourceState.INIT ||
    resourceState === ResourceState.LOADING ||
    balanceSummary.state === ResourceState.LOADING ||
    balanceSummary.state === ResourceState.INIT
  ) {
    return (
      <View testID="repayment-widget-skeleton" style={repayWidgetStyles.skeletonRoot}>
        <View style={repayWidgetStyles.info}>
          <Skeleton width={90} height={18} style={repayWidgetStyles.skeletonTextSmall} />
          <Skeleton width={95} height={36} style={repayWidgetStyles.skeletonTextLarge} />
        </View>
        <Skeleton width={67} height={70} style={[repayWidgetStyles.dueDay]} />
        <Skeleton width={120} height={30} style={repayWidgetStyles.paymentBtn} />
      </View>
    );
  }
  if (checkUIRule(UIRules.ShowHomeRepayWidget)) {
    return <Fade>{prepareWidgetLayout(stmConditions, totalOutstandingData, isPreviewMode, handleAction)}</Fade>;
  }
  return null;
};

const prepareWidgetLayout = (
  conditions: MiniBnplStatementConditions,
  statement: MiniBnplTotalOutstandingState | null | undefined,
  isPreviewMode: boolean,
  onAction?: (actionName: string) => void,
): React.ReactElement | null => {
  let payAmount = 0;
  if (statement && !isNaN(statement?.totalDueAmount)) {
    payAmount = Math.abs(Number(statement.totalDueAmount));
  }

  const renderDesc = (textColor: string) => {
    return (
      <AppText color={textColor}>
        {"Số tiền phải trả: "}
        <AppText color={textColor} bold>
          {formatCurrency(payAmount)}
        </AppText>
      </AppText>
    );
  };
  let countTime = 0;
  let countUnit = 'ngày';
  if (statement && !isNaN(statement.dueDistance)) {
    countTime = Math.floor(Math.abs(statement.dueDistance / 3600 / 24));
    if (countTime === 0) {
      countTime = Math.floor(Math.abs(statement.dueDistance / 3600)) || 1;
      countUnit = 'giờ';
    }
  }
  if (conditions.isExistOverDue) {
    return (
      <RepaymentWidgetLayout
        theme={THEME_CONFIG.danger}
        title="Quá hạn thanh toán"
        description={renderDesc(THEME_CONFIG.danger.textColor)}
        dayTitle="Quá hạn"
        count={countTime}
        countUnit={countUnit}
        onAction={() => onAction?.('open-repayment')}
      />
    );
  }
  if (conditions.isExistOnDue) {
    return (
      <RepaymentWidgetLayout
        theme={THEME_CONFIG.warning}
        title="Đến hạn thanh toán"
        description={renderDesc(THEME_CONFIG.warning.textColor)}
        dayTitle="Còn"
        count={countTime}
        countUnit={countUnit}
        onAction={() => onAction?.('open-repayment')}
      />
    );
  }

  return (
    <View style={styles.emptyRepaymentWrapper}>
      <LinearGradient
        style={styles.gradient}
        colors={['rgba(255, 255, 255, 0)', 'rgba(196, 247, 221, 0.2)']}
      />
      <AppText bold color="#001F3E" size={14} height={18}>
        Không có khoản nào{`\n`}đến hạn thanh toán
      </AppText>
      <AppImage style={styles.image} source={images.ImageMiniEmptyReminder} width={114} height={114} />
    </View>
  );
};

const styles = StyleSheet.create({
  emptyRepaymentWrapper: {
    width: 343,
    height: 114,
    borderRadius: 12,
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingTop: 12,
    paddingBottom: 12,
    paddingLeft: 16,
    paddingRight: 0,
    position: 'relative',
  },
  image: {
    marginLeft: 16,
  },
  gradient: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
  },
});


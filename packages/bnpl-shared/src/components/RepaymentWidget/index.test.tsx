import React from 'react';
import { renderWithRedux } from 'bnpl-shared/src/jest/renderWithRedux';
import { RepaymentWidget } from './index';
import { fireEvent, waitFor } from '@testing-library/react-native';
import { ResourceState } from 'bnpl-shared/src/types';
import { dummyStm, dummyCIMBStmCondition } from 'bnpl-shared/src/jest/fakeStatementData';
import { PartnerCode } from 'bnpl-shared/src/constants';
import { store } from 'bnpl-shared/src/redux/store';
import { setSummaryUserBalance } from 'bnpl-shared/src/redux/userBalanceReducer';

let mockStm = dummyStm;
let mockStmCondition = dummyCIMBStmCondition;

jest.mock('bnpl-shared/src/utils/statement_controller/useCIMBStatementController', () => ({
  useCIMBStatementController: () => ({
    stmConditions: mockStmCondition,
    userStatement: mockStm,
  }),
}));

describe(RepaymentWidget.name, () => {
  beforeAll(() => {
    store.dispatch(
      setSummaryUserBalance({
        state: ResourceState.READY,
        data: {
          total_limit: 5000000,
          available_balance: 4900000,
          over_repayment_balance: 0,
          outstanding_balance: 100000,
        },
      }),
    );
  });

  it('render in preview mode', () => {
    const { queryByText } = renderWithRedux(<RepaymentWidget partnerCode={PartnerCode.CIMB} isPreviewMode={true} />);
    expect(queryByText('Hạn thanh toán')).toBeFalsy();
  });

  it('render widget when statement is on due date', async () => {
    mockStmCondition = {
      ...dummyCIMBStmCondition,
      isOnDue: true,
    };
    const onActionHandle = jest.fn();
    const { queryByText, getByTestId } = renderWithRedux(
      <RepaymentWidget partnerCode={PartnerCode.CIMB} isPreviewMode={false} onAction={onActionHandle} />,
    );
    await waitFor(() => expect(queryByText('Hôm nay là ngày đến hạn')).toBeTruthy());
    expect(queryByText('Thanh toán ngay')).toBeTruthy();
    fireEvent.press(getByTestId('button-repayment'));
    expect(onActionHandle).toHaveBeenCalledWith('open-repayment');
  });

  it('render widget when statement is over due date', async () => {
    mockStm = {
      ...dummyStm,
      days_to_maturity: -2,
    };
    mockStmCondition = {
      ...dummyCIMBStmCondition,
      isOverDue: true,
    };
    const onActionHandle = jest.fn();
    const { queryByText, getByTestId, queryAllByText } = renderWithRedux(
      <RepaymentWidget partnerCode={PartnerCode.CIMB} isPreviewMode={false} onAction={onActionHandle} />,
    );
    await waitFor(() => expect(queryAllByText('Trễ hạn').length).toEqual(2));
    expect(queryByText('2')).toBeTruthy();
    expect(queryByText('ngày')).toBeTruthy();
    expect(queryByText('Thanh toán ngay')).toBeTruthy();
    fireEvent.press(getByTestId('button-repayment'));
    expect(onActionHandle).toHaveBeenCalledWith('open-repayment');
  });

  it('render widget when statement is available', async () => {
    mockStm = {
      ...dummyStm,
      days_to_maturity: 15,
    };
    mockStmCondition = {
      ...dummyCIMBStmCondition,
      isBeforeDue: true,
    };
    const onActionHandle = jest.fn();
    const { queryByText, getByTestId } = renderWithRedux(
      <RepaymentWidget partnerCode={PartnerCode.CIMB} isPreviewMode={false} onAction={onActionHandle} />,
    );
    await waitFor(() => expect(queryByText('Còn')).toBeTruthy());
    expect(queryByText('15')).toBeTruthy();
    expect(queryByText('ngày')).toBeTruthy();
    expect(queryByText('Thanh toán ngay')).toBeTruthy();
    fireEvent.press(getByTestId('button-repayment'));
    expect(onActionHandle).toHaveBeenCalledWith('open-repayment');
  });
});

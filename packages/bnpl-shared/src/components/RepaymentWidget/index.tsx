import React, { FC } from 'react';
import { AppColors, PartnerCode } from 'bnpl-shared/src/constants';
import { StyleSheet } from 'bnpl-shared/src/shared/styles/StyleSheet';
import { RepaymentWidgetCIMB } from 'bnpl-shared/src/components/RepaymentWidget/RepaymentWidgetCIMB';
import { RepaymentWidgetLotte } from 'bnpl-shared/src/components/RepaymentWidget/RepaymentWidgetLotte';
import { RepaymentWidgetMiniBnpl } from "./RepaymentWidgetMiniBnpl";

type Props = {
  isPreviewMode: boolean;
  onAction?: (actionName: string) => void;
  partnerCode: PartnerCode;
};

export const RepaymentWidget: FC<Props> = ({ isPreviewMode, onAction, partnerCode }) => {
  switch (partnerCode) {
    case PartnerCode.CIMB:
      return <RepaymentWidgetCIMB isPreviewMode={isPreviewMode} onAction={onAction} />;
    case PartnerCode.LOTTE:
      return <RepaymentWidgetLotte isPreviewMode={isPreviewMode} onAction={onAction} />;
    case PartnerCode.MINI_BNPL:
      return <RepaymentWidgetMiniBnpl isPreviewMode={isPreviewMode} onAction={onAction} />;
    default:
      return null;
  }
};

export const repayWidgetStyles = StyleSheet.create({
  skeletonRoot: {
    position: 'relative',
    width: 343,
    height: 114,
    borderRadius: 12,
    backgroundColor: '#fff',
    padding: 12,
    marginBottom: 16,
  },
  skeletonTextSmall: {
    width: 90,
    height: 20,
    borderRadius: 4,
    marginBottom: 6,
  },
  skeletonTextLarge: {
    width: 148,
    height: 16,
    borderRadius: 4,
  },
  dueDay: {
    position: 'absolute',
    right: 12,
    top: 16,
    borderRadius: 8,
  },
  root: {
    width: 165,
    height: 165,
    aspectRatio: 1,
    borderRadius: 22,
    backgroundColor: '#fff',
    padding: 16,
  },
  statusImage: {
    width: 165,
    height: 165,
  },
  info: {
    flex: 1,
  },
  expiredDate: {
    marginTop: 8,
  },
  paymentBtn: {
    alignSelf: 'flex-start',
    flexDirection: 'row',
    alignItems: 'center',
    paddingVertical: 6,
    paddingHorizontal: 6,
    borderRadius: 100,
    height: 16,
  },
  paymentText: {
    marginRight: 6,
    color: '#1C86B4',
    fontSize: 13,
    lineHeight: 18,
  },
  paymentTextOverdue: {
    marginRight: 6,
    color: AppColors.textPaymentExpired,
    fontSize: 13,
    lineHeight: 18,
  },
  paymentTextDisable: {
    marginRight: 6,
    color: AppColors.disabled,
    fontSize: 13,
    lineHeight: 18,
  },
});

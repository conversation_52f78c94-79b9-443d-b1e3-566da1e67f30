import { StyleProp, View, ViewStyle } from 'react-native';
import { AppText } from 'bnpl-shared/src/shared/react-native-customized';
import { AppColors } from 'bnpl-shared/src/constants';
import React, { FC } from 'react';
import { StyleSheet } from 'bnpl-shared/src/shared/styles/StyleSheet';

const Ribbon: FC<{ style?: StyleProp<ViewStyle>; text: string; testID?: string }> = ({ style, text, testID }) => {
  return (
    <View testID={testID} style={[styles.ribbon, style]}>
      <AppText bold color={AppColors.white}>
        {text}
      </AppText>
    </View>
  );
};

export default Ribbon;

const styles = StyleSheet.create({
  ribbon: {
    paddingVertical: 4,
    paddingHorizontal: 8,
    borderTopStartRadius: 4,
    borderTopEndRadius: 4,
    borderBottomStartRadius: 4,
    backgroundColor: AppColors.green[4],
    justifyContent: 'center',
    alignItems: 'center',
  },
});

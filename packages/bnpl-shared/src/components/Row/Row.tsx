import React, { FC, Children, PropsWithChildren } from 'react';
import { View } from 'react-native';
import { StyleSheet } from 'bnpl-shared/src/shared/styles/StyleSheet';

export const Row: FC<PropsWithChildren<{ cols: number }>> = ({ cols, children }) => {
  return (
    <View style={styles.root}>
      {Children.map(children, child => (child ? <View style={{ width: `${100 / cols}%` }}>{child}</View> : null))}
    </View>
  );
};

const styles = StyleSheet.create({
  root: {
    flexDirection: 'row',
    flexWrap: 'wrap',
  },
});

import React, { SVGProps } from "react";

export default function GeneralBackIc24(props: SVGProps<SVGSVGElement>) {
  const { color = "#99A5B2" } = props; // #99A5B2 is Dark200 in color component
  return (
    <svg
      width={24}
      height={24}
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
      {...props}
    >
      <path
        fillRule="evenodd"
        clipRule="evenodd"
        d="M15.997 4.289c.495.419.54 1.141.1 1.613L10.404 12l5.691 6.098c.44.472.396 1.194-.1 1.613a1.242 1.242 0 0 1-1.693-.095l-6.4-6.857a1.103 1.103 0 0 1 0-1.518l6.4-6.857a1.242 1.242 0 0 1 1.694-.095Z"
        fill={color}
      />
    </svg>
  );
};

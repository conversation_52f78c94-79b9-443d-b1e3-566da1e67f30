import React from 'react';

export default function LogoZ({ width = 118, height = 139, opacity = 1, color = '#00CF6A' }: LogoZProps) {
  return (
    <svg width={width} height={height} viewBox="0 0 118 139" fill="none" xmlns="http://www.w3.org/2000/svg">
      <g style={{ mixBlendMode: 'multiply' }} opacity={opacity}>
        <path d="M106.555 139C77.6141 125.302 44.4175 125.302 15.4766 139L0 118.352L69.1286 36.001C65.086 36.3755 61.0323 36.5664 56.9824 36.5664C37.4632 36.5664 17.8191 32.1535 0.176245 23.8012L11.4413 0C40.3822 13.6977 73.5788 13.6977 102.52 0L118 20.6475L48.8714 102.999C52.914 102.625 56.9677 102.434 61.0176 102.434C80.5368 102.434 100.181 106.847 117.824 115.199L106.555 139Z" fill={color} />
      </g>
    </svg>
  )
}

interface LogoZProps {
  width?: number,
  height?: number,
  opacity?: number,
  color?: string,
}

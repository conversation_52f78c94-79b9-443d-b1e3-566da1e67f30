import { AppText } from 'bnpl-shared/src/shared/react-native-customized';
import React, { FC } from 'react';
import { Spacer } from 'bnpl-shared/src/components/Spacer';
import { AppColors } from 'bnpl-shared/src/constants';
import { StyleSheet } from 'bnpl-shared/src/shared/styles/StyleSheet';
import { StyleProp, View, ViewStyle } from 'react-native';

const AccountScreenSection: FC<{ title: string; style?: StyleProp<ViewStyle> }> = ({ title, style, children }) => {
  return (
    <>
      <AppText bold size={16}>
        {title}
      </AppText>
      <Spacer height={16} />
      <View style={[styles.root, style]}>{children}</View>
    </>
  );
};

export default AccountScreenSection;

const styles = StyleSheet.create({
  root: {
    backgroundColor: AppColors.background,
    borderRadius: 8,
  },
});

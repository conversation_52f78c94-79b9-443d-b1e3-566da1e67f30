//#region Section
import React, { FC } from 'react';
import { StyleProp, View, ViewStyle } from 'react-native';
import { Colors, StyleUtils } from 'bnpl-shared/src/shared/styles/StyleUtils';
import { AppText } from 'bnpl-shared/src/shared/react-native-customized';
import { StyleSheet } from 'bnpl-shared/src/shared/styles/StyleSheet';

const SectionDefault: FC<{ title: string; style?: StyleProp<ViewStyle> }> = ({ title, style, children }) => {
  const styles = sectionStyles;
  return (
    <View style={[styles.root, StyleUtils.shadow, style]}>
      {title && (
        <AppText bold size={16} height={20} style={styles.title}>
          {title}
        </AppText>
      )}
      {children}
    </View>
  );
};

export default SectionDefault;

const sectionStyles = StyleSheet.create({
  root: {
    backgroundColor: Colors.background,
    borderRadius: 8,
    paddingHorizontal: 16,
    paddingVertical: 12,
    marginBottom: 12,
  },
  title: {
    marginBottom: 12,
  },
});
//#endregion

//#region SectionListItem
import React, { FC, isValidElement, ReactElement } from 'react';
import { View } from 'react-native';
import { Colors, StyleUtils } from 'bnpl-shared/src/shared/styles/StyleUtils';
import { AppText } from 'bnpl-shared/src/shared/react-native-customized';
import { StyleSheet } from 'bnpl-shared/src/shared/styles/StyleSheet';

const SectionListItem: FC<{ left: string; right: string | ReactElement }> = ({ left, right }) => {
  const styles = sectionListItemStyles;
  return (
    <View style={[StyleUtils.rowStretchBetween, styles.root]}>
      <AppText color={Colors.text2}>{left}</AppText>
      {isValidElement(right) ? right : <AppText style={styles.rightText}>{right}</AppText>}
    </View>
  );
};

export default SectionListItem;

const sectionListItemStyles = StyleSheet.create({
  root: {
    paddingVertical: 12,
  },
  rightText: {
    textAlign: 'right',
    flex: 1,
    paddingLeft: 24,
  },
});
//#endregion

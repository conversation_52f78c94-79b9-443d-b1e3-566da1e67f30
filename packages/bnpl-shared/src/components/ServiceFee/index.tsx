import React, { FC } from 'react';
import { formatCurrency } from 'bnpl-shared/src/shared/utils/formatCurrency';
import { View } from 'react-native';
import { AppImage, AppText } from 'bnpl-shared/src/shared/react-native-customized';
import { AppColors } from 'bnpl-shared/src/constants';
import { images } from 'bnpl-shared/src/res';
import { Spacer } from 'bnpl-shared/src/components/Spacer';
import { StyleSheet } from 'bnpl-shared/src/shared/styles/StyleSheet';

export const ServiceFee: FC<{ serviceFee?: string }> = ({ serviceFee }) => {
  if (!serviceFee) {
    return null;
  }
  return (
    <View testID={'stm-balance'} style={styles.balanceRoot}>
      <AppImage tintColor={AppColors.white} width={16} height={16} source={images.IconDollarYellowCircle} />
      <Spacer width={4} />
      <AppText size={16} color={AppColors.white}>
        Phí dịch vụ:{' '}
        <AppText color={AppColors.white} size={16} bold>
          {formatCurrency(Number(serviceFee))}
        </AppText>
      </AppText>
    </View>
  );
};

const styles = StyleSheet.create({
  balanceRoot: { flexDirection: 'row', alignItems: 'center', height: 56, paddingHorizontal: 16 },
});

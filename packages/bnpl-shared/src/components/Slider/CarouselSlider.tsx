import React, { FC, useEffect, useRef, useState } from 'react';
import { AdBanner, AUTO_PLAY_DELAY } from 'bnpl-shared/src/types';
import Carousel, { Pagination } from 'react-native-snap-carousel';
import { TouchableOpacity, StyleProp, ImageStyle } from 'react-native';
import { windowWidth } from 'bnpl-shared/src/shared/styles/StyleUtils';
import { Dot } from 'bnpl-shared/src/shared/swiper/Dot';
import { Spacer } from 'bnpl-shared/src/components/Spacer';
import { AppSliderProps } from 'bnpl-shared/src/components/Slider/index';
import { AppImage } from 'bnpl-shared/src/shared/react-native-customized';
import { ImagePlayerRef } from 'bnpl-shared/src/shared/ImagePlayer';

export const CarouselSlider: FC<AppSliderProps> = ({
  resources,
  onBannerPress,
  itemSize,
  loop,
  autoplay,
  autoplayInterval = AUTO_PLAY_DELAY,
  onIndexChange,
  imageStyle,
  itemPadding = 0,
  pagination = false,
}) => {
  const [activeSlide, setActiveSlide] = useState<number>(0);
  useEffect(() => {
    if (resources && resources.length > 0) {
      onIndexChange?.(0);
    }
  }, []);

  if (!resources) {
    return null;
  }

  return (
    <>
      <Carousel
        vertical={false}
        data={resources}
        loop={loop}
        autoplay={autoplay}
        autoplayInterval={autoplayInterval}
        activeSlideOffset={0}
        inactiveSlideScale={1}
        loopClonesPerSide={5}
        shouldOptimizeUpdates={false}
        inactiveSlideOpacity={1}
        renderItem={(baseData: { index: number; dataIndex: number; item: AdBanner }, _) => {
          return (
            <CarouselBannerItem
              isActive={activeSlide === baseData.index}
              index={baseData.index}
              item={baseData.item}
              imageStyle={imageStyle}
              onBannerPress={onBannerPress}
              itemSize={itemSize}
              itemPadding={itemPadding}
            />
          );
        }}
        sliderWidth={windowWidth}
        activeSlideAlignment={'start'}
        onSnapToItem={(index: number) => {
          setActiveSlide(index);
        }}
        onScrollIndexChanged={(index: number) => {
          onIndexChange?.(index);
          setActiveSlide(index);
        }}
        itemWidth={itemSize.width + itemPadding}
      />
      {resources.length > 1 && pagination ? (
        <Pagination
          dotsLength={resources.length}
          activeDotIndex={activeSlide}
          containerStyle={{ paddingVertical: 12 }}
          dotElement={<Dot active={true} />}
          inactiveDotElement={<Dot active={false} />}
        />
      ) : (
        <Spacer height={16} />
      )}
    </>
  );
};

export const CarouselBannerItem: FC<{
  index: number;
  item: AdBanner;
  onBannerPress?: (url: string, index: number) => void;
  imageStyle?: StyleProp<ImageStyle>;
  itemPadding?: number;
  itemSize: { width: number; height: number };
  isActive?: boolean;
}> = props => {
  const imagePlayerRef = useRef<ImagePlayerRef>(null);

  useEffect(() => {
    if (props.isActive) {
      imagePlayerRef.current?.play();
    } else {
      imagePlayerRef.current?.pause();
    }
  }, [props.isActive]);

  return (
    <TouchableOpacity
      style={[
        {
          marginStart: props.itemPadding,
          borderRadius: 8,
          overflow: 'hidden',
        },
        props.imageStyle,
      ]}
      testID={`carousel-banner-${props.index}`}
      onPress={() => props.onBannerPress?.(props.item.redirect_url, props.index)}>
      <AppImage
        imagePlayerRef={imagePlayerRef}
        autoPlayAnimation={true}
        scaleToDevice={false}
        width={props.itemSize.width}
        height={props.itemSize.height}
        resizeMode={'cover'}
        key={props.item.id}
        source={{ uri: props.item.banner_url }}
      />
    </TouchableOpacity>
  );
};

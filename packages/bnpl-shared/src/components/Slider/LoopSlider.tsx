import { AppSliderP<PERSON>, CarouselBannerItem } from 'bnpl-shared/src/components/Slider/index';
import React, { FC, useEffect, useState } from 'react';
import { AppCarousel } from 'bnpl-shared/src/components';
import { windowWidth } from 'bnpl-shared/src/shared/styles/StyleUtils';
import { AUTO_PLAY_DELAY } from 'bnpl-shared/src/types';

export const LoopSlider: FC<AppSliderProps> = ({
  resources,
  onBannerPress,
  itemSize,
  loop,
  autoplay,
  autoplayInterval = AUTO_PLAY_DELAY,
  onIndexChange,
  imageStyle,
  itemPadding = 0,
}) => {
  const [activeSlide, setActiveSlide] = useState<number>(0);
  useEffect(() => {
    if (resources && resources.length > 0) {
      onIndexChange?.(0);
    }
  }, []);

  if (!resources) {
    return null;
  }

  return (
    <AppCarousel
      testID="loop-slider"
      onAnimateNextPage={(nextPage: number) => {
        onIndexChange?.(nextPage);
        setActiveSlide(nextPage);
      }}
      delay={autoplayInterval}
      autoplay={autoplay}
      isLooped={loop}
      style={{ width: windowWidth, height: itemSize.height }}>
      {resources.map((item, index) => {
        return (
          <CarouselBannerItem
            isActive={index === activeSlide}
            key={`${item.id}_${index}`}
            index={index}
            item={item}
            imageStyle={imageStyle}
            onBannerPress={onBannerPress}
            itemSize={itemSize}
            itemPadding={itemPadding}
          />
        );
      })}
    </AppCarousel>
  );
};

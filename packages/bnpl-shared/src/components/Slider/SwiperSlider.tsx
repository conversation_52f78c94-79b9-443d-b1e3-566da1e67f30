import React, { FC, useEffect, useRef } from 'react';
import { ImageStyle, StyleProp, TouchableOpacity, View } from 'react-native';
import { calculateScaleDimension, StyleSheet } from 'bnpl-shared/src/shared/styles/StyleSheet';
import { SwiperComponent } from 'bnpl-shared/src/shared/swiper';
import { AppSliderProps } from 'bnpl-shared/src/components/Slider/index';
import { AdBanner, AUTO_PLAY_DELAY } from 'bnpl-shared/src/types';
import { AppImage } from 'bnpl-shared/src/shared/react-native-customized';
import { ImagePlayerRef } from 'bnpl-shared/src/shared/ImagePlayer';

export type SwiperSliderProps = AppSliderProps & {
  slidesPerView?: number;
};

export const SwiperSlider: FC<SwiperSliderProps> = ({
  resources,
  onBannerPress,
  itemSize,
  autoplay,
  autoplayInterval = AUTO_PLAY_DELAY,
  loop,
  onIndexChange,
  imageStyle,
  pagination,
  slidesPerView = 1,
}) => {
  const [innerIndex, setInnerIndex] = React.useState(0);
  useEffect(() => {
    if (resources && resources?.length > 0) {
      onIndexChange?.(0);
    }
  }, []);

  if (!resources) {
    return null;
  }

  return (
    <View
      testID={'swiper-slider'}
      style={{ height: calculateScaleDimension(itemSize.height + (resources.length > 1 ? 26 : 16)) }}>
      <SwiperComponent
        autoplay={autoplay ? autoplayInterval : 0}
        loop={loop}
        appPaginationStyle={styles.pagingStyle}
        suppressTitleWarning
        slidesPerView={slidesPerView}
        showsPagination={pagination}
        onActiveIndexChange={index => {
          onIndexChange?.(index);
          setInnerIndex(index);
        }}
        scrollEnabled>
        {resources.map((item, index) => {
          return (
            <SwiperBanner
              isActive={innerIndex === index}
              key={item.id}
              imageStyle={imageStyle}
              item={item}
              index={index}
              itemSize={itemSize}
              onBannerPress={onBannerPress}
            />
          );
        })}
      </SwiperComponent>
    </View>
  );
};

const SwiperBanner: FC<{
  item: AdBanner;
  index: number;
  itemSize: { width: number; height: number };
  onBannerPress?: (url: string, index: number) => void;
  imageStyle?: StyleProp<ImageStyle>;
  isActive?: boolean;
}> = ({ item, index, onBannerPress, itemSize, imageStyle, isActive }) => {
  const imagePlayerRef = useRef<ImagePlayerRef>(null);

  useEffect(() => {
    if (isActive) {
      imagePlayerRef.current?.play();
    } else {
      imagePlayerRef.current?.pause();
    }
  }, [isActive]);

  return (
    <TouchableOpacity
      key={item.id}
      testID={`swiper-banner-${index}`}
      onPress={() => onBannerPress?.(item.redirect_url, index)}>
      <AppImage
        imagePlayerRef={imagePlayerRef}
        autoPlayAnimation={false}
        scaleToDevice={false}
        width={itemSize.width}
        height={itemSize.height}
        style={[styles.fullBannerImg, imageStyle]}
        resizeMode={'cover'}
        key={item.id}
        source={{ uri: item.banner_url }}
      />
    </TouchableOpacity>
  );
};

const styles = StyleSheet.create({
  fullBannerImg: {
    borderRadius: 8,
  },
  pagingStyle: { bottom: 16 },
});

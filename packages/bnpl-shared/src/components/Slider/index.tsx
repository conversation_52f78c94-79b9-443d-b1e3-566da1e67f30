import { AdBanner } from 'bnpl-shared/src/types';
import { ImageStyle, StyleProp } from 'react-native';

export type AppSliderProps = {
  resources?: AdBanner[];
  onBannerPress?: (url: string, index: number) => void;
  itemSize: { width: number; height: number };
  loop?: boolean;
  autoplay?: boolean;
  autoplayInterval?: number;
  onIndexChange?: (index: number) => void;
  itemPadding?: number;
  imageStyle?: StyleProp<ImageStyle>;
  pagination?: boolean;
};

export * from './SwiperSlider';
export * from './CarouselSlider';
export * from './LoopSlider';

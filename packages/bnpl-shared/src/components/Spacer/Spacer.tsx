import React, { FC, memo } from 'react';
import { StyleProp, View, ViewStyle } from 'react-native';
import { transformScalableFields } from '../../shared/styles/StyleSheet';

const Spacer: FC<{ width?: number | string; height?: number; style?: StyleProp<ViewStyle> }> = memo(
  ({ width, height, style }) => {
    return <View style={[transformScalableFields({ height, width }), style]} />;
  },
);

export default Spacer;

import { StatementQuickSummary } from 'bnpl-shared/src/components';
import React from 'react';
import { fireEvent } from '@testing-library/react-native';
import { renderWithRedux } from 'bnpl-shared/src/jest/renderWithRedux';
import { dummyStm, dummyCIMBStmCondition, dummyLotteStmCondition, dummyLotteStm } from '../../jest/fakeStatementData';
import { PartnerCode } from 'bnpl-shared/src/constants';

const mockStm = dummyStm;
const mockLotteStm = dummyLotteStm;

let mockStmCondition = dummyCIMBStmCondition;

let mockLotteStmCondition = dummyLotteStmCondition;

jest.mock('bnpl-shared/src/utils/statement_controller/useCIMBStatementController', () => ({
  useCIMBStatementController: () => ({
    stmConditions: mockStmCondition,
    userStatement: mockStm,
  }),
}));

jest.mock('bnpl-shared/src/utils/statement_controller/useLotteStatementController', () => ({
  useLotteStatementController: () => ({
    stmConditions: mockLotteStmCondition,
    userStatement: mockLotteStm,
  }),
}));

describe(StatementQuickSummary.name, () => {
  describe('CIMB', () => {
    it('render on due statement', () => {
      const onCTAPress = jest.fn();
      const { queryByText, getByTestId } = renderWithRedux(
        <StatementQuickSummary partnerCode={PartnerCode.CIMB} onDetailCTAPress={onCTAPress} />,
      );
      expect(queryByText('Sao kê tháng 1')).toBeTruthy();
      expect(queryByText('20:00 - 22/02/2022')).toBeTruthy();
      fireEvent.press(getByTestId('cta-detail'));
      expect(onCTAPress).toHaveBeenCalled();
    });
    it('render overdue statement', () => {
      mockStmCondition = {
        ...mockStmCondition,
        isOverDue: true,
      };
      const onCTAPress = jest.fn();
      const { queryByText, getByTestId, queryByTestId } = renderWithRedux(
        <StatementQuickSummary partnerCode={PartnerCode.CIMB} onDetailCTAPress={onCTAPress} />,
      );
      expect(queryByText('Sao kê tháng 1')).toBeTruthy();
      expect(queryByTestId('warning-banner')).toBeTruthy();
      expect(
        queryByText(
          'Bạn đã trễ hạn thanh toán và đang bị tính lãi suất 0.1% mỗi ngày. Hãy thanh toán tối thiểu để tránh nợ quá hạn.',
        ),
      ).toBeTruthy();
      fireEvent.press(getByTestId('cta-detail'));
      expect(onCTAPress).toHaveBeenCalled();
    });
  });

  describe('Lotte', () => {
    it('render statement on due as expected', () => {
      mockLotteStmCondition = {
        ...mockLotteStmCondition,
        isOnDue: true,
        noSpendInPeriod: false,
      };
      const onCTAPress = jest.fn();
      const { queryByText, queryByTestId } = renderWithRedux(
        <StatementQuickSummary partnerCode={PartnerCode.LOTTE} onDetailCTAPress={onCTAPress} />,
      );
      expect(queryByText('Sao kê 11/04/2024')).toBeTruthy();
      expect(queryByTestId('notice-banner')).toBeTruthy();
      expect(queryByTestId('cta-detail')).toBeFalsy();
      expect(queryByText('Đã thanh toán Dư nợ đến hạn?')).toBeTruthy();
      expect(
        queryByText('Dư nợ sẽ được cập nhật trong vòng 5 phút, tối đa 1 ngày làm việc sau khi thanh toán thành công.'),
      ).toBeTruthy();
    });
    it('render statement overdue as expected', () => {
      mockLotteStmCondition = {
        ...mockLotteStmCondition,
        isOnDue: false,
        isOverDue: true,
        noSpendInPeriod: false,
      };
      const onCTAPress = jest.fn();
      const { queryByText, queryByTestId } = renderWithRedux(
        <StatementQuickSummary partnerCode={PartnerCode.LOTTE} onDetailCTAPress={onCTAPress} />,
      );
      expect(queryByText('Sao kê 11/04/2024')).toBeTruthy();
      expect(queryByTestId('warning-banner')).toBeTruthy();
      expect(queryByTestId('cta-detail')).toBeFalsy();
      expect(queryByTestId('warning-text')).toBeTruthy();
    });
  });
});

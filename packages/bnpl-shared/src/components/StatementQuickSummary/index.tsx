import React, { FC } from 'react';
import { StyleUtils } from 'bnpl-shared/src/shared/styles/StyleUtils';
import { StyleSheet } from 'bnpl-shared/src/shared/styles/StyleSheet';
import { AppColors, PartnerCode } from 'bnpl-shared/src/constants';
import { View } from 'react-native';
import { AppImage, AppText } from 'bnpl-shared/src/shared/react-native-customized';
import { Spacer } from 'bnpl-shared/src/components/Spacer';
import { images } from 'bnpl-shared/src/res';
import { LinkButton } from 'bnpl-shared/src/shared/LinkButton';
import { TwoSideView } from 'bnpl-shared/src/shared/TwoSideView/TwoSideView';
import { formatDate } from 'bnpl-shared/src/shared/utils/formatDateStatement';
import { useCIMBStatementController } from 'bnpl-shared/src/utils/statement_controller/useCIMBStatementController';
import { useLotteStatementController } from 'bnpl-shared/src/utils/statement_controller/useLotteStatementController';

type Props = {
  onDetailCTAPress?: () => void;
  partnerCode: PartnerCode;
};

const CIMBStatementQuickSummary: FC<{ onDetailCTAPress?: () => void }> = ({ onDetailCTAPress }) => {
  const { stmConditions: stmConditionsCIMB, userStatement: CIMBStatement } = useCIMBStatementController();
  const period = CIMBStatement ? String(new Date(CIMBStatement.statement_date).getMonth() + 1) : '...';
  const formattedDate = CIMBStatement
    ? `20:00 - ${formatDate(CIMBStatement.repayment_grace_end_date, 'dd/MM/yyyy')}`
    : '';
  const isOverDue = stmConditionsCIMB.isOverDue;
  return (
    <View testID={'stm-summary'} style={styles.container}>
      <View style={[StyleUtils.flexRow, StyleUtils.flexOne]}>
        <AppImage width={60} height={60} source={images.ImageCheckBoard} />
        <Spacer width={8} />
        <View
          style={{
            ...StyleUtils.flexOne,
            alignSelf: 'flex-start',
          }}>
          <TwoSideView
            style={StyleUtils.flexOne}
            left={<AppText style={{ width: 120 }}>{`Sao kê tháng ${period}`}</AppText>}
            right={
              <View style={styles.borderLinkButton}>
                <LinkButton onPress={onDetailCTAPress} testID="cta-detail">
                  Chi tiết
                </LinkButton>
              </View>
            }
          />
          <Spacer height={16} />
          <TwoSideView
            style={StyleUtils.flexOne}
            left={<AppText style={{ width: 120 }}>Đến hạn</AppText>}
            right={<AppText bold>{formattedDate}</AppText>}
          />
        </View>
      </View>
      {isOverDue && (
        <View testID="warning-banner" style={styles.warningContainer}>
          <AppImage width={24} height={24} source={images.IconWaringHexagon} />
          <Spacer width={8} />
          <AppText color={AppColors.text2} style={StyleUtils.flexOne}>
            Bạn đã trễ hạn thanh toán và đang bị tính lãi suất 0.1% mỗi ngày. Hãy thanh toán tối thiểu để tránh nợ quá
            hạn.
          </AppText>
        </View>
      )}
    </View>
  );
};

const LotteStatementQuickSummary: FC<{ onDetailCTAPress?: () => void }> = ({ onDetailCTAPress }) => {
  const { userStatement: LotteStatement, stmConditions } = useLotteStatementController();
  const formattedDate = LotteStatement ? LotteStatement.statement_due_date_with_hour : '';
  const incurredDate = LotteStatement ? formatDate(LotteStatement?.statement_billing_date, 'dd/MM/yyyy') : '';
  const isOnDue = stmConditions?.isOnDue;
  const isOverDue = stmConditions?.isOverDue;
  return (
    <View testID={'stm-summary'} style={styles.container}>
      <View style={[StyleUtils.flexRow, StyleUtils.flexOne]}>
        <AppImage width={60} height={60} source={images.ImageCheckBoard} />
        <Spacer width={8} />
        <View
          style={{
            ...StyleUtils.flexOne,
            alignSelf: 'flex-start',
          }}>
          <AppText style={StyleUtils.flexOne}>{`Sao kê ${incurredDate}`}</AppText>
          <Spacer height={16} />
          <TwoSideView
            style={StyleUtils.flexOne}
            left={<AppText style={{ width: 120 }}>Đến hạn</AppText>}
            right={<AppText bold>{formattedDate}</AppText>}
          />
        </View>
      </View>
      {isOnDue && (
        <View testID="notice-banner" style={styles.bannerContainer}>
          <AppText bold>Đã thanh toán Dư nợ đến hạn?</AppText>
          <AppText color={AppColors.text2} style={StyleUtils.flexOne}>
            Dư nợ sẽ được cập nhật trong vòng 5 phút, tối đa 1 ngày làm việc sau khi thanh toán thành công.
          </AppText>
        </View>
      )}
      {isOverDue && (
        <View testID="warning-banner" style={styles.warningContainer}>
          <AppText testID="warning-text">
            Bạn đã trễ hạn thanh toán. Vui lòng thanh toán dư nợ đến hạn ngay để hạn chế phát sinh lãi và nợ quá hạn.
          </AppText>
        </View>
      )}
    </View>
  );
};

export const StatementQuickSummary: FC<Props> = ({ onDetailCTAPress, partnerCode }) => {
  return partnerCode === PartnerCode.CIMB ? (
    <CIMBStatementQuickSummary onDetailCTAPress={onDetailCTAPress} />
  ) : (
    <LotteStatementQuickSummary onDetailCTAPress={onDetailCTAPress} />
  );
};

const styles = StyleSheet.create({
  emptyContainer: {
    backgroundColor: AppColors.orange['1'],
    borderRadius: 8,
    paddingVertical: 12,
    paddingHorizontal: 16,
    flexDirection: 'row',
    alignItems: 'center',
    height: 68,
    marginBottom: 16,
  },
  container: {
    backgroundColor: AppColors.background,
    borderRadius: 8,
    padding: 16,
    margin: 16,
    flex: 1,
  },
  borderLinkButton: {
    flexDirection: 'row',
    borderWidth: 1,
    borderColor: AppColors.primary,
    borderRadius: 16,
    paddingVertical: 4,
    paddingHorizontal: 8,
  },
  greenRect: {
    width: 4,
    height: 8,
    backgroundColor: AppColors.green['4'],
  },
  blueRect: {
    width: 4,
    height: 8,
    backgroundColor: AppColors.primary,
  },
  amountText: {
    marginStart: 8,
  },
  row: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  warningContainer: {
    flexDirection: 'row',
    backgroundColor: AppColors.red['5'],
    borderRadius: 8,
    paddingVertical: 12,
    paddingHorizontal: 16,
    alignItems: 'center',
    marginTop: 16,
  },
  bannerContainer: {
    flexDirection: 'column',
    backgroundColor: AppColors.background2,
    borderRadius: 8,
    paddingVertical: 12,
    paddingHorizontal: 16,
    marginTop: 16,
  },
});

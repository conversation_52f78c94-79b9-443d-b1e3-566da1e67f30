import { dummyStm, dummyCIMBStmCondition, dummyLotteStmCondition, dummyLotteStm } from '../../jest/fakeStatementData';
import { StatementStatus } from 'bnpl-shared/src/components';
import { renderWithRedux } from 'bnpl-shared/src/jest/renderWithRedux';
import React from 'react';
import { fireEvent } from '@testing-library/react-native';
import { PartnerCode } from 'bnpl-shared/src/constants';

const mockStm = dummyStm;
let mockStmCondition = dummyCIMBStmCondition;

const mockLotteStm = dummyLotteStm;
let mockLotteStmCondition = dummyLotteStmCondition;

jest.mock('bnpl-shared/src/utils/statement_controller/useCIMBStatementController', () => ({
  useCIMBStatementController: () => ({
    stmConditions: mockStmCondition,
    userStatement: mockStm,
  }),
}));

jest.mock('bnpl-shared/src/utils/statement_controller/useLotteStatementController', () => ({
  useLotteStatementController: () => ({
    stmConditions: mockLotteStmCondition,
    userStatement: mockLotteStm,
  }),
}));

describe('StatementStatus', () => {
  describe('CIMB', () => {
    it('verify render no debt statement', () => {
      mockStmCondition = {
        ...dummyCIMBStmCondition,
        noSpendInPeriod: true,
      };
      const { queryByText } = renderWithRedux(<StatementStatus partnerCode={PartnerCode.CIMB} />);
      expect(queryByText('Không có dư nợ tháng 1')).toBeTruthy();
    });

    it('verify render statement is repaid', () => {
      mockStmCondition = {
        ...dummyCIMBStmCondition,
        isRepaid: true,
      };
      const { queryByText } = renderWithRedux(<StatementStatus partnerCode={PartnerCode.CIMB} />);
      expect(queryByText('Đã thanh toán sao kê tháng 1')).toBeTruthy();
    });

    it('verify render statement is repaid min pay', () => {
      mockStmCondition = {
        ...dummyCIMBStmCondition,
        minPayIsRepaid: true,
      };
      const { queryByText } = renderWithRedux(<StatementStatus partnerCode={PartnerCode.CIMB} />);
      expect(queryByText('Đã thanh toán tối thiểu sao kê tháng 1')).toBeTruthy();
    });

    it('verify action triggered', () => {
      mockStmCondition = {
        ...dummyCIMBStmCondition,
        minPayIsRepaid: true,
      };
      const onDetailPress = jest.fn();
      const { getByText, queryByText } = renderWithRedux(
        <StatementStatus partnerCode={PartnerCode.CIMB} onDetailPress={onDetailPress} />,
      );
      expect(queryByText('Chi tiết')).toBeTruthy();
      fireEvent.press(getByText('Chi tiết'));
      expect(onDetailPress).toHaveBeenCalled();
    });
  });

  describe('Lotte', () => {
    it('verify render no debt statement', () => {
      mockLotteStmCondition = {
        ...dummyLotteStmCondition,
        noSpendInPeriod: true,
      };
      const { queryByText } = renderWithRedux(<StatementStatus partnerCode={PartnerCode.LOTTE} />);
      expect(queryByText('Chi tiết')).toBeFalsy();
      expect(queryByText('Không có dư nợ đến hạn Sao kê 11/04/2024')).toBeTruthy();
    });
    it('verify render debt statement has been repaid', () => {
      mockLotteStmCondition = {
        ...dummyLotteStmCondition,
        noSpendInPeriod: false,
        isRepaid: true,
      };
      const { queryByText } = renderWithRedux(<StatementStatus partnerCode={PartnerCode.LOTTE} />);
      expect(queryByText('Chi tiết')).toBeFalsy();
      expect(queryByText('Đã thanh toán Sao kê 11/04/2024')).toBeTruthy();
    });
  });
});

import React, { FC } from 'react';
import { StyleSheet } from 'bnpl-shared/src/shared/styles/StyleSheet';
import { AppColors, PartnerCode } from 'bnpl-shared/src/constants';
import { AppImage, AppText } from 'bnpl-shared/src/shared/react-native-customized';
import { LinkButton } from 'bnpl-shared/src/shared/LinkButton';
import { View } from 'react-native';
import { StyleUtils } from 'bnpl-shared/src/shared/styles/StyleUtils';
import { images } from 'bnpl-shared/src/res';
import { Spacer } from 'bnpl-shared/src/components/Spacer';
import { formatDate } from 'bnpl-shared/src/shared/utils/formatDateStatement';
import { useCIMBStatementController } from 'bnpl-shared/src/utils/statement_controller/useCIMBStatementController';
import { useLotteStatementController } from 'bnpl-shared/src/utils/statement_controller/useLotteStatementController';

const renderStatusMessage = (partnerCode: PartnerCode) => {
  const { stmConditions: smtConditionsCIMB, userStatement: CIMBStatement } = useCIMBStatementController();
  const { stmConditions: stmConditionsLotte, userStatement: LotteStatement } = useLotteStatementController();

  let period = '...';
  let statusMessage = '';
  let statementDate = '...';

  switch (partnerCode) {
    case PartnerCode.CIMB:
      if (CIMBStatement) {
        try {
          period = String(new Date(CIMBStatement.statement_date).getMonth() + 1);
        } catch (err: any) {}
      }
      if (smtConditionsCIMB.noSpendInPeriod) {
        statusMessage = 'Không có dư nợ';
      } else if (smtConditionsCIMB.isRepaid) {
        statusMessage = 'Đã thanh toán sao kê';
      } else if (smtConditionsCIMB.minPayIsRepaid) {
        statusMessage = 'Đã thanh toán tối thiểu sao kê';
      }
      return (
        <AppText style={StyleUtils.flexOne}>
          {statusMessage} tháng {period}
        </AppText>
      );
    case PartnerCode.LOTTE:
      if (LotteStatement) {
        try {
          statementDate = formatDate(LotteStatement.statement_billing_date, 'dd/MM/yyyy') || statementDate;
        } catch (err: any) {}
      }
      if (stmConditionsLotte.noSpendInPeriod) {
        statusMessage = 'Không có dư nợ đến hạn Sao kê';
      } else if (stmConditionsLotte.isRepaid) {
        statusMessage = 'Đã thanh toán Sao kê';
      }
      if (statusMessage === '') {
        return null;
      }
      return (
        <AppText style={StyleUtils.flexOne}>
          {statusMessage} {statementDate}
        </AppText>
      );
    default:
      return null;
  }
};

export const StatementStatus: FC<{ onDetailPress?: () => void; partnerCode: PartnerCode }> = ({
  onDetailPress,
  partnerCode,
}) => {
  return (
    <View testID={'stm-status'} style={styles.root}>
      <AppImage width={24} height={24} tintColor={AppColors.green['4']} source={images.IconCheck} />
      <Spacer width={4} />
      {renderStatusMessage(partnerCode)}
      <Spacer width={4} />
      {partnerCode === PartnerCode.CIMB && <LinkButton onPress={onDetailPress}>Chi tiết</LinkButton>}
    </View>
  );
};

const styles = StyleSheet.create({
  root: {
    flexDirection: 'row',
    borderRadius: 8,
    borderColor: AppColors.primary2,
    paddingHorizontal: 16,
    height: 48,
    borderWidth: 1,
    margin: 16,
    alignItems: 'center',
    justifyContent: 'flex-start',
  },
  description: {
    textAlign: 'center',
  },
  button: { width: 280 },
});

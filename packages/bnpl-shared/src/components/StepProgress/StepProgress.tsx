import React, { FC, memo } from 'react';
import { StyleSheet, View, ViewStyle } from 'react-native';
import { Colors } from 'bnpl-shared/src/shared/styles/StyleUtils';
import { AppImage } from 'bnpl-shared/src/shared/react-native-customized';
import { useBindingProgress } from 'bnpl-shared/src/utils/useBindingProgress';
import { images } from 'bnpl-shared/src/res';

type Props = {
  style?: ViewStyle;
};

const StepProgress: FC<Props> = memo(({ style }) => {
  const { stepBar, stepPoint } = useBindingProgress();

  return (
    <View style={[styles.root, style]}>
      <View style={styles.progressBar}>
        <View style={[styles.stepProgress, stepBar >= 1 && styles.stepActive]} />
        <View style={[styles.stepProgress, stepBar >= 2 && styles.stepActive]} />
        <View style={[styles.stepProgress, stepBar >= 3 && styles.stepActive]} />
        <View style={[styles.stepProgress, stepBar === 4 && styles.stepActive]} />
      </View>

      <View style={[styles.circlePoint, stepPoint >= 1 && styles.stepActive]}>
        <AppImage source={images.IconStepProgress1} height={10} width={10} />
      </View>
      <View style={[styles.circlePoint, stepPoint >= 2 && styles.stepActive]}>
        <AppImage source={images.IconStepProgress2} height={10} width={10} />
      </View>
      <View style={[styles.circlePoint, stepPoint >= 3 && styles.stepActive]}>
        <AppImage source={images.IconStepProgress3} height={10} width={10} />
      </View>
    </View>
  );
});

export default StepProgress;

//#region
const styles = StyleSheet.create({
  root: {
    width: '100%',
    position: 'relative',
    flexDirection: 'row',
    justifyContent: 'space-around',
    paddingTop: 18,
  },
  circlePoint: {
    backgroundColor: Colors.disabled,
    borderRadius: 10,
    width: 20,
    height: 20,
    justifyContent: 'center',
    alignItems: 'center',
  },
  stepActive: {
    backgroundColor: Colors.primary,
    marginRight: -1,
  },
  progressBar: {
    backgroundColor: Colors.primary2,
    height: 3,
    width: '70%',
    position: 'absolute',
    bottom: 9,
    left: '15%',
    flexDirection: 'row',
  },
  stepProgress: {
    backgroundColor: Colors.primary2,
    height: 3,
    flex: 1,
  },
});
//#endregion

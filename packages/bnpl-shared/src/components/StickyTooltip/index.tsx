import React, { FC, memo, useEffect, useRef, useState } from 'react';
import { Animated, LayoutRectangle, Platform, TouchableOpacity, View } from 'react-native';
import { calculateScaleDimension, StyleSheet } from 'bnpl-shared/src/shared/styles/StyleSheet';
import { AppText } from 'bnpl-shared/src/shared/react-native-customized';
import { AppColors } from 'bnpl-shared/src/constants';

//#region StickyTooltip
const StickyTooltip: FC<{
  visible?: boolean;
  content: string;
  onTooltipPress?: () => void;
  runAnimation?: boolean;
  anchorView?: LayoutRectangle;
}> = ({ visible, content, onTooltipPress, anchorView, runAnimation = false }) => {
  const [translateY] = useState(() => new Animated.Value(0));
  const translateYRef = useRef(translateY);
  translateYRef.current = translateY;

  useEffect(() => {
    if (runAnimation) {
      const animation = Animated.loop(
        Animated.sequence([
          Animated.timing(translateYRef.current, {
            toValue: 8,
            duration: 800,
            useNativeDriver: Platform.OS !== 'web',
          }),
          Animated.timing(translateYRef.current, {
            toValue: 4,
            duration: 800,
            useNativeDriver: Platform.OS !== 'web',
          }),
        ]),
      );
      animation.start();
      return () => animation.stop();
    }
  }, [runAnimation]);

  if (!anchorView || !visible) {
    return null;
  }

  return (
    <Animated.View
      style={[
        styles.tooltip,
        { transform: [{ translateY }] },
        {
          top: calculateScaleDimension(anchorView.height + 8),
          left: calculateScaleDimension(anchorView.x),
        },
      ]}>
      <TouchableOpacity onPress={onTooltipPress}>
        <View style={[styles.triangle]} />
        <View style={styles.tooltipContainer}>
          <AppText color={AppColors.white}>{content}</AppText>
        </View>
      </TouchableOpacity>
    </Animated.View>
  );
};

export default memo(StickyTooltip);

const styles = StyleSheet.create({
  tooltip: {
    position: 'absolute',
  },
  triangle: {
    width: 0,
    height: 0,
    backgroundColor: 'transparent',
    borderStyle: 'solid',
    borderLeftWidth: 10,
    borderRightWidth: 10,
    borderBottomWidth: 12,
    borderLeftColor: 'transparent',
    borderRightColor: 'transparent',
    borderBottomColor: AppColors.primary,
    margin: 0,
    borderWidth: 0,
    marginStart: 12,
    marginBottom: -1,
  },
  tooltipContainer: {
    padding: 8,
    borderRadius: 8,
    backgroundColor: AppColors.primary,
    justifyContent: 'center',
    alignItems: 'center',
  },
});
//#endregion

import { SuggestButton } from 'bnpl-shared/src/components/SuggestButton/index';
import { fireEvent, render } from '@testing-library/react-native';
import { AppText } from 'bnpl-shared/src/shared/react-native-customized';
import React from 'react';

describe(SuggestButton.name, () => {
  it('renders inactive', () => {
    const { queryByTestId, queryByText } = render(
      <SuggestButton testID={'test'} active={false} onPress={jest.fn()}>
        <AppText>test</AppText>
      </SuggestButton>,
    );
    expect(queryByText('test')).toBeTruthy();
    expect(queryByTestId('test-center-dot')).toBeFalsy();
  });
  it('renders active', () => {
    const { queryByTestId, queryByText } = render(
      <SuggestButton testID={'test'} active={true} onPress={jest.fn()}>
        <AppText>test</AppText>
      </SuggestButton>,
    );
    expect(queryByText('test')).toBeTruthy();
    expect(queryByTestId('test-center-dot')).toBeTruthy();
  });
  it('renders disabled', () => {
    const onPress = jest.fn();
    const { getByTestId } = render(
      <SuggestButton testID={'test'} active={false} disabled={true} onPress={onPress}>
        <AppText>test</AppText>
      </SuggestButton>,
    );
    fireEvent.press(getByTestId('test'));
    expect(onPress).not.toHaveBeenCalled();
  });
  it('renders radio button alignment correctly', () => {
    const onPress = jest.fn();
    const { queryByTestId } = render(
      <SuggestButton radioButtonAlign={'right'} testID={'test'} active={true} disabled={false} onPress={onPress}>
        <AppText>test</AppText>
      </SuggestButton>,
    );
    expect(queryByTestId('radio-right')).toBeTruthy();
    expect(queryByTestId('radio-left')).toBeFalsy();
  });
});

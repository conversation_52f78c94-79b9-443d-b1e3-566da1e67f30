//#region SuggestButton
import React, { FC } from 'react';
import { StyleProp, StyleSheet, TouchableOpacity, View, ViewStyle } from 'react-native';
import { AppColors } from 'bnpl-shared/src/constants';

export const SuggestButton: FC<{
  testID?: string;
  active: boolean;
  onPress: () => void;
  style?: StyleProp<ViewStyle>;
  disabled?: boolean;
  radioButtonAlign?: 'left' | 'right';
}> = ({ children, active, onPress, style, disabled, testID, radioButtonAlign = 'left' }) => {
  const styles = suggestButtonStyles;
  const renderRadioButton = (radioButtonTestID?: string) => (
    <TouchableOpacity
      testID={radioButtonTestID}
      disabled={disabled}
      onPress={onPress}
      style={[styles.radioButtonWrapper, active && styles.active, disabled && { opacity: 0.3 }]}>
      {active ? <View testID={`${testID}-center-dot`} style={styles.dot} /> : null}
    </TouchableOpacity>
  );
  return (
    <TouchableOpacity disabled={disabled} testID={testID} onPress={onPress}>
      <View style={[styles.root, active ? styles.active : {}, disabled ? { opacity: 0.5 } : {}, style]}>
        {radioButtonAlign === 'left' ? renderRadioButton('radio-left') : null}
        {children}
        {radioButtonAlign === 'right' ? renderRadioButton('radio-right') : null}
      </View>
    </TouchableOpacity>
  );
};

const suggestButtonStyles = StyleSheet.create({
  root: {
    flex: 1,
    borderWidth: 1,
    borderColor: AppColors.expensePieStroke,
    borderRadius: 8,
    paddingHorizontal: 16,
    paddingVertical: 8,
    paddingRight: 8,
    flexDirection: 'row',
    alignItems: 'center',
    overflow: 'visible',
  },
  radioButtonWrapper: {
    height: 20,
    width: 20,
    borderRadius: 10,
    borderWidth: 2,
    borderColor: AppColors.disabled,
    alignItems: 'center',
    justifyContent: 'center',
  },
  active: {
    borderColor: AppColors.primary,
  },
  dot: {
    height: 10,
    width: 10,
    borderRadius: 5,
    backgroundColor: AppColors.primary,
  },
});
//#endregion

import { TabSelector } from './index';
import { fireEvent, render } from '@testing-library/react-native';
import React from 'react';

describe(TabSelector.name, () => {
  it('renders', () => {
    const { queryByText } = render(
      <TabSelector
        values={['A', 'B', 'C']}
        itemActiveStyle={{}}
        itemInactiveStyle={{}}
        activeTextColor={''}
        inactiveTextColor={''}
        onValueChange={jest.fn}
      />,
    );
    expect(queryByText('A')).toBeTruthy();
    expect(queryByText('B')).toBeTruthy();
    expect(queryByText('C')).toBeTruthy();
  });
  it('verify tab select callback', () => {
    const onValueChange = jest.fn();
    const { getByText } = render(
      <TabSelector
        values={['A', 'B', 'C']}
        itemActiveStyle={{}}
        itemInactiveStyle={{}}
        activeTextColor={''}
        inactiveTextColor={''}
        onValueChange={onValueChange}
      />,
    );
    fireEvent.press(getByText('B'));
    expect(onValueChange).toHaveBeenCalledWith(1);
  });
});

import React, { forwardRef, useEffect, useImperativeHandle, useRef, useState } from 'react';
import { ScrollView, StyleProp, TextStyle, TouchableOpacity, View, ViewStyle } from 'react-native';
import { AppText } from 'bnpl-shared/src/shared/react-native-customized';
import { StyleSheet } from 'bnpl-shared/src/shared/styles/StyleSheet';
import { AppColors } from 'bnpl-shared/src/constants';

type Props = {
  values: string[];
  initialIndex?: number;
  itemActiveStyle?: StyleProp<ViewStyle>;
  itemInactiveStyle?: StyleProp<ViewStyle>;
  containerStyle?: StyleProp<ViewStyle>;
  tabStyle?: StyleProp<ViewStyle>;
  tabTextStyle?: StyleProp<TextStyle>;
  activeTextColor?: string;
  inactiveTextColor?: string;
  tabContainerStyle?: StyleProp<ViewStyle>;
  onValueChange: (index: number) => void;
  highLightItems?: Array<number>;
  scrollEnabled?: boolean;
};

export const TabSelector = forwardRef(
  (
    {
      values,
      initialIndex = 0,
      tabStyle = {},
      itemActiveStyle = styles.tabActive,
      itemInactiveStyle = styles.tabInactive,
      activeTextColor = AppColors.primary,
      inactiveTextColor = AppColors.text,
      tabTextStyle,
      onValueChange,
      containerStyle,
      tabContainerStyle,
      highLightItems,
      scrollEnabled = true,
    }: Props,
    ref,
  ) => {
    useImperativeHandle(ref, () => ({
      setActiveIndex,
    }));

    const [activeIndex, setActiveIndex] = useState<number>(initialIndex);
    const scrollViewRef = useRef<any>(null);
    const positionCacheRef = useRef<any>();
    const TIMEOUT = useRef<any>();

    useEffect(() => {
      if (initialIndex) {
        const timeout = setTimeout(() => {
          handleScrollToIndex(initialIndex);
        }, 1000);
        return () => {
          timeout && clearTimeout(timeout);
        };
      }
    }, []);

    useEffect(() => {
      setActiveIndex(initialIndex);
    }, [initialIndex]);

    useEffect(() => {
      onValueChange(activeIndex);
      handleScrollToIndex(activeIndex);
      return () => {
        TIMEOUT.current && clearTimeout(TIMEOUT.current);
      };
    }, [activeIndex, onValueChange]);

    const handleScrollToIndex = (index: number) => {
      const positionIndex = positionCacheRef?.current?.[index];
      if (positionIndex >= 0) {
        if (TIMEOUT.current) {
          clearTimeout(TIMEOUT.current);
        }
        TIMEOUT.current = setTimeout(() => {
          scrollViewRef?.current?.scrollTo?.({ x: positionIndex });
        }, 200);
      }
    };

    return (
      <ScrollView
        ref={scrollViewRef}
        testID="tab-selector"
        contentContainerStyle={[styles.containerStyle, containerStyle]}
        horizontal={true}
        showsHorizontalScrollIndicator={false}
        bounces={true}
        scrollEnabled={scrollEnabled}>
        {values.map((value, index) => {
          return (
            <View
              key={`${value}_${index}`}
              style={tabContainerStyle}
              onLayout={({ nativeEvent }) => {
                positionCacheRef.current = {
                  ...positionCacheRef.current,
                  [`${index}`]: nativeEvent.layout.x,
                };
              }}>
              <TabButton
                tabStyle={tabStyle}
                label={value}
                active={activeIndex === index}
                activeTextColor={activeTextColor}
                inactiveTextColor={inactiveTextColor}
                activeStyle={itemActiveStyle}
                inactiveStyle={itemInactiveStyle}
                tabTextStyle={tabTextStyle}
                onChange={() => {
                  setActiveIndex(index);
                }}
                activeDot={highLightItems?.includes(index)}
              />
            </View>
          );
        })}
      </ScrollView>
    );
  },
);

const styles = StyleSheet.create({
  containerStyle: {},
  tab: {
    height: 34,
    borderRadius: 16,
    paddingVertical: 7,
    paddingHorizontal: 12,
    marginRight: 12,
    alignItems: 'center',
    justifyContent: 'center',
  },
  tabInactive: {
    backgroundColor: AppColors.background4,
    borderWidth: 0,
  },
  tabActive: {
    borderWidth: 1,
    borderColor: AppColors.primary,
  },
  dot: {
    borderRadius: 100,
    position: 'absolute',
    right: 0,
    top: 0,
    width: 8,
    height: 8,
    backgroundColor: AppColors.red[4],
  },
});

//#region TabButton
const TabButton = ({
  label,
  active,
  onChange,
  activeTextColor,
  inactiveTextColor,
  activeStyle,
  inactiveStyle,
  tabStyle,
  tabTextStyle,
  activeDot = false,
}: {
  label: string;
  active: boolean;
  activeTextColor: string;
  inactiveTextColor: string;
  activeStyle: StyleProp<ViewStyle>;
  inactiveStyle: StyleProp<ViewStyle>;
  tabStyle?: StyleProp<ViewStyle>;
  onChange: () => void;
  tabTextStyle?: StyleProp<TextStyle>;
  activeDot?: boolean;
}) => {
  return (
    <TouchableOpacity onPress={onChange} style={[styles.tab, active ? activeStyle : inactiveStyle, tabStyle]}>
      <AppText
        style={[tabTextStyle, { color: active ? activeTextColor : inactiveTextColor }]}
        size={14}
        height={18}
        weight="400"
        numberOfLines={1}>
        {label}
      </AppText>
      {activeDot ? <View style={styles.dot} /> : null}
    </TouchableOpacity>
  );
};

//#endregion

import React, { forwardRef } from 'react';
import styled, { css } from 'styled-components';
import { TabItemProps } from './types';
import { Label } from '@zpi/looknfeel/typography';
import { AppColors } from 'bnpl-shared/src/constants';

const TabItem = forwardRef<any, TabItemProps>(({ label, value, active, onChange, badge }, ref) => {
  return (
    <Wrapper ref={ref} badge={badge} onClick={() => onChange(value)}>
      <Label bold color={active ? AppColors.blue[500] : AppColors.dark[300]}>
        {label}
      </Label>
    </Wrapper>
  );
});

TabItem.displayName = 'TabItem';

export default TabItem;

const Wrapper = styled.button<{ badge?: string }>`
  padding: 10px;
  border-radius: 10px;
  background-color: white;
  height: 48px;
  display: flex;
  align-items: center;
  justify-content: center;
  flex: 1;
  ${({ badge }) =>
    badge &&
    css`
      label {
        position: relative;
      }
      label::after {
        content: '${badge}';
        position: absolute;
        left: calc(100% + 8px);
        background-color: ${AppColors.red[500]};
        border-radius: 8px;
        height: 16px;
        padding: 0px 4px;
        color: white;
        font-size: 12px;
        line-height: 16px;
        font-weight: 700;
      }
    `}
`;

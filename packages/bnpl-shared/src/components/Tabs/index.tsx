import React, { useRef, useState } from 'react';
import styled from 'styled-components';
import { TabsProps } from './types';
import TabItem from './TabItem';
import { AppColors } from 'bnpl-shared/src/constants';

export default function Tabs({ tabs, activeTab, onChange }: TabsProps) {
  const tabBarWrapperRef = useRef<any>(null);
  const [activeTabEl, setActiveTabEl] = useState<any>(null);

  // Define the handleTabRef function
  const handleTabRef = (el: any, isActive: boolean) => {
    if (isActive && el) {
      setActiveTabEl(el);
    }
  };

  const { left: wrapperLeft } = tabBarWrapperRef?.current?.getBoundingClientRect() || { left: 0 };
  const scrollLeft = tabBarWrapperRef?.current?.scrollLeft || 0;
  const { width, left: activeTabLeft } = activeTabEl?.getBoundingClientRect() || { width: 0, left: 0 };
  const left = activeTabLeft + scrollLeft - wrapperLeft;
  const activeLineStyle = { width: `${width}px`, left: `${left}px` };

  return (
    <Wrapper ref={tabBarWrapperRef}>
      {tabs.map(tab => (
        <TabItem
          key={tab.value}
          label={tab.label}
          value={tab.value}
          active={activeTab === tab.value}
          badge={tab.badge}
          onChange={onChange}
          ref={el => handleTabRef(el, activeTab === tab.value)}
        />
      ))}
      <ActiveLine style={activeLineStyle} />
    </Wrapper>
  );
}

const Wrapper = styled.div`
  display: flex;
  align-items: center;
  justify-content: center;
  width: 100%;
  position: relative;
`;

const ActiveLine = styled.div`
  position: absolute;
  bottom: 0;
  height: 2px;
  background-color: ${AppColors.blue[500]};
  transition: all 0.3s;
`;

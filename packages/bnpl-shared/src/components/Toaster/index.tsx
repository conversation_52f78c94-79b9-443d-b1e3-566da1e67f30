import React from 'react';
import { View, Animated } from 'react-native';
import { GeneralCheckSolidSecondary, GeneralClosecircleSolidSecondary, GeneralInfoSolidSecondary, GeneralWarningSolidSecondary } from "@zpi/looknfeel-icons";
import { toast as sonnerToast } from 'sonner';
import { AppColors } from 'bnpl-shared/src/constants';
import { StyleSheet } from 'bnpl-shared/src/shared/styles/StyleSheet';
import { AppText } from 'bnpl-shared/src/shared/react-native-customized';

interface ToastContentProps {
  content?: string;
  navigationBarHeight?: number;
}

const ToastWrapper = ({ children }: { children: React.ReactNode }) => {
  const [fadeAnim] = React.useState(new Animated.Value(0));
  const [slideAnim] = React.useState(new Animated.Value(-20));

  React.useEffect(() => {
    Animated.parallel([
      Animated.timing(fadeAnim, {
        toValue: 1,
        duration: 300,
        useNativeDriver: true,
      }),
      Animated.timing(slideAnim, {
        toValue: 0,
        duration: 300,
        useNativeDriver: true,
      })
    ]).start();
  }, []);

  return (
    <Animated.View
      style={{
        opacity: fadeAnim,
        transform: [{ translateY: slideAnim }],
      }}
    >
      {children}
    </Animated.View>
  );
};

const CustomToastComponents = {
  success: ({ content, navigationBarHeight = 0 }: ToastContentProps) => {
    return (
      <ToastWrapper>
        <View style={[styles.toastContainer, styles.successToast, navigationBarHeight > 0 && { marginTop: navigationBarHeight }]}>
          <View style={styles.iconContainer}>
            <GeneralCheckSolidSecondary color={AppColors.green[500]} />
          </View>
          <View style={styles.contentContainer}>
            {content && <AppText color={AppColors.dark[500]}>{content}</AppText>}
          </View>
        </View>
      </ToastWrapper>
    )
  },
  error: ({ content, navigationBarHeight = 0 }: ToastContentProps) => (
    <ToastWrapper>
      <View style={[styles.toastContainer, styles.errorToast, navigationBarHeight > 0 && { marginTop: navigationBarHeight }]}>
        <View style={styles.iconContainer}>
          <GeneralClosecircleSolidSecondary color={AppColors.red[500]} />
        </View>
        <View style={styles.contentContainer}>
          {content && <AppText color={AppColors.dark[500]}>{content}</AppText>}
        </View>
      </View>
    </ToastWrapper>
  ),
  info: ({ content, navigationBarHeight = 0 }: ToastContentProps) => (
    <ToastWrapper>
      <View style={[styles.toastContainer, styles.infoToast, navigationBarHeight > 0 && { marginTop: navigationBarHeight }]}>
        <View style={styles.iconContainer}>
          <GeneralInfoSolidSecondary color={AppColors.blue[500]} />
        </View>
        <View style={styles.contentContainer}>
          {content && <AppText color={AppColors.dark[500]}>{content}</AppText>}
        </View>
      </View>
    </ToastWrapper>
  ),
  warning: ({ content, navigationBarHeight = 0 }: ToastContentProps) => (
    <ToastWrapper>
      <View style={[styles.toastContainer, styles.warningToast, navigationBarHeight > 0 && { marginTop: navigationBarHeight }]}>
        <View style={styles.iconContainer}>
          <GeneralWarningSolidSecondary color={AppColors.orange[500]} />
        </View>
        <View style={styles.contentContainer}>
          {content && <AppText color={AppColors.dark[500]}>{content}</AppText>}
        </View>
      </View>
    </ToastWrapper>
  ),
};

const styles = StyleSheet.create({
  toastContainer: {
    flexDirection: 'row',
    backgroundColor: 'white',
    borderRadius: 25,
    marginHorizontal: 16,
    alignItems: 'center',
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.25,
    shadowRadius: 3.84,
    elevation: 5,
    borderWidth: 1,
    padding: 12,
  },
  successToast: {
    borderColor: AppColors.green[200],
  },
  errorToast: {
    borderColor: AppColors.red[200],
  },
  warningToast: {
    borderColor: AppColors.orange[200],
  },
  infoToast: {
    borderColor: AppColors.blue[200],
  },
  iconContainer: {
    marginRight: 8,
  },
  contentContainer: {
    flex: 1,
  },
});

interface ToastOptions {
  duration?: number;
};

// Helper functions to show toasts while maintaining existing UI
export const toast = {
  navigationBarHeight: 0,
  setNavigationBarHeight: (height: number) => {
    toast.navigationBarHeight = height;
  },
  success: (message: string, options?: ToastOptions) => {
    return sonnerToast.custom(() => CustomToastComponents.success({ 
      content: message, 
      navigationBarHeight: toast.navigationBarHeight,
    }), {
      duration: options?.duration,
    });
  },
  error: (message: string, options?: ToastOptions) => {
    return sonnerToast.custom(() => CustomToastComponents.error({ 
      content: message, 
      navigationBarHeight: toast.navigationBarHeight,
    }), {
      duration: options?.duration,
    });
  },
  info: (message: string, options?: ToastOptions) => {
    
    return sonnerToast.custom(() => CustomToastComponents.info({ 
      content: message, 
      navigationBarHeight: toast.navigationBarHeight,
    }), {
      duration: options?.duration,
    });
  },
  warning: (message: string, options?: ToastOptions) => {
    return sonnerToast.custom(() => CustomToastComponents.warning({ 
      content: message, 
      navigationBarHeight: toast.navigationBarHeight,
    }), {
      duration: options?.duration,
    });
  },
};

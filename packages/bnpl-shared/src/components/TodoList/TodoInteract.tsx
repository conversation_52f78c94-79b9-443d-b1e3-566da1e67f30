import React from 'react';
import { TodoAction, TodoTaskState } from 'bnpl-shared/src/types/TodoTypes';
import { AppButton, AppImage } from 'bnpl-shared/src/shared/react-native-customized';
import { images } from 'bnpl-shared/src/res';
import { AppColors } from 'bnpl-shared/src/constants';
import { StyleSheet } from 'bnpl-shared/src/shared/styles/StyleSheet';

type TodoInteractProps = {
  state: TodoTaskState;
  onAction: TodoAction;
};

export const TodoInteract: React.FC<Partial<TodoInteractProps>> = ({ state, onAction }) => {
  if (state === 'completed') {
    return <AppImage width={28} height={28} style={styles.completeIcon} source={images.IconCheckCircle} />;
  }

  return (
    <AppButton
      debouncePress
      title="Làm ngay"
      variant="outlined"
      onPress={onAction}
      buttonStyle={styles.button}
      titleStyle={styles.buttonTitle}
    />
  );
};

const styles = StyleSheet.create({
  button: {
    height: 32,
    paddingHorizontal: 8,
  },
  buttonTitle: {
    fontSize: 12,
    fontWeight: '400',
  },
  completeIcon: {
    tintColor: AppColors.green[4],
  },
});

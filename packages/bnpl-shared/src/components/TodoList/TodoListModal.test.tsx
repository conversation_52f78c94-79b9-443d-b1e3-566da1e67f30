import { checkNativeFeatureAvailable, getCurrentDate } from 'bnpl-shared/src/utils';
import { store } from 'bnpl-shared/src/redux/store';
import { setBindingStatus, setOnboardingStatus } from 'bnpl-shared/src/redux/bindingReducer';
import { BindingStatus, OnboardingStatus, ResourceState } from 'bnpl-shared/src/types';
import { setSummaryUserBalance } from 'bnpl-shared/src/redux/userBalanceReducer';
import { renderWithRedux } from 'bnpl-shared/src/jest/renderWithRedux';
import { FAKE_NAVIGATION } from 'bnpl-shared/src/jest/constants';
import { fireEvent, waitFor } from '@testing-library/react-native';
import { Linking, Platform } from 'react-native';
import { TodoTaskType } from 'bnpl-shared/src/types/TodoTypes';
import { launchDeepLink } from 'bnpl-shared/src/shared/ZaloPayModules';
import React from 'react';
import { TodoListModal } from 'bnpl-shared/src/components/TodoList/TodoListModal';

describe('when user have todo list task', () => {
  beforeEach(() => {
    (checkNativeFeatureAvailable as jest.Mock).mockReturnValue(true);
    store.dispatch(setBindingStatus(BindingStatus.open));
    store.dispatch(setOnboardingStatus(OnboardingStatus.APPROVE));
    store.dispatch(
      setSummaryUserBalance({
        state: ResourceState.READY,
        data: {
          total_limit: 5000000,
          available_balance: 5000000,
          over_repayment_balance: 250000,
          outstanding_balance: 0,
          user_service_fee: 0,
        },
      }),
    );
  });

  it('todo list task would not display calendar reminder item when native feature version is not valid', async () => {
    (checkNativeFeatureAvailable as jest.Mock).mockReturnValue(false);
    const { queryByText } = renderWithRedux(<TodoListModal navigation={FAKE_NAVIGATION} />);
    await waitFor(() => expect(queryByText('Nhắc hạn thanh toán qua iOS/ Android')).toBeFalsy());
  });

  it('action follow zalopay oa in zalo work as expect', async () => {
    const openURLSpy = jest.spyOn(Linking, 'openURL');
    const { getByTestId } = renderWithRedux(<TodoListModal navigation={FAKE_NAVIGATION} />);
    const followOATask = getByTestId(TodoTaskType.ZalopPayOAFollow);
    const followOAAction = followOATask.findByProps({ title: 'Làm ngay' });
    fireEvent.press(followOAAction);
    await waitFor(() => expect(openURLSpy).toHaveBeenCalledWith('https://zalo.me/4046405574025332160'));
    openURLSpy.mockRestore();
  });

  it('action create calendar reminder work as expect', async () => {
    jest.useFakeTimers();
    Platform.OS = 'ios';
    const mockDate = new Date(1684304824000); // 17/05/2023
    (getCurrentDate as jest.Mock).mockReturnValue(mockDate);
    const { getByTestId } = renderWithRedux(<TodoListModal navigation={FAKE_NAVIGATION} />);
    const addCalendarTask = getByTestId(TodoTaskType.RepaymentReminder);
    const addCalendarAction = addCalendarTask.findByProps({ title: 'Làm ngay' });
    fireEvent.press(addCalendarAction);
    jest.advanceTimersByTime(1100);
    await waitFor(() => expect(launchDeepLink).toHaveBeenCalled());
  });
});

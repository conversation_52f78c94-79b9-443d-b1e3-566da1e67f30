import React, { FC, useEffect } from 'react';
import { TodoList } from 'bnpl-shared/src/components/TodoList';
import { AppImage } from 'bnpl-shared/src/shared/react-native-customized';
import { images } from 'bnpl-shared/src/res';
import { Linking, Platform, View } from 'react-native';
import { StyleSheet } from 'bnpl-shared/src/shared/styles/StyleSheet';
import { StyleUtils } from 'bnpl-shared/src/shared/styles/StyleUtils';
import BottomButton from 'bnpl-shared/src/shared/BottomButton';
import { InfoModalService } from 'bnpl-shared/src/services';
import { AppColors } from 'bnpl-shared/src/constants';
import { TodoTaskType } from 'bnpl-shared/src/types/TodoTypes';
import CalendarDeeplinkBuilder, { RecurrenceType } from '../../features/calendar_reminder/CalendarDeeplinkBuilder';
import { BNPL_APP_REDIRECT, ZLP_OA } from 'bnpl-shared/src/constants/PublicUrls';
import { ABTestingGroup, BindingStatus, ExperimentName, UtmCampaign } from 'bnpl-shared/src/types';
import { launchDeepLink } from 'bnpl-shared/src/shared/ZaloPayModules';
import { useAppSelector } from 'bnpl-shared/src/redux/store';
import { useAutoRepaymentBinding } from 'bnpl-shared/src/features/auto_repayment/helpers/useAutoRepaymentBinding';
import { useRemoteConfigs } from 'bnpl-shared/src/remote_configs/useRemoteConfigs';
import RepaymentTracking from 'bnpl-shared/src/screens/RepaymentScreen/RepaymentTracking';

export const TodoListModal: FC<{ navigation: any }> = ({ navigation }) => {
  const abTestAutoRepayment = useAppSelector(state => state.abTesting[ExperimentName.AUTO_REPAYMENT]);
  const enableAutoRepayment = abTestAutoRepayment === ABTestingGroup.Variation_1;
  const { getConfig } = useRemoteConfigs();
  const { actions } = useAutoRepaymentBinding({ navigation, entryPoint: 'todo-list' });
  const bindingStatus = useAppSelector(state => state.binding.bindingStatus);

  useEffect(() => {
    RepaymentTracking.trackTodoListModalShow();
  }, []);

  const handleToDoTaskTriggered = (type: TodoTaskType) => {
    RepaymentTracking.trackTodoListItemClick(type);
    InfoModalService.hideModal();
    switch (type) {
      // case TodoTaskType.FirstRepayment:
      //   navigation.navigate(ScreenKey.Repayment);
      //   break;
      case TodoTaskType.UseAutoRepayment:
        if ((bindingStatus === BindingStatus.open || bindingStatus === BindingStatus.locked) && enableAutoRepayment) {
          actions.toggleAutoRepay(false);
        }
        break;
      // case TodoTaskType.FirstPurchase:
      //   setTimeout(() => {
      //     InfoModalService.showModal({
      //       screen: <AllServicesModal />,
      //       type: ModalType.BOTTOM_SHEET,
      //       bottomSheetProps: {
      //         title: 'Dịch vụ thanh toán',
      //         containerStyle: { height: windowHeight - 240 },
      //       },
      //     });
      //   }, 300);
      //   break;
      case TodoTaskType.RepaymentReminder:
        const deeplink = new CalendarDeeplinkBuilder(
          'Đến hạn thanh toán TKTS - Zalopay',
          'Vui lòng thanh toán trước 20:00 - ngày 4 hàng tháng. Truy cập Tài Khoản Trả Sau trên Zalopay để kiểm tra dư nợ. Vui lòng bỏ qua nếu đã thanh toán.',
        )
          .setStartTime(4, 20)
          .setEndTime(4, 21)
          .setAlarmBeforeInMinutes(120)
          .setUrl(`${BNPL_APP_REDIRECT}?utm_campaign=${UtmCampaign.Repayment}`)
          .setRecurrence(RecurrenceType.MONTHLY)
          .build();
        //Workaround for iOS
        setTimeout(() => {
          launchDeepLink(deeplink, true);
        }, 1000);
        break;
      case TodoTaskType.ZalopPayOAFollow:
        const zalopayOAUrl = getConfig('zalo.zalopay_oa_url') || ZLP_OA;
        if (Platform.OS === 'web') {
          window?.ZPI_SPA_SDK?.navigateTo?.(zalopayOAUrl);
          return;
        }
        Linking.openURL(zalopayOAUrl);
        break;
    }
  };

  return (
    <View style={styles.root}>
      <View style={styles.imageWrapper}>
        <AppImage style={styles.image} resizeMode={'cover'} width={'100%'} height={125} source={images.ImageRepayTip} />
      </View>
      <TodoList
        expandable={false}
        title={'Mẹo tránh trễ hạn'}
        initExpand={true}
        onToDoTaskTriggered={handleToDoTaskTriggered}
      />
      <BottomButton
        onPress={() => {
          RepaymentTracking.trackTodoListModalCtaClick('understood');
          InfoModalService.hideModal();
        }}
        style={styles.bottomButton}
        title="Đã hiểu"
      />
    </View>
  );
};

const styles = StyleSheet.create({
  root: {
    backgroundColor: AppColors.background,
  },
  imageWrapper: {
    ...StyleUtils.centered,
    overflow: 'hidden',
    borderRadius: 8,
    marginHorizontal: 16,
  },
  image: { overflow: 'hidden', borderRadius: 8 },
  bottomButton: {
    marginTop: 8,
    marginHorizontal: 16,
  },
});

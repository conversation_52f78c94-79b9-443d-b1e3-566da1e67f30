import React, { memo } from 'react';
import deepEqual from 'deep-equal';
import { View } from 'react-native';
import { StyleSheet } from 'bnpl-shared/src/shared/styles/StyleSheet';
import { TodoTaskElement } from 'bnpl-shared/src/types/TodoTypes';
import { AppImage, AppText } from 'bnpl-shared/src/shared/react-native-customized';
import { TodoInteract } from './TodoInteract';
import { Spacer } from 'bnpl-shared/src/components/Spacer';

export type TodoTaskProps = TodoTaskElement;

export const TodoTask: React.FC<TodoTaskProps> = memo(({ title, type, state, iconImg, onAction }) => {
  return (
    <View testID={type} style={[styles.root]}>
      {iconImg && <AppImage resizeMode="cover" width={36} height={36} source={iconImg} style={styles.icon} />}
      <AppText size={14} style={styles.title}>
        {title}
      </AppText>
      <Spacer width={12} />
      <TodoInteract state={state} onAction={onAction} />
    </View>
  );
}, deepEqual);

const styles = StyleSheet.create({
  root: {
    flexDirection: 'row',
    alignItems: 'center',
    height: 48,
    borderRadius: 8,
    marginTop: 12,
  },
  icon: {
    width: 36,
    height: 36,
    marginRight: 12,
  },
  title: {
    flex: 1,
  },
});

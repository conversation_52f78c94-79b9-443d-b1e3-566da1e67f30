import React, { FC, useEffect, useMemo, useState } from 'react';
import { Platform, View } from 'react-native';
import { Colors } from 'bnpl-shared/src/shared/styles/StyleUtils';
import { StyleSheet } from 'bnpl-shared/src/shared/styles/StyleSheet';
import { AppButton, AppText } from 'bnpl-shared/src/shared/react-native-customized';
import { getToDoListArray } from './todo.data';
import { TodoTask as TodoTaskComp } from './TodoTask';
import { TodoTaskElement, TodoTaskState, TodoTaskType } from 'bnpl-shared/src/types/TodoTypes';
import { NativeFeature } from 'bnpl-shared/src/types';
import { checkNativeFeatureAvailable } from 'bnpl-shared/src/utils';
import { trackingToDoListItemClick, trackingToDoListViewMoreClick } from 'bnpl-shared/src/screens/HomeScreen/tracking';

export type TodoListProps = {
  initExpand?: boolean;
  onToDoTaskTriggered?: (actionType: TodoTaskType) => void;
  expandable: boolean;
  title?: string;
};

const TASK_ITEMS_TO_SHOW = 3;

export const TodoList: FC<TodoListProps> = ({
  title = 'Khám phá tính năng mới',
  expandable,
  initExpand = false,
  onToDoTaskTriggered,
}) => {
  const todoListFilter = (todoListArray: TodoTaskElement[]): TodoTaskElement[] => {
    //Currently we only support repayment reminder for ZPA version >= 8.26,
    //so we need to remove it from list data
    if (Platform.OS !== 'web' && !checkNativeFeatureAvailable(NativeFeature.CalendarSDK)) {
      const reminderItemIndex = todoListArray.findIndex(item => item.type === TodoTaskType.RepaymentReminder);
      if (reminderItemIndex !== -1) {
        todoListArray.splice(reminderItemIndex, 1);
      }
    }
    return todoListArray;
  };

  const todoListFilteredData = getToDoListArray(todoListFilter);

  const initTodoTaskState = () => {
    return todoListFilteredData.reduce((result, item) => {
      result[item.type] = item.state || 'idle';
      return result;
    }, {} as Record<TodoTaskType, TodoTaskState>);
  };

  const [expand, setExpand] = useState(Boolean(initExpand));
  const [todoState] = useState<Record<TodoTaskType, TodoTaskState>>(initTodoTaskState());

  const getStateMap: Record<TodoTaskType, (...args: any) => void> = useMemo(
    () => ({
      [TodoTaskType.RepaymentReminder]: () => {},
      [TodoTaskType.ZalopPayOAFollow]: () => {},
      // [TodoTaskType.UtilityExperience]: () => {},
      // [TodoTaskType.FirstPurchase]: () => {},
      // [TodoTaskType.FirstRepayment]: () => {},
      [TodoTaskType.UseAutoRepayment]: () => {},
    }),
    [],
  );

  useEffect(() => {
    todoListFilteredData.forEach(item => {
      getStateMap[item.type]?.();
    });
  }, [getStateMap]);

  const expandBtnTitle = expand ? 'Thu gọn' : 'Xem thêm';
  const numberOfTodoTask = expandable
    ? todoListFilteredData.length
    : expand
    ? todoListFilteredData.length
    : TASK_ITEMS_TO_SHOW;

  return (
    <View testID={'todo-list-widget'} style={styles.root}>
      <AppText size={16} height={20} bold color={Colors.text}>
        {title}
      </AppText>
      <View>
        {todoListFilteredData.slice(0, numberOfTodoTask).map((item, index) => (
          <TodoTaskComp
            key={index}
            {...item}
            state={todoState[item.type]}
            onAction={() => {
              trackingToDoListItemClick(item.type);
              onToDoTaskTriggered?.(item.type);
            }}
          />
        ))}
      </View>
      {expandable && (
        <View style={styles.expandSection}>
          <AppButton
            variant="naked"
            title={expandBtnTitle}
            buttonStyle={styles.expandBtn}
            titleStyle={styles.expandTitle}
            onPress={() => {
              trackingToDoListViewMoreClick();
              setExpand(!expand);
            }}
          />
        </View>
      )}
    </View>
  );
};

const styles = StyleSheet.create({
  root: {
    padding: 16,
    borderRadius: 8,
  },
  expandSection: {
    alignItems: 'center',
    marginTop: 12,
  },
  expandBtn: {
    height: 'auto',
  },
  expandTitle: {
    fontSize: 14,
    fontWeight: '400',
  },
});

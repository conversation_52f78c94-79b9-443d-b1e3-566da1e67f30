import { images } from 'bnpl-shared/src/res';
import { TodoTaskElement, TodoTaskType } from 'bnpl-shared/src/types/TodoTypes';

export const todoListData: Record<TodoTaskType, TodoTaskElement> = {
  [TodoTaskType.RepaymentReminder]: {
    title: 'Đặt lịch nhắc nhở thanh toán dư nợ',
    type: TodoTaskType.RepaymentReminder,
    state: 'idle',
    iconImg: images.IconCalendar,
  },
  [TodoTaskType.ZalopPayOAFollow]: {
    title: 'Quan tâm Zalopay trên Zalo ngay để nhận thông báo dư nợ',
    type: TodoTaskType.ZalopPayOAFollow,
    state: 'idle',
    iconImg: images.IconZlp,
  },
  // [TodoTaskType.UtilityExperience]: {
  //   title: 'Trải nghiệm tiện ích',
  //   type: TodoTaskType.UtilityExperience,
  //   state: 'idle',
  //   iconImg: images.IconStar,
  // },
  // [TodoTaskType.FirstPurchase]: {
  //   title: '<PERSON><PERSON> sắm bằng tài khoản trả sau lần đầu',
  //   type: TodoTaskType.FirstPurchase,
  //   state: 'idle',
  //   iconImg: images.IconThunder,
  // },
  // [TodoTaskType.FirstRepayment]: {
  //   title: 'Thanh toán dư nợ lần đầu',
  //   type: TodoTaskType.FirstRepayment,
  //   state: 'idle',
  //   iconImg: images.IconMoneyBag,
  // },
  [TodoTaskType.UseAutoRepayment]: {
    title: 'Bật thanh toán dư nợ tự động',
    type: TodoTaskType.UseAutoRepayment,
    state: 'idle',
    iconImg: images.IconAutoRepayment,
  },
};

export function getToDoListArray(filter?: (data: TodoTaskElement[]) => TodoTaskElement[]) {
  if (filter) {
    return filter(Object.values(todoListData));
  }
  return Object.values(todoListData);
}

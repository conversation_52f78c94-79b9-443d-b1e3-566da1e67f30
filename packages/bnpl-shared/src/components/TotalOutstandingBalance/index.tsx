import React, { FC } from 'react';
import { useUserBalanceController } from 'bnpl-shared/src/utils/useUserBalanceController';
import { formatCurrency } from 'bnpl-shared/src/shared/utils/formatCurrency';
import { View } from 'react-native';
import { AppImage, AppText } from 'bnpl-shared/src/shared/react-native-customized';
import { AppColors } from 'bnpl-shared/src/constants';
import { images } from 'bnpl-shared/src/res';
import { Spacer } from 'bnpl-shared/src/components/Spacer';
import { StyleSheet } from 'bnpl-shared/src/shared/styles/StyleSheet';

export const TotalOutstandingBalance: FC = () => {
  const { balanceSummary } = useUserBalanceController();
  const userOB = balanceSummary.data?.outstanding_balance;
  const totalOutstandingAmountFormatted = formatCurrency(userOB || 0);
  return (
    <View testID={'stm-balance'} style={styles.balanceRoot}>
      <AppImage tintColor={AppColors.white} width={16} height={16} source={images.IconDollarYellowCircle} />
      <Spacer width={4} />
      <AppText size={16} color={AppColors.white}>
        Tổng dư nợ:{' '}
        <AppText color={AppColors.white} size={16} bold>
          {totalOutstandingAmountFormatted}
        </AppText>
      </AppText>
    </View>
  );
};

const styles = StyleSheet.create({
  balanceRoot: { flexDirection: 'row', alignItems: 'center', height: 56, paddingHorizontal: 16 },
});

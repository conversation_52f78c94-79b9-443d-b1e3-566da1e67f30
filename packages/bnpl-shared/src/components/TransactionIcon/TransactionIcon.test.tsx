import React from 'react';
import { render } from '@testing-library/react-native';
import { TransactionIcon } from './TransactionIcon';
import { TransactionType } from 'bnpl-shared/src/types';

describe(TransactionIcon.name, () => {
  it('renders icon given the transaction type', () => {
    render(<TransactionIcon type={TransactionType.REPAYMENT} size={36} />);
    render(<TransactionIcon type={TransactionType.PAYMENT} size={36} />);
  });
});

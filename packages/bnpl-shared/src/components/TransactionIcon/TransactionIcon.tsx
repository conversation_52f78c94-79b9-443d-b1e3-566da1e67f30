import React, { FC } from 'react';
import { ImageStyle } from 'react-native';
import { TransactionType } from 'bnpl-shared/src/types';
import { AppImage } from 'bnpl-shared/src/shared/react-native-customized';
import { size as getSize } from 'bnpl-shared/src/shared/styles/StyleUtils';
import { images } from 'bnpl-shared/src/res';

export const TransactionIcon: FC<{ type: TransactionType; size: number; style?: ImageStyle }> = ({
  type,
  size = 16,
  style,
}) => {
  switch (type) {
    case TransactionType.FEE_SERVICE:
      return <AppImage source={images.IconTransFeeUseCircle} {...getSize(size)} style={style} />;
    case TransactionType.REPAYMENT:
      return <AppImage source={images.IconTransRepayment} {...getSize(size)} style={style} />;
    case TransactionType.FEE_LATE:
      return <AppImage source={images.IconTransFeePaidLateCircle} {...getSize(size)} style={style} />;
    default:
    case TransactionType.PAYMENT:
      return <AppImage source={images.IconTransPurchase} {...getSize(size)} style={style} />;
    case TransactionType.REFUND_FOR_PAYMENT:
    case TransactionType.REVERT_FOR_PAYMENT:
      return <AppImage source={images.IconTransRefund} {...getSize(size)} style={style} />;
    case TransactionType.REFUND_FOR_REPAYMENT:
      return <AppImage source={images.IconTransRepayment} {...getSize(size)} style={style} />;
  }
};

import React from 'react';
import { AppColors } from 'bnpl-shared/src/constants';
import { AppText } from 'bnpl-shared/src/shared/react-native-customized';
import { StyleSheet } from 'bnpl-shared/src/shared/styles/StyleSheet';
import { View } from 'react-native';

export default function Badge({ children }: BadgeProps) {
  return (
    <View style={styles.container}>
      <AppText size={12} height={16} color={AppColors.green[700]}>{children}</AppText>
    </View>
  );
}

interface BadgeProps {
  children: React.ReactNode;
}

const styles = StyleSheet.create({
  container: {
    backgroundColor: AppColors.green[50],
    height: 20,
    alignItems: 'center',
    justifyContent: 'center',
    paddingHorizontal: 8,
    borderRadius: 4,
  },
});

import React from 'react';
import { render } from '@testing-library/react-native';
import { Transaction, TransactionStatus, TransactionType } from 'bnpl-shared/src/types';
import { TransactionItem } from './TransactionItem';

const transaction: Transaction = {
  id: '0',
  type: TransactionType.PAYMENT,
  status: TransactionStatus.PROCESSING,
  amount: 0,
  description: 'txn description',
  created_at: '2021-07-10 12:11:00.000',
  updated_at: '2021-07-11 12:11:00.000',
};

it('can render', () => {
  const { queryByTestId } = render(<TransactionItem item={transaction} />);
  expect(queryByTestId('info-banner')).toBeFalsy();
});

it('render info banner only with status === INIT/PROCESSING && type === REPAYMENT ', () => {
  const { queryByTestId, rerender } = render(
    <TransactionItem
      showInfoBanner={true}
      item={{
        id: '0',
        type: TransactionType.REPAYMENT,
        status: TransactionStatus.PROCESSING,
        amount: 0,
        description: 'txn description',
        created_at: '2021-07-10 12:11:00.000',
        updated_at: '2021-07-11 12:11:00.000',
      }}
    />,
  );
  expect(queryByTestId('info-banner')).toBeTruthy();

  rerender(
    <TransactionItem
      showInfoBanner={true}
      item={{
        id: '0',
        type: TransactionType.REPAYMENT,
        status: TransactionStatus.INIT,
        amount: 0,
        description: 'txn description',
        created_at: '2021-07-10 12:11:00.000',
        updated_at: '2021-07-11 12:11:00.000',
      }}
    />,
  );
  expect(queryByTestId('info-banner')).toBeTruthy();
});

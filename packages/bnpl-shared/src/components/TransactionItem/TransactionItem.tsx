//#region
import React, { FC } from 'react';
import { View } from 'react-native';
import { TransactionIcon } from '../TransactionIcon';
import { TransactionStatusText } from '../TransactionStatusText';
import { Spacer } from '../Spacer';
import { Transaction, TransactionStatus, TransactionType } from 'bnpl-shared/src/types';
import { AppImage, AppText } from 'bnpl-shared/src/shared/react-native-customized';
import { StyleSheet } from 'bnpl-shared/src/shared/styles/StyleSheet';
import { TwoSideView } from 'bnpl-shared/src/shared/TwoSideView/TwoSideView';
import { formatCurrencyWithSign } from 'bnpl-shared/src/shared/utils/formatCurrencyWithSign';
import { formatAsReadableDateTime } from 'bnpl-shared/src/shared/utils/formatAsReadableDateTime';
import { Colors, size as getSize, StyleUtils } from 'bnpl-shared/src/shared/styles/StyleUtils';
import { AppColors } from 'bnpl-shared/src/constants';
import { images } from 'bnpl-shared/src/res';
import Badge from './Badge';
//#endregion

type Props = {
  item: Transaction;
  showInfoBanner?: boolean;
};

export const TransactionItem: FC<Props> = ({ item, showInfoBanner = false }) => {
  const shouldShowInfoBanner =
    showInfoBanner &&
    [
      TransactionStatus.INIT,
      TransactionStatus.PROCESSING,
      TransactionStatus.PENDING,
      TransactionStatus.FAILED,
    ].includes(item.status) &&
    item.type === TransactionType.REPAYMENT;

  const convertStatus = (status: TransactionStatus) => {
    if(item.status === TransactionStatus.FAILED) {
      return TransactionStatus.PROCESSING;
    }
    return status;
  };
  const transformStatus = item.type === TransactionType.REPAYMENT ? convertStatus(item.status) : item.status;

  return (
    <View style={styles.root}>
      <View style={styles.contentContainer}>
        <View style={styles.iconContainer}>
          <TransactionIcon type={item.type} size={36} />
        </View>
        <View style={styles.textContainer}>
          <TwoSideView
            left={
              <View style={styles.leftContainer}>
                <AppText numberOfLines={1}>{item.description}</AppText>
                {item?.metadata?.label && (
                  <>
                    <Spacer width={4} />
                    <Badge>
                      {item?.metadata?.label}
                    </Badge>
                  </>
                )}
              </View>
            }
            right={<AppText bold>{formatAmountByType(item.amount, item.type)}</AppText>}
          />
          <Spacer height={4} />
          <TwoSideView
            left={<AppText color={Colors.text2}>{formatAsReadableDateTime(item.created_at)}</AppText>}
            right={<TransactionStatusText status={transformStatus} />}
          />
        </View>
      </View>
      {shouldShowInfoBanner && (
        <View testID={'info-banner'} style={[StyleUtils.flexRow, styles.bannerContainer]}>
          <AppImage {...getSize(20)} tintColor={AppColors.warning2} source={images.IconInfoICircleNoFill} />
          <Spacer width={10} />
          <AppText style={StyleUtils.flexOne}>Giao dịch đang chờ xác nhận thanh toán từ đối tác</AppText>
        </View>
      )}
    </View>
  );
};

//#region
const styles = StyleSheet.create({
  root: {
    borderColor: Colors.disabledBackground,
    borderWidth: 1,
    borderRadius: 8,
  },
  iconContainer: {
    justifyContent: 'center',
    paddingRight: 8,
  },
  textContainer: {
    flex: 1,
  },
  bannerContainer: {
    height: 56,
    backgroundColor: AppColors.warning,
    paddingVertical: 10,
    paddingHorizontal: 16,
  },
  leftContainer: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  contentContainer: { padding: 16, flexDirection: 'row', alignItems: 'stretch', justifyContent: 'center' },
});

export const formatAmountByType = (amount: number, type: TransactionType) =>
  formatCurrencyWithSign(
    type === TransactionType.PAYMENT || type === TransactionType.FEE_SERVICE || type === TransactionType.FEE_LATE
      ? -Math.abs(amount)
      : amount,
  );
//#endregion

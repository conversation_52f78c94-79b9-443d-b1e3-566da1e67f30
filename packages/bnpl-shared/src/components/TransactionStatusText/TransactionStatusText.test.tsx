import React from 'react';
import { render } from '@testing-library/react-native';
import { TransactionStatusText } from './TransactionStatusText';
import { TransactionStatus } from 'bnpl-shared/src/types';

describe(TransactionStatusText.name, () => {
  describe('Given long mode', () => {
    it('renders "Giao dịch thành công" if status is "succeeded"', () => {
      const { queryByText } = render(<TransactionStatusText mode="long" status={TransactionStatus.SUCCEEDED} />);
      expect(queryByText('Giao dịch thành công')).toBeTruthy();
    });

    it('renders "Giao dịch thất bại" if status is "failed"', () => {
      const { queryByText } = render(<TransactionStatusText mode="long" status={TransactionStatus.FAILED} />);
      expect(queryByText('Giao dịch thất bại')).toBeTruthy();
    });

    it('renders "Giao dịch đang xử lý" if status is "pending"', () => {
      const { queryByText } = render(<TransactionStatusText mode="long" status={TransactionStatus.PROCESSING} />);
      expect(queryByText('Giao dịch đang xử lý')).toBeTruthy();
    });
  });

  describe('Given short mode', () => {
    it('renders "Thành công" if status is "succeeded"', () => {
      const { queryByText } = render(<TransactionStatusText mode="short" status={TransactionStatus.SUCCEEDED} />);
      expect(queryByText('Thành công')).toBeTruthy();
    });

    it('renders "Thất bại" if status is "failed"', () => {
      const { queryByText } = render(<TransactionStatusText mode="short" status={TransactionStatus.FAILED} />);
      expect(queryByText('Thất bại')).toBeTruthy();
    });

    it('renders "Đang xử lý" if status is "pending"', () => {
      const { queryByText } = render(<TransactionStatusText mode="short" status={TransactionStatus.PROCESSING} />);
      expect(queryByText('Đang xử lý')).toBeTruthy();
    });
  });
});

import React, { FC } from 'react';
import { AppColors } from 'bnpl-shared/src/constants';
import { TransactionStatus } from 'bnpl-shared/src/types';
import { AppText, AppTextProps } from 'bnpl-shared/src/shared/react-native-customized';

export const TransactionStatusText: FC<
  {
    status: TransactionStatus;
    mode?: 'long' | 'short';
  } & AppTextProps
> = ({ status, mode = 'short', ...others }) => {
  const composeText = (text: string) => {
    const newText: string = `${mode === 'long' ? 'giao dịch ' : ''}${text}`;
    return newText.charAt(0).toLocaleUpperCase('vi-VN') + newText.slice(1);
  };

  switch (status) {
    case TransactionStatus.FAILED:
      return (
        <AppText {...others} color={AppColors.transactionFailed}>
          {composeText('thất bại')}
        </AppText>
      );
    case TransactionStatus.SUCCEEDED:
      return (
        <AppText {...others} color={AppColors.expensePieArc}>
          {composeText('thành công')}
        </AppText>
      );
    default:
      return (
        <AppText {...others} color={AppColors.transactionPending}>
          {composeText('đang xử lý')}
        </AppText>
      );
  }
};

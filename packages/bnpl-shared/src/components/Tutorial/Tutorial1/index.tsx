import { StyleSheet } from 'bnpl-shared/src/shared/styles/StyleSheet';
import { SwiperComponent } from 'bnpl-shared/src/shared/swiper';
import React, { FC } from 'react';
import { View } from 'react-native';
import { Slide } from '../Slide';
import { images } from 'bnpl-shared/src/res';

const Tutorial1: FC<{ onActiveIndexChange?: (index: number) => void }> = ({ onActiveIndexChange }) => {
  return (
    <View style={styles.root}>
      <SwiperComponent
        onActiveIndexChange={onActiveIndexChange}
        suppressTitleWarning
        showsPagination
        scrollEnabled
        loop>
        <Slide artwork={images.Tutorial1_1} title="Giới thiệu sản phẩm" subtitle="Nhấn “Đăng ký ngay” để tiếp tục" />
        <Slide
          artwork={images.Tutorial1_2}
          title="Đăng ký thông tin"
          subtitle="Nhập thông tin, nhấn “<PERSON><PERSON><PERSON> hồ sơ” để tiếp tục"
        />
        <Slide
          artwork={images.Tutorial1_3}
          title="Xác thực gương mặt"
          subtitle="Chụp ảnh gương mặt của bạn để xác thực chính chủ"
        />
        <Slide artwork={images.Tutorial1_4} title="Xác thực OTP" subtitle="Xác thực OTP và chờ duyệt" />
        <Slide
          artwork={images.Tutorial1_5}
          title="Thông báo được duyệt"
          subtitle="Từ giờ, bạn có thể dùng TK Trả Sau để mua sắm"
        />
      </SwiperComponent>
    </View>
  );
};

export default Tutorial1;

const styles = StyleSheet.create({
  root: {
    height: 450,
  },
});

import { StyleSheet } from 'bnpl-shared/src/shared/styles/StyleSheet';
import { SwiperComponent } from 'bnpl-shared/src/shared/swiper';
import React, { FC } from 'react';
import { View } from 'react-native';
import { Slide } from '../Slide';
import { images } from 'bnpl-shared/src/res';

const Tutorial2: FC<{ onActiveIndexChange?: (index: number) => void }> = ({ onActiveIndexChange }) => {
  return (
    <View style={styles.root}>
      <SwiperComponent
        onActiveIndexChange={onActiveIndexChange}
        suppressTitleWarning
        showsPagination
        scrollEnabled
        loop>
        <Slide
          artwork={images.Tutorial2_1}
          title="Thanh toán bằng Tài Khoản Trả Sau"
          subtitle="Màn xác nhận giao dịch, chọn thay đổi “Nguồn tiền”"
        />
        <Slide artwork={images.Tutorial2_2} title="Chọn nguồn tiền" subtitle="Chọn Tài <PERSON>ản T<PERSON>ả Sau" />
        <Slide
          artwork={images.Tutorial2_3}
          title="Thanh toán bằng Tà<PERSON>ho<PERSON>n Trả Sau"
          subtitle="Chọn “Xác nhận giao dịch” để thanh toán"
        />
      </SwiperComponent>
    </View>
  );
};

export default Tutorial2;

const styles = StyleSheet.create({
  root: {
    height: 450,
  },
});

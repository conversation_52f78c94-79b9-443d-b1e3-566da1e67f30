import { StyleSheet } from 'bnpl-shared/src/shared/styles/StyleSheet';
import { SwiperComponent } from 'bnpl-shared/src/shared/swiper';
import React, { FC } from 'react';
import { View } from 'react-native';
import { Slide } from '../Slide';
import { images } from 'bnpl-shared/src/res';

const Tutorial3: FC<{ onActiveIndexChange?: (index: number) => void }> = ({ onActiveIndexChange }) => {
  return (
    <View style={styles.root}>
      <SwiperComponent
        onActiveIndexChange={onActiveIndexChange}
        suppressTitleWarning
        showsPagination
        scrollEnabled
        loop>
        <Slide artwork={images.Tutorial3_1} title="Thông báo dư nợ" subtitle="Nhấn vào thông báo để xem dư nợ" />
        <Slide artwork={images.Tutorial3_2} title="Chọn số tiền" subtitle="Chọn/nhập số tiền và nhấn thanh toán" />
        <Slide
          artwork={images.Tutorial3_3}
          title="Xác nhận thanh toán"
          subtitle="Chọn nguồn tiền và xác nhận thanh toán"
        />
        <Slide artwork={images.Tutorial3_4} title="Giao dịch thành công" subtitle="Hoàn tất thanh toán dư nợ." />
      </SwiperComponent>
    </View>
  );
};

export default Tutorial3;

const styles = StyleSheet.create({
  root: {
    height: 450,
  },
});

import { StyleSheet } from 'bnpl-shared/src/shared/styles/StyleSheet';
import { SwiperComponent } from 'bnpl-shared/src/shared/swiper';
import React, { FC } from 'react';
import { View } from 'react-native';
import { Slide } from '../Slide';
import { images } from 'bnpl-shared/src/res';

const Tutorial4: FC<{ onActiveIndexChange?: (index: number) => void }> = ({ onActiveIndexChange }) => {
  return (
    <View style={styles.root}>
      <SwiperComponent
        onActiveIndexChange={onActiveIndexChange}
        suppressTitleWarning
        showsPagination
        scrollEnabled
        loop>
        <Slide artwork={images.Tutorial4_1} title="Trang chủ" subtitle="Xem số dư, thông báo, khuyến mãi,..." />
        <Slide artwork={images.Tutorial4_2} title="<PERSON><PERSON> nợ" subtitle="Xem tổng dư nợ, dư nợ đến hạn, sao kê,..." />
        <Slide artwork={images.Tutorial4_3} title="Lịch sử" subtitle="Xem danh sách/chi tiết lịch sử mua sắm/trả nợ" />
        <Slide artwork={images.Tutorial4_4} title="Cá nhân" subtitle="Xem thông tin tài khoản, sản phẩm, cài đặt,..." />
      </SwiperComponent>
    </View>
  );
};

export default Tutorial4;

const styles = StyleSheet.create({
  root: {
    height: 450,
  },
});

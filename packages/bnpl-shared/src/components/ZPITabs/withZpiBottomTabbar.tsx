import React, { createContext, useContext, useState } from 'react';
import { Platform, View } from 'react-native';
import { AppColors, BOTTOM_TAB_HEIGHT, TAB_BAR_CONFIG } from 'bnpl-shared/src/constants';
import ZpiTabbar from 'bnpl-shared/src/components/ZPITabs/ZpiTabbar';
import { TabItemConfig } from 'bnpl-shared/src/types';
import { calculateScaleDimension, StyleSheet } from 'bnpl-shared/src/shared/styles/StyleSheet';
import { getBottomSafe } from 'bnpl-shared/src/utils';

// Context for controlling the tab bar visibility
const ZpiTabbarContext = createContext({
  isTabbarVisible: true,
  setTabbarVisible: (_: boolean) => { },
});

// Hook for consuming the tab bar context
export const useZpiTabbar = () => useContext(ZpiTabbarContext);

export const withZpiBottomTabbar = (
  WrappedComponent: React.ComponentType<any>,
  tabbarConfigs: TabItemConfig[] = TAB_BAR_CONFIG,
) => {
  if (Platform.OS !== 'web') {
    return WrappedComponent;
  }

  const displayName = WrappedComponent.displayName || WrappedComponent.name || 'Component';

  const ComponentWithTabBar = (props: any) => {
    const [isTabbarVisible, setTabbarVisible] = useState(true);
    const activeScreen = props.location.pathname.slice(1);
    return (
      <ZpiTabbarContext.Provider value={{ isTabbarVisible, setTabbarVisible }}>
        {isTabbarVisible ? (
          <div style={{ overflow: 'hidden', height: '100%' }}>
            <View style={[styles.fullScreenHeight]}>
              <View style={styles.screenWrapper}>
                <WrappedComponent {...props} />
              </View>
              <View testID="main-bottom-tab" style={styles.tabbar}>
                <ZpiTabbar indicator={false} active={activeScreen} config={tabbarConfigs} />
              </View>
            </View>
          </div>
        ) : (
          <WrappedComponent {...props} />
        )}
      </ZpiTabbarContext.Provider>
    );
  };

  ComponentWithTabBar.displayName = `withTabBar(${displayName})`;

  return ComponentWithTabBar;
};

const styles = StyleSheet.create({
  fullScreenHeight: {
    position: 'relative',
    height: '100%',
    paddingBottom: calculateScaleDimension(BOTTOM_TAB_HEIGHT + getBottomSafe()),
  },
  screenWrapper: { flex: 1 },
  tabbar: {
    height: BOTTOM_TAB_HEIGHT + getBottomSafe(),
    position: 'absolute',
    left: 0,
    right: 0,
    bottom: getBottomSafe(),
    backgroundColor: AppColors.background,
    borderTopWidth: 1,
    borderTopColor: AppColors.primary2,
    justifyContent: 'center',
  },
});

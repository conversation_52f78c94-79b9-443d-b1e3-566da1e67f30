import { buildColors } from '../shared/styles/StyleUtils';
import { TabItemConfig } from 'bnpl-shared/src/types';
import { local_images } from 'bnpl-shared/src/res/images/local';
export * from './MultiPartner';

export const VIETQR_LIMIT_AMOUNT = 10000;
export const CURRENCY = 'đ';

export const FORM_DATA_BOUNDARY = 'bnpl';

export const BOTTOM_TAB_HEIGHT = 64;

export const INVALID_ACCOUNT_ID = '0';

export enum AccountStatus {
  ACTIVE = 'ACTIVE',
  INACTIVE = 'INACTIVE',
  LOCKED = 'LOCKED',
}

export enum RepayMethodId {
  ZaloPay = 'zalopay',
  VietQR = 'vietqr',
}

export enum SurveyId {
  FEEDBACK = 'paylater-feedback',
  RENEW = 'paylater_renew',
  RENEW_SIGN_CONTRACT = 'paylater_renew_sign_contract',
  TERMINATE_ACCOUNT = 'paylater_terminate_account',
  AUTO_REPAY_TURN_OFF = 'paylater_auto_repay_turn_off',
}

export enum PartnerCode {
  CIMB = 'CIMB',
  LOTTE = 'LFVN',
  MINI_BNPL = 'MINI_BNPL',
}

export enum UtilityConfig {
  AUTO_REPAY = 'auto_repayment',
  PRIORITY_SOF = 'pay_later_top_priority',
}

export enum ConfigKey {
  PRIORITY_SOF_TIMESTAMP = 'priority_sof_timestamp',
  ADS_POPUP_TIMESTAMP = 'ads_popup_timestamp',
  BOTTOM_NAV_HIGHLIGHT_TIMESTAMP = 'bottom_nav_highlight_timestamp',
}

export enum Utility {
  SetDefaultSourceOfFund,
  AutoRepayment,
  Statement,
  Notification,
  Reminder,
  LockAccount,
  UnlockAccount,
  TerminateAccount,
  FAQ,
  UserGuide,
  ZaloPaySupport,
  ViewContract,
  ProductInfo,
  ManageListSOF,
  ReminderSetting,
}

export enum ScreenKey {
  OldVersionLockOutScreen = 'OldVersionLockOutScreen',
  LockOutScreen = 'LockOutScreen',
  AddInformationScreen = 'AddInformationScreen',
  SplashScreen = 'SplashScreen',
  HomeScreen = 'HomeScreen',
  HomeUnbindScreen = 'HomeUnbindScreen',
  OnboardingScreen = 'OnboardingScreen',
  WebviewScreen = 'WebviewScreen',
  ApplicationReview = 'ApplicationReview',
  Faq = 'Faq',
  ProductInfo = 'ProductInfo',
  TransactionHistory = 'TransactionHistory',
  PaymentHistory = 'PaymentHistory',
  RepaymentHistory = 'RepaymentHistory',
  TransactionDetail = 'TransactionDetail',
  UserGuide = 'UserGuide',
  PersonalExtraDataScreen = 'PersonalExtraDataScreen',
  Maintenance = 'MaintenanceScreen',
  Repayment = 'Repayment',
  ResubmitOnboarding = 'ResubmitOnboarding',
  Notification = 'Notification',
  Error = 'Error',
  RenewFaceChallenge = 'RenewFaceChallenge',
  MainScreen = 'MainScreen',
  AccountScreen = 'AccountScreen',
  StatementDetailScreen = 'StatementDetailScreen',
  PartnerPickScreen = 'PartnerPickScreen',
  NonWhitelistOnboardingScreen = 'NonWhitelistOnboardingScreen',
  MPSubmitDetailInfoScreen = 'MultiPartnerSubmitDetailInfoScreen',
  MPOnboardingScreen = 'MultiPartnerOnboardingScreen',
  MPVerifyOTPScreen = 'MultiPartnerVerifyOTPScreen',
  MiniRepaymentHistory = 'MiniPartnerRepaymentHistoryScreen',
  MPMainScreen = 'MultiPartnerMainScreen',
  MPHomeScreen = 'MultiPartnerHomeScreen',
  MPRepayScreen = 'MultiPartnerRepayScreen',
  MPResubmitScreen = 'MultiPartnerResubmitScreen',
  MPAccountScreen = 'MultiPartnerAccountScreen',
  MPPaymentHistory = 'MultiPartnerPaymentHistory',
  MPRepaymentHistory = 'MultiPartnerRepaymentHistory',
  MPTransactionHistory = 'MultiPartnerTransactionHistory',
  ReminderSettingScreen = 'ReminderSettingScreen',
  MPWaitingApproval = 'MultiPartnerWaitingApproval',
  EmbeddedRenewalInfoScreen = 'EmbeddedRenewalInfoScreen',
  EmbeddedRenewSignScreen = 'EmbeddedRenewSignScreen',
  EmbeddedRenewFaceAuthenScreen = 'EmbeddedRenewFaceAuthenScreen',
}

export const AppColors = buildColors({
  // Home screen colors
  textPaymentActive: '#3587FB',
  textPaymentExpired: '#E94563',
  textWidgetPaymentTerm: '#FFFFFF',
  expenseLimitStart: '#3AA9D9',
  expenseLimitEnd1: '#F6FA48',
  expenseLimitEnd2: '#8148FA',
  expenseLimitEnd3: '#FA4848',
  transactionSucceeded: '#02A25F',
  transactionFailed: '#E3173C',
  transactionPending: '#FF8D00',
  expensePieArc: '#00CF6A',
  expensePieCircle: '#3573E7',
  expensePieStroke: '#CCE1FF',

  blue: {
    1: '#CCE1FF',
    25: '#E7F6FF',
    50: '#D3EEFF',
    200: '#81C4FF',
    500: '#0033C9',
  },

  green: {
    1: '#027947',
    2: '#E6FAF1',
    3: '#53B05A',
    4: '#00CF6A',
    5: '#EEFFF5',
    50: '#EEFFF5',
    200: '#B2FFD8',
    500: '#03CA77',
    700: '#007C40',
  },

  red: {
    1: '#D61D4B',
    2: '#FCE8EC',
    3: '#880E24',
    4: '#E3173C',
    5: '#FEF3F5',
    6: '#E94563',
    7: '#F9D1d8',
    50: '#FCE8EC',
    200: '#F4A2B1',
    500: '#E3173C',
    700: '#880E24',
  },

  orange: {
    1: '#FFFBF4',
    2: '#966518',
    50: '#FFF6D3',
    200: '#FFD96D',
    500: '#FF8D00',
    700: '#995500',
  },

  dark: {
    50: 'rgba(230, 233, 236, 1)',
    200: '#99A5B2',
    300: 'rgba(102, 121, 139, 1)',
    400: '#334C65',
    500: '#001F3E',
  },

  teal: {
    600: '#008ED4',
  },

  //UNNAME colors
  white: '#FFFFFF',
  dark300: '#66798B',
  dark200: '#99A5B2',
  stroke: '#EEF4FE',
  strokeV2: '#F2F6F7',
  blackOpacity: (opacity: number) => `rgba(0,0,0,${opacity})`,
});

export const MULTI_PARTNER_TAB_BAR_CONFIG: TabItemConfig[] = [
  {
    id: 0,
    title: 'Trang chủ',
    path: ScreenKey.MPHomeScreen,
    matchPath: [ScreenKey.MPMainScreen, ScreenKey.MPHomeScreen],
    icon: { src: local_images.IconBottomHomeInactive, srcActive: local_images.IconBottomHomeActive, size: 24 },
  },
  {
    id: 1,
    title: 'Dư nợ',
    path: ScreenKey.MPRepayScreen,
    matchPath: [],
    icon: { src: local_images.IconBottomRepayInactive, srcActive: local_images.IconBottomRepayActive, size: 24 },
  },
  {
    id: 2,
    title: 'Lịch sử',
    path: ScreenKey.MPTransactionHistory,
    matchPath: [ScreenKey.MPPaymentHistory, ScreenKey.MPRepaymentHistory],
    icon: { src: local_images.IconBottomHistoryInactive, srcActive: local_images.IconBottomHistoryActive, size: 24 },
  },
  {
    id: 3,
    title: 'Cá nhân',
    path: ScreenKey.MPAccountScreen,
    matchPath: [],
    icon: { src: local_images.IconBottomProfileInactive, srcActive: local_images.IconBottomProfileActive, size: 24 },
  },
];

export const TAB_BAR_CONFIG: TabItemConfig[] = [
  {
    id: 0,
    title: 'Trang chủ',
    path: ScreenKey.HomeScreen,
    matchPath: [ScreenKey.MainScreen, ScreenKey.HomeScreen],
    icon: { src: local_images.IconBottomHomeInactive, srcActive: local_images.IconBottomHomeActive, size: 24 },
  },
  {
    id: 1,
    title: 'Dư nợ',
    path: ScreenKey.Repayment,
    matchPath: [],
    icon: { src: local_images.IconBottomRepayInactive, srcActive: local_images.IconBottomRepayActive, size: 24 },
  },
  {
    id: 2,
    title: 'Lịch sử',
    path: ScreenKey.TransactionHistory,
    matchPath: [ScreenKey.PaymentHistory, ScreenKey.RepaymentHistory],
    icon: { src: local_images.IconBottomHistoryInactive, srcActive: local_images.IconBottomHistoryActive, size: 24 },
  },
  {
    id: 3,
    title: 'Cá nhân',
    path: ScreenKey.AccountScreen,
    matchPath: [],
    icon: { src: local_images.IconBottomProfileInactive, srcActive: local_images.IconBottomProfileActive, size: 24 },
  },
];

export enum AsyncStorageKey {
  BNPL_DEVTOOL_REPAYMENTSCREEN_CONFIG = 'BNPL_DEVTOOL_REPAYMENTSCREEN_CONFIG',
  AUTO_REPAY_TURN_OFF_SURVEY_LAST_SHOW = 'auto_repay_turn_off_survey_last_show',
}

export enum PLATFORM {
  ZPI = 'ZPI',
  ZPA = 'ZPA',
}

export enum UMStatus {
  Invalid = -1,
  Cancel = 0,
  Success = 1,
  Failure = 2,
  Pending = 3,
}

import React from 'react';
import { View } from 'react-native';
import { FullScreenFormLayout } from 'bnpl-shared/src/components/layouts/FullScreenFormLayout';
import { Spacer } from 'bnpl-shared/src/components/Spacer';
import { AppColo<PERSON>, ScreenKey } from 'bnpl-shared/src/constants';
import { AppButton, AppImage, AppText } from 'bnpl-shared/src/shared/react-native-customized';
import { StyleSheet } from 'bnpl-shared/src/shared/styles/StyleSheet';
import { images } from 'bnpl-shared/src/res';
import { toast } from 'bnpl-shared/src/components/Toaster';
import { hideLoading, showLoading, usePromptAuthChallengeFlow } from 'bnpl-shared/src/shared/ZaloPayModules';
import { useAuthChallengeSource } from 'bnpl-shared/src/hooks/useAuthChallengeSource';
import { AuthChallengeType, UMStatus } from 'bnpl-shared/src/types';
import { postVerifyRenewFaceChallengeApi } from 'bnpl-shared/src/api/renew_overdraft';
import { useAppSelector } from 'bnpl-shared/src/redux/store';
import { useNavigation } from 'bnpl-shared/src/shared/navigation/useNavigation';
import { getBottomSafe } from 'bnpl-shared/src/utils';

export default function EmbeddedRenewFaceAuthenScreen(props: any) {
  const navigation = useNavigation(props);
  const authSource = useAuthChallengeSource();
  const { promptAuthChallengeFlow } = usePromptAuthChallengeFlow();
  const { renewInfo } = useAppSelector(state => state.featureRenewOverDraft);

  async function handleClickCTA() {
    try {
      const result = await promptAuthChallengeFlow(Number(authSource), AuthChallengeType.FaceAuth);
      if (result.status === UMStatus.Success && result.resultData) {
        showLoading();
        postVerifyRenewFaceChallengeApi(renewInfo.data?.appendixId || 0, result.resultData).then((res) => {
          navigation.replace(ScreenKey.MPWaitingApproval);
        }).catch((err) => {
          console.log("verify face challenge error", err);
          toast.error(err?.message || 'Có lỗi xảy ra, vui lòng thử lại sau.', { duration: 3000 });
        });
      }
    } catch (e: any) {
      if (e !== 'failed') {
        toast.error(e?.message || 'Có lỗi xảy ra, vui lòng thử lại sau.', { duration: 3000 });
      }
    } finally {
      hideLoading();
    }
  }
  return (
    <FullScreenFormLayout
      contentStyles={styles.contentWrapper}
      actionsStyles={styles.actionWrapper}
      content={
        <View style={styles.container}>
          <AppImage
            source={images.MiniBNPLFaceChallengeAvatar}
            width={180}
            height={240}
          />
          <Spacer height={32} />
          <AppText size={16} height={24} color={AppColors.dark[500]} bold>Bấm “Xác thực ngay” để ký hợp đồng</AppText>
        </View>
      }
      actions={
        <AppButton title="Xác thực ngay" onPress={handleClickCTA} />
      }
    />
  )
}

const styles = StyleSheet.create({
  container: {
    padding: 32,
    backgroundColor: AppColors.white,
    justifyContent: 'center',
    alignItems: 'center',
  },
  contentWrapper: {
    paddingTop: 214,
  },
  actionWrapper: {
    paddingTop: 16,
    paddingBottom: getBottomSafe() + 16,
  },
});

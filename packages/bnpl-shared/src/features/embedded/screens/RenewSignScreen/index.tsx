import React from 'react';
import { View } from 'react-native';
import { FullScreenFormLayout } from 'bnpl-shared/src/components/layouts/FullScreenFormLayout';
import { Spacer } from 'bnpl-shared/src/components/Spacer';
import { AppColo<PERSON>, ScreenKey } from 'bnpl-shared/src/constants';
import { AppButton, AppImage, AppText } from 'bnpl-shared/src/shared/react-native-customized';
import { StyleSheet } from 'bnpl-shared/src/shared/styles/StyleSheet';
import { images } from 'bnpl-shared/src/res';
import { postInitRenewSignContractApi } from 'bnpl-shared/src/api/renew_overdraft/postInitRenewSignContractApi';
import { useAppSelector } from 'bnpl-shared/src/redux/store';
import { SignMethod } from 'bnpl-shared/src/types';
import { toast } from 'bnpl-shared/src/components/Toaster';
import { hideLoading, showLoading } from 'bnpl-shared/src/shared/ZaloPayModules';
import { useNavigation } from 'bnpl-shared/src/shared/navigation/useNavigation';
import { getBottomSafe } from 'bnpl-shared/src/utils';

export default function EmbeddedRenewSignScreen(props: any) {
  const navigation = useNavigation(props);
  const { renewInfo } = useAppSelector(state => state.featureRenewOverDraft);
  async function handleClickCTA() {
    try {
      showLoading();
      const { authentication_type } = await postInitRenewSignContractApi(renewInfo.data?.appendixId || 0);
      if (authentication_type === SignMethod.FACE) {
        navigation.navigate(ScreenKey.EmbeddedRenewFaceAuthenScreen);
        return;
      } else {
        toast.error('Phương thức xác thực chưa hỗ trợ');
      }
    } catch (e: any) {
      toast.error(e.message || 'Có lỗi xảy ra, vui lòng thử lại sau');
    } finally {
      hideLoading();
    }
  }
  return (
    <FullScreenFormLayout
      contentStyles={styles.contentWrapper}
      actionsStyles={styles.actionWrapper}
      content={
        <View style={styles.container}>
          <AppImage
            source={images.ImageEmbeddedSignRenew}
            width={200}
            height={200}
          />
          <Spacer height={32} />
          <AppText size={16} height={24} color={AppColors.dark[500]} bold>Bấm “Xác thực ngay” để ký hợp đồng</AppText>
        </View>
      }
      actions={
        <AppButton title="Xác thực ngay" onPress={handleClickCTA} />
      }
    />
  )
}

const styles = StyleSheet.create({
  container: {
    padding: 32,
    backgroundColor: AppColors.white,
    justifyContent: 'center',
    alignItems: 'center',
  },
  contentWrapper: {
    paddingTop: 214,
  },
  actionWrapper: {
    paddingTop: 16,
    paddingBottom: getBottomSafe() + 16,
  },
});

import React from 'react';
import { View } from 'react-native';
import { StyleSheet } from 'bnpl-shared/src/shared/styles/StyleSheet';
import { AppText } from 'bnpl-shared/src/shared/react-native-customized';
import { AppColors } from 'bnpl-shared/src/constants';
import { Skeleton } from 'bnpl-shared/src/shared/animated-wrappers/Skeleton';
import { Spacer } from 'bnpl-shared/src/components/Spacer';

export default function InfoRow({ title, value, isLoading }: InfoRowProps) {
  return (
    <View style={styles.container}>
      <AppText color={AppColors.dark[500]} size={14} height={18} style={styles.title}>{title}</AppText>
      <Spacer width={16} />
      {isLoading ? (
        <Skeleton width={100} height={18} />
      ) : (
        <AppText color={AppColors.dark[500]} size={14} height={18} style={styles.value}>{value}</AppText>
      )}
    </View>
  );
}

interface InfoRowProps {
  title: string;
  value: string;
  isLoading?: boolean;
}

const styles = StyleSheet.create({
  container: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    minHeight: 50,
    padding: 16,
  },
  title: {
    textAlign: 'left',
    flexShrink: 0,
  },
  value: {
    textAlign: 'right',
  },
});

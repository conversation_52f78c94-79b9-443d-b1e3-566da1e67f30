import React, { useEffect } from 'react';
import { ScrollView, View } from 'react-native';
import { StyleSheet } from 'bnpl-shared/src/shared/styles/StyleSheet';
import Inform from 'bnpl-shared/src/components/Inform';
import { Spacer } from 'bnpl-shared/src/components/Spacer';
import { AppColors, PartnerCode, ScreenKey } from 'bnpl-shared/src/constants';
import InfoRow from './components/InfoRow';
import { AppButton, AppText } from 'bnpl-shared/src/shared/react-native-customized';
import { LinkButton } from 'bnpl-shared/src/shared/LinkButton';
import { ResourceState, RewardBannerKey } from 'bnpl-shared/src/types';
import RewardBanner from 'bnpl-shared/src/features/reward_banner';
import { getBottomSafe } from 'bnpl-shared/src/utils';
import { toast } from 'bnpl-shared/src/components/Toaster';
import { hideLoading, showLoading } from 'bnpl-shared/src/shared/ZaloPayModules';
import { postConfirmRenewRequestApi, postCreateRenewRequestApi } from 'bnpl-shared/src/api/renew_overdraft';
import { useDispatch } from 'react-redux';
import { setAppendixData } from 'bnpl-shared/src/redux/featureRenewOverDraftReducer';
import { useAppSelector } from 'bnpl-shared/src/redux/store';
import { useNavigation } from 'bnpl-shared/src/shared/navigation/useNavigation';
import { InfoModalService, ModalType } from 'bnpl-shared/src/services';
import { EdgeCaseModalUI } from 'bnpl-shared/src/screens/onboarding-flow/OnboardingScreen/bod1/EdgeCaseModal/EdgeCaseModalUI';
import { useErrorHandler } from 'bnpl-shared/src/features/multipartner_integration/helpers/useErrorHandler';

export default function RenewalInfoScreen(props: any) {
  const navigation = useNavigation(props);
  const dispatch = useDispatch();
  const { renewInfo } = useAppSelector(state => state.featureRenewOverDraft);
  const handleError = useErrorHandler();

  async function handleClickCTA() {
    try {
      const { data, code } = await postConfirmRenewRequestApi(renewInfo.data?.appendixId || 0);
      if (code === 1) {
        navigation.replace(ScreenKey.EmbeddedRenewSignScreen);
        return;
      }
      InfoModalService.showModal({
        screen: (
          <EdgeCaseModalUI
            edgeCaseInfo={data}
            handleCtaAction={(cta: string) => {
              InfoModalService.hideModal();
            }}
          />
        ),
        type: ModalType.BOTTOM_SHEET,
        bottomSheetProps: {
          title: '',
        },
      });
    } catch (e: any) {
      handleError({
        error: e,
      });
    }
  }

  useEffect(() => {
    (async () => {
      try {
        showLoading();
        const { data } = await postCreateRenewRequestApi();
        if (data.appendix_id) {
          dispatch(
            setAppendixData({
              id: data.appendix_id,
              info: data.renew_appendix_info,
              status: data.renew_appendix_status,
            }),
          );
        } else if (data.notice) {
          InfoModalService.showModal({
            screen: (
              <EdgeCaseModalUI
                edgeCaseInfo={data.notice}
                handleCtaAction={() => {
                  InfoModalService.hideModal();
                }}
              />
            ),
            type: ModalType.BOTTOM_SHEET,
            bottomSheetProps: {
              title: '',
            },
          });
        }
      } catch (e: any) {
        toast.error('Có lỗi xảy ra, vui lòng thử lại sau!', { duration: 3000 });
      } finally {
        hideLoading();
      }
    })();
  }, []);
  const isLoading = renewInfo.state !== ResourceState.READY || !renewInfo.data?.appendixInfo;
  return (
    <View style={styles.container}>
      <ScrollView contentContainerStyle={styles.scrollViewContent}>
        <Inform type="info">
          Bạn thực hiện tái ký để tiếp tục sử dụng Tài khoản trả sau nhé!
        </Inform>
        <Spacer height={16} />
        <View style={styles.infoContainer}>
          <InfoRow title="Họ tên" value={renewInfo.data?.appendixInfo?.full_name || ''} isLoading={isLoading} />
          <InfoRow title="Ngày sinh" value={renewInfo.data?.appendixInfo?.birthday || ''} isLoading={isLoading} />
          <InfoRow title="Giới tính" value={renewInfo.data?.appendixInfo?.gender || ''} isLoading={isLoading} />
          <InfoRow title="CMND/CCCD" value={renewInfo.data?.appendixInfo?.id_number || ''} isLoading={isLoading} />
          <InfoRow title="Ngày cấp" value={renewInfo.data?.appendixInfo?.id_issued_date || ''} isLoading={isLoading} />
          <InfoRow title="Số TKTT" value={renewInfo.data?.appendixInfo?.account_number || ''} isLoading={isLoading} />
          <InfoRow title="Hình chân dung" value={renewInfo.data?.appendixInfo?.face_info || ''} isLoading={isLoading} />
          <InfoRow title="Nơi cấp" value={renewInfo.data?.appendixInfo?.id_issued_location || ''} isLoading={isLoading} />
          <InfoRow title="Chứng minh cũ" value={renewInfo.data?.appendixInfo?.old_id_number || ''} isLoading={isLoading} />
          <InfoRow title="Quốc tịch" value={renewInfo.data?.appendixInfo?.nationality || ''} isLoading={isLoading} />
          <InfoRow title="Ngày hết hạn" value={renewInfo.data?.appendixInfo?.id_expired_date || ''} isLoading={isLoading} />
          <InfoRow title="Địa chỉ thường trú" value={renewInfo.data?.appendixInfo?.certify_address || ''} isLoading={isLoading} />
        </View>
        <Spacer height={8} />
        <AppText>
          Bằng cách nhấn “Tiếp tục”, tôi xác nhận đã đọc, đồng ý với nội dung{' '}
          <LinkButton
            onPress={() => {
            }}>
            Hợp đồng mẫu
          </LinkButton>{' '}
          và{' '}
          <LinkButton
            onPress={() => {
            }}>
            Chính sách bảo vệ quyền riêng tư của Zalopay
          </LinkButton>{' '}
        </AppText>
      </ScrollView>
      <View style={styles.footer}>
        <RewardBanner
          partner={PartnerCode.CIMB.toLocaleLowerCase()}
          bannerKey={RewardBannerKey.RenewApplication}
        />
        <AppButton
          disabled={isLoading}
          onPress={handleClickCTA}
          title="Tiếp tục"
        />
      </View>
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: AppColors.white,
  },
  scrollViewContent: {
    padding: 16,
  },
  infoContainer: {
    paddingVertical: 8,
    borderRadius: 8,
    borderWidth: 1,
    borderColor: AppColors.strokeV2,
  },
  footer: {
    paddingTop: 16,
    paddingBottom: getBottomSafe() + 16,
    paddingHorizontal: 16,
  },
});

import { ErrorBoundary } from 'bnpl-shared/src/app/ErrorBoundary';
import { Skeleton } from 'bnpl-shared/src/shared/animated-wrappers/Skeleton';
import { StyleSheet } from 'bnpl-shared/src/shared/styles/StyleSheet';
import { loadRemote } from 'bnpl-shared/src/utils/loadRemote';
import React, { Suspense, useEffect, useRef, useState } from 'react';
import { LayoutChangeEvent, StyleProp, View, ViewStyle } from 'react-native';
import { getFinSDKURL } from './getFinSDKURL';
import { createRemoteComponent } from '@module-federation/bridge-react';

export enum PermissionStatus {
    PERMISSION_DENIED = 'PERMISSION_DENIED',
    PERMISSION_NOT_GRANTED = 'PERMISSION_NOT_GRANTED',
    PERMISSION_ALWAYS = 'PERMISSION_ALWAYS',
    PERMISSION_WHEN_USE = 'PERMISSION_WHEN_USE',
}

export type CityModel = {
  id: number;
  name: string;
  city_name: string;
  latitude: number;
  longitude: number;
};

export interface CurrentLocation {
  permissionStatus: PermissionStatus;
  shouldOpenSetting: boolean;
  location: CityModel
};


export interface LocationPickerProps {
  onLocationChange: (result: CurrentLocation) => void;
  onLayout?: (event: LayoutChangeEvent) => void;
  children?: React.ReactNode;
  title?: string;
}

function LocationPicker({
  onLocationChange,
  onLayout,
  ...props
}: LocationPickerProps) {
  const MFComponent = useRef<React.ComponentType<any> | null>(null);
    const [isEmpty, setIsEmpty] = useState(true);

  useEffect(() => {
    const fetchRemoteComponent = async () => {
      try {
         MFComponent.current = createRemoteComponent({
          loader: async () =>
            await await loadRemote(
              getFinSDKURL(), // Remote URL
              'fin_sdk', // Remote container name
              './R17LocationPicker', // Module name (remote component)
            ),
          export: 'R17LocationPicker',
          fallback: () => <Skeleton width={120} height={40} />,
          loading: () => <Skeleton width={120} height={40} />,
        });
        setIsEmpty(false);
      } catch (error) {
        setIsEmpty?.(true);
        console.error('Error loading remote component:', error);
      }
    };
    fetchRemoteComponent();
  }, []);

  if (isEmpty || !MFComponent.current) return null;
  
  const Comp = MFComponent.current;
  return (
    <Suspense fallback={() => <Skeleton width={343} height={40} />}>
      <ErrorBoundary>
        <View onLayout={onLayout} style={styles.root}>
          {Comp ? <Comp onLocationChange={onLocationChange} {...props} />: null}
        </View>
      </ErrorBoundary>
    </Suspense>
  );
}

export default LocationPicker;

const styles = StyleSheet.create({
  root: {
    paddingHorizontal: 16,
  },
});

import React from 'react';
import { View } from 'react-native';
import ImageSlider from '../ImageSlider';
import { images } from 'bnpl-shared/src/res';
import { BottomSheetLayout } from 'bnpl-shared/src/shared/BottomSheet/BottomSheetLayout';
import { StyleSheet } from 'bnpl-shared/src/shared/styles/StyleSheet';
import { AppColors } from 'bnpl-shared/src/constants';

const slides = [
  {
    image: images.MiniBNPLPayTutorialStep1,
    text: '1. Chọn dịch vụ\n“Điện thoại” hoặc “Vé phim”',
  },
  {
    image: images.MiniBNPLPayTutorialStep2,
    text: '2. Chọn sản phẩm',
  },
  {
    image: images.MiniBNPLPayTutorialStep3,
    text: '3. Ở bước xác nhận giao dịch,\nchọn “Xem tất cả” phư<PERSON>ng thức thanh toán',
  },
  {
    image: images.MiniBNPLPayTutorialStep4,
    text: '4. <PERSON><PERSON><PERSON> “<PERSON><PERSON><PERSON> khoản trả sau”',
  },
  {
    image: images.MiniBNPLPayTutorialStep5,
    text: '5. <PERSON><PERSON><PERSON> “Xác nhận” để thanh toán',
  },
];

export default function PayTutorialContent({ onRequestClose }: PayTutorialContentProps  ) {
  return (
    <BottomSheetLayout
      title="Mua sắm bằng Tài khoản trả sau"
      onRequestClose={onRequestClose}
      content={
        <View style={styles.container}>
          <ImageSlider slides={slides} />
        </View>
      }
    />
  )
}

interface PayTutorialContentProps {
  onRequestClose: () => void;
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    width: '100%',
    backgroundColor: AppColors.white,
    paddingTop: 16,
    paddingBottom: 32,
    paddingHorizontal: 24,
  },
});

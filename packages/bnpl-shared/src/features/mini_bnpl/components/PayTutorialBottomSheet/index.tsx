import React from 'react';
import { AppModal } from 'bnpl-shared/src/shared/react-native-customized/AppModal';
import PayTutorialContent from './PayTutorialContent';

export default function PayTutorialBottomSheet({ visible, onRequestClose }: PayTutorialBottomSheetProps) {
  return (
    <AppModal transparent visible={visible} onRequestClose={onRequestClose}>
      <PayTutorialContent onRequestClose={onRequestClose} />
    </AppModal>
  )
}

interface PayTutorialBottomSheetProps {
  visible: boolean;
  onRequestClose: () => void;
}

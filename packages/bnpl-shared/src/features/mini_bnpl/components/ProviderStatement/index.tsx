import React from 'react';
import { View, StyleProp, ViewStyle, TextStyle } from 'react-native';
import { StyleSheet } from 'bnpl-shared/src/shared/styles/StyleSheet';
import { AppImage, AppText } from 'bnpl-shared/src/shared/react-native-customized';
import { images } from 'bnpl-shared/src/res';
import { AppColors } from 'bnpl-shared/src/constants';

export default function ProviderStatement({ style, labelStyle }: { style?: StyleProp<ViewStyle>, labelStyle?: StyleProp<TextStyle> }) {
  return (
    <View style={[styles.container, style]}>
      <AppText color={AppColors.dark[300]} size={12} height={16} style={labelStyle}>*Sản phẩm mua hàng trả chậm được cung cấp bởi</AppText>
      <AppImage style={styles.label} source={images.LogoMiniBNPL} height={10} width={44} />
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flexDirection: 'row',
    justifyContent: 'center',
    alignItems: 'center',
    marginTop: 24,
    paddingBottom: 32,
  },
  label: {
    marginLeft: 2,
    marginTop: 1,
  },
});

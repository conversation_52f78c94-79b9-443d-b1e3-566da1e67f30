import React from 'react';
import { View } from 'react-native';
import ImageSlider from '../ImageSlider';
import { images } from 'bnpl-shared/src/res';
import { BottomSheetLayout } from 'bnpl-shared/src/shared/BottomSheet/BottomSheetLayout';
import { StyleSheet } from 'bnpl-shared/src/shared/styles/StyleSheet';
import { AppColors } from 'bnpl-shared/src/constants';

const slides = [
  {
    image: images.MiniBNPLRepayTutorialStep1,
    text: '1. Bấm “Thanh toán ngay”',
  },
  {
    image: images.MiniBNPLRepayTutorialStep2,
    text: '2. <PERSON><PERSON>m tra số tiền đến hạn\nbấm “Thanh toán”',
  },
  {
    image: images.MiniBNPLRepayTutorialStep3,
    text: '3. <PERSON><PERSON><PERSON> thức thanh toán\nvà bấm “Xác nhận”',
  },
];

export default function RepayTutorialContent({ onRequestClose }: RepayTutorialContentProps) {
  return (
    <BottomSheetLayout
      title="Thanh toán khoản trả chậm"
      onRequestClose={onRequestClose}
      content={
        <View style={styles.container}>
          <ImageSlider slides={slides} />
        </View>
      }
    />
  )
}

interface RepayTutorialContentProps {
  onRequestClose: () => void;
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    width: '100%',
    backgroundColor: AppColors.white,
    paddingTop: 16,
    paddingBottom: 32,
    paddingHorizontal: 24,
  },
});

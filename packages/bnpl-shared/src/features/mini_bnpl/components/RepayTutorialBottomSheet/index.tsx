import React from 'react';
import { AppModal } from 'bnpl-shared/src/shared/react-native-customized/AppModal';
import RepayTutorialContent from './RepayTutorialContent';

export default function RepayTutorialBottomSheet({ visible, onRequestClose }: RepayTutorialBottomSheetProps) {
  return (
    <AppModal transparent visible={visible} onRequestClose={onRequestClose}>
      <RepayTutorialContent onRequestClose={onRequestClose} />
    </AppModal>
  )
}

interface RepayTutorialBottomSheetProps {
  visible: boolean;
  onRequestClose: () => void;
}

import React from 'react';
import { TouchableOpacity, View } from 'react-native';
import { StyleSheet } from 'bnpl-shared/src/shared/styles/StyleSheet';
import { AppText } from 'bnpl-shared/src/shared/react-native-customized/AppText';
import { AppColors } from 'bnpl-shared/src/constants';

export default function SectionHeader({ title, cta }: SectionHeaderProps) {
  return (
    <View style={styles.container}>
      <View style={styles.left}>
        <View style={styles.mark} />
        <AppText style={styles.title} color={AppColors.dark[500]} size={16} height={20} weight="bold">{title}</AppText>
      </View>
      {cta && (
        <TouchableOpacity activeOpacity={0.8} onPress={cta.onClick}>
          <AppText color={AppColors.blue[500]} size={16} height={20}>{cta.label}</AppText>
        </TouchableOpacity>
      )}
    </View>
  )
}

interface SectionHeaderProps {
  title: string;
  cta?: {
    label: string;
    onClick: () => void;
  }
}

const styles = StyleSheet.create({
  container: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    height: 40,
  },
  mark: {
    borderTopLeftRadius: 2,
    borderBottomLeftRadius: 2,
    height: 14,
    width: 4,
    backgroundColor: AppColors.green[4],
  },
  title: {
    marginLeft: 8,
  },
  left: {
    flex: 1,
    flexDirection: 'row',
    alignItems: 'center',
  },
});

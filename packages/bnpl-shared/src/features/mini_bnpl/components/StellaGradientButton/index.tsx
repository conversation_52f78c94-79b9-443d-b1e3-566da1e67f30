import React from 'react';
import { View, TouchableOpacity } from 'react-native';
import { StyleSheet } from 'bnpl-shared/src/shared/styles/StyleSheet';
import { AppText } from 'bnpl-shared/src/shared/react-native-customized';
import LinearGradient from 'react-native-linear-gradient';
import { AppColors } from 'bnpl-shared/src/constants';

export default function StellaGradientButton({ onClick, children }: StellaGradientButtonProps) {
  return (
    <TouchableOpacity activeOpacity={0.8} style={styles.container} onPress={onClick}>
      <LinearGradient
        start={{ x: 0.605, y: -0.11 }}
        end={{ x: 0.637, y: 0.4975 }}
        colors={['#0AADF8', '#0033C9']}
        style={styles.ctaGradient}
      />
      <View style={styles.content}>
        <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
          <path d="M12.75 2C12.75 1.58579 12.4142 1.25 12 1.25C11.5858 1.25 11.25 1.58579 11.25 2C11.25 7.2467 6.9967 11.5 1.75 11.5C1.33579 11.5 1 11.8358 1 12.25C1 12.6642 1.33579 13 1.75 13C6.99671 13 11.25 17.2533 11.25 22.5C11.25 22.9142 11.5858 23.25 12 23.25C12.4142 23.25 12.75 22.9142 12.75 22.5C12.75 17.2533 17.0033 13 22.25 13C22.6642 13 23 12.6642 23 12.25C23 11.8358 22.6642 11.5 22.25 11.5C17.0033 11.5 12.75 7.24671 12.75 2Z" fill="white" />
        </svg>
        <AppText style={styles.text} size={16} height={20} bold color={AppColors.white}>{children}</AppText>
      </View>
    </TouchableOpacity>
  );
}

interface StellaGradientButtonProps {
  children: string;
  onClick: () => void;
}

const styles = StyleSheet.create({
  container: {
    height: 48,
    width: '100%',
    borderRadius: 24,
    backgroundColor: AppColors.blue[500],
    position: 'relative',
    overflow: 'hidden',
    alignItems: 'center',
    justifyContent: 'center',
  },
  content: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
  },
  text: {
    marginLeft: 10,
  },
  ctaGradient: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
  },
});

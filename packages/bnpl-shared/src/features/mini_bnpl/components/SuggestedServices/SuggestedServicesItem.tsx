import React from 'react';
import { TouchableOpacity, View } from 'react-native';
import { StyleSheet } from 'bnpl-shared/src/shared/styles/StyleSheet';
import { AppText } from 'bnpl-shared/src/shared/react-native-customized/AppText';
import { AppColors } from 'bnpl-shared/src/constants';
import { AppImage } from 'bnpl-shared/src/shared/react-native-customized/AppImage';

export default function SuggestedServicesItem({ imageURL, title, onClick, bordered, lastItem }: SuggestedServicesItemProps) {
  return (
    <TouchableOpacity activeOpacity={1} style={[styles.container, bordered && styles.bordered, lastItem && styles.lastItem]} onPress={onClick}>
      <AppImage source={{ uri: imageURL }} height={40} width={40} />
      <AppText style={styles.text} numberOfLines={2} size={14} height={18}>{title}</AppText>
    </TouchableOpacity>
  )
}

interface SuggestedServicesItemProps {
  imageURL: string;
  title: string;
  onClick: () => void;
  bordered?: boolean;
  lastItem?: boolean;
  marginRight?: number;
}

const styles = StyleSheet.create({
  container: {
    flexDirection: 'column',
    alignItems: 'center',
    justifyContent: 'center',
    width: 100,
    height: 100,
    borderRadius: 10,
    flex: 1,
    marginRight: 12,
  },
  lastItem: {
    marginRight: 0,
  },
  bordered: {
    borderColor: AppColors.stroke,
    borderWidth: 1,
  },
  text: {
    textAlign: 'center',
  }
})

import React, { useEffect, useRef, useState } from 'react';
import { LayoutChangeEvent, LayoutRectangle, Platform, TouchableOpacity, View } from 'react-native';
import { StyleSheet } from 'bnpl-shared/src/shared/styles/StyleSheet';
import { trackingBalanceLoad } from 'bnpl-shared/src/screens/HomeScreen/tracking';
import { useUserBalanceController } from 'bnpl-shared/src/utils/useUserBalanceController';
import { ResourceState } from 'bnpl-shared/src/types';
import { AppColors } from 'bnpl-shared/src/constants';
import { AppText } from 'bnpl-shared/src/shared/react-native-customized/AppText';
import { formatCurrency } from 'bnpl-shared/src/shared/utils/formatCurrency';
import LinearGradient from 'react-native-linear-gradient';
import StickyTooltip from 'bnpl-shared/src/components/StickyTooltip';
import { GeneralInfoLineSecondary } from '@zpi/looknfeel-icons';
import * as Tracking from '../../tracking';
import { Skeleton } from 'bnpl-shared/src/shared/animated-wrappers/Skeleton';

export default function BalanceSummary({
  isPreviewMode,
}: BalanceSummarySectionProps) {
  const startLoadTimeRef = useRef<number>(0);
  const { balanceSummary } = useUserBalanceController();
  const [tooltipVisible, setTooltipVisible] = useState(false);
  const [tooltipAnchor, setTooltipAnchor] = useState<LayoutRectangle>();
  const availableBalance = balanceSummary.data?.available_balance || 0;

  useEffect(() => {
    if (tooltipVisible) {
      setTimeout(() => {
        setTooltipVisible(false);
      }, 3000);
    }
  }, [tooltipVisible]);

  function handleClickInfoTooltip() {
    Tracking.clickBalanceSummaryTooltip({ accountState: 'active' });
    setTooltipVisible(!tooltipVisible);
  }

  const onLayout = (event: LayoutChangeEvent) => {
    setTooltipAnchor(event.nativeEvent.layout);
  };

  useEffect(() => {
    startLoadTimeRef.current = new Date().getTime();
  }, []);

  useEffect(() => {
    if (
      (balanceSummary.state === ResourceState.READY || balanceSummary.state === ResourceState.FAIL) &&
      startLoadTimeRef.current > 0
    ) {
      const load_time = Math.round((new Date().getTime() - startLoadTimeRef.current) / 1000);
      trackingBalanceLoad({ load_success: balanceSummary.state === ResourceState.READY, load_time });
      startLoadTimeRef.current = 0;
    }
  }, [balanceSummary.state]);

  return (
    <>
    <View style={styles.gradientWrapper}>
      <LinearGradient colors={['rgba(213, 253, 237, 1)', 'rgba(213, 253, 237, 0)']} style={styles.gradient} />
      <View style={styles.container}>
        <View style={styles.row}>
          <View style={styles.left}>
            <AppText size={14} height={18} color={AppColors.dark["500"]}>Số dư khả dụng</AppText>
            <TouchableOpacity style={styles.info} onLayout={onLayout} onPress={handleClickInfoTooltip}>
              <GeneralInfoLineSecondary color={AppColors.blue["500"]} width={16} height={16} />
            </TouchableOpacity>
          </View>
          <View style={styles.statusView}>
            <AppText size={12} height={16} color={AppColors.green["1"]}>Đang hoạt động</AppText>
          </View>
        </View>
        {balanceSummary.state !== ResourceState.READY ? <Skeleton style={styles.amountText} height={36} width={100} /> : <AppText style={styles.amountText} size={24} height={36} color={AppColors.dark["500"]} bold>{formatCurrency(availableBalance)}</AppText>}
      </View>
      </View>
      <StickyTooltip
        visible={tooltipVisible}
        anchorView={{ ...tooltipAnchor, x: 115, height: 32 } as LayoutRectangle}
        onTooltipPress={() => setTooltipVisible(!tooltipVisible)}
        content={`Số tiền tối đa hiện tại bạn có thể\nsử dụng để thanh toán bằng\nTài khoản trả sau.`}
      />
    </>
  );
};

interface BalanceSummarySectionProps {
  onRequestToogleOverRepaymentModal?: () => void;
  showOutStandingBalanceTooltip?: boolean;
  onTooltipPress?: () => void;
  isPreviewMode: boolean;
};

const styles = StyleSheet.create({
  gradientWrapper: {
    borderRadius: 16,
    padding: 1,
    overflow: 'hidden',
    height: 94,
  },
  gradient: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
  },
  container: {
    backgroundColor: AppColors.white,
    padding: 16,
    borderRadius: 16,
  },
  info: {
    flexDirection: 'row',
    alignItems: 'center',
    width: 16,
    height: 16,
    marginLeft: 4,
  },
  row: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    width: '100%',
  },
  left: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  statusView: {
    height: 16,
    paddingHorizontal: 8,
    borderRadius: 4,
    backgroundColor: AppColors.green["5"],
  },
  amountText: {
    marginTop: 8,
  }
});

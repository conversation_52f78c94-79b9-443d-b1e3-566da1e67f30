import React from 'react';
import { AppImage, AppText } from 'bnpl-shared/src/shared/react-native-customized';
import { View, TouchableOpacity } from 'react-native';
import { StyleSheet } from 'bnpl-shared/src/shared/styles/StyleSheet';
import PayTutorialContent from 'bnpl-shared/src/features/mini_bnpl/components/PayTutorialBottomSheet/PayTutorialContent';
import LinearGradient from 'react-native-linear-gradient';
import { images } from 'bnpl-shared/src/res';
import * as Tracking from '../../tracking';
import { InfoModalService, ModalType } from 'bnpl-shared/src/services';

export default function BuyTutorialButton() {
  return (
    <View style={styles.container}>
      <TouchableOpacity
        style={styles.content}
        onPress={() => {
          Tracking.clickBuyTutorialButton();
          InfoModalService.showModal({
            screen: (
              <PayTutorialContent
                onRequestClose={() => {
                  InfoModalService.hideModal();
                }}
              />
            ),
            type: ModalType.MODAL,
            options: {
              transparent: true,
            },
          });
        }}
      >
        <LinearGradient
          colors={[
            '#CEF0FF',
            'rgba(159, 225, 255, 0.5)',
            'rgba(205, 239, 255, 0.600687)',
            '#9FE1FF'
          ]}
          start={{ x: 1, y: 0.5 }}
          end={{ x: 0, y: 0.5 }}
          style={styles.gradientBox}
        >
          <View style={styles.overlay} />
        </LinearGradient>
        <AppImage source={images.IconMiniBNPLBuyTutorial} width={71} height={63} />
        <View style={styles.floatContent}>
          <AppText size={14} height={18}>Hướng dẫn mua sắm bằng{"\n"}Tài khoản trả sau</AppText>
          <AppImage source={images.IconArrowNextOutlineActive} width={16} height={16} />
        </View>
      </TouchableOpacity>
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    height: 70,
    flexDirection: 'row',
    alignItems: 'center',
  },
  content: {
    height: 44,
    width: 343,
    flexDirection: 'row',
    alignItems: 'center',
    position: 'relative',
  },
  floatContent: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    zIndex: 10,
    flex: 1,
    paddingLeft: 8,
    paddingRight: 16,
  },
  gradientBox: {
    width: 343,
    height: 44,
    position: 'absolute',
    top: 0,
    bottom: 0,
    left: 0,
    right: 0,
    borderRadius: 12,
  },
  overlay: {
    ...StyleSheet.absoluteFillObject,
    backgroundColor: 'white',
    opacity: 0.8,
  },
});

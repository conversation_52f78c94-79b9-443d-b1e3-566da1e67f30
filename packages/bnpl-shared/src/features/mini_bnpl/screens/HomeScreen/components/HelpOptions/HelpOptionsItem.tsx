import React from 'react';
import { Image, StyleSheet, TouchableOpacity, View } from 'react-native';
import { AppImage, AppText } from 'bnpl-shared/src/shared/react-native-customized';
import { AppColors, PartnerCode } from 'bnpl-shared/src/constants';
import { StyleUtils, windowWidth } from 'bnpl-shared/src/shared/styles/StyleUtils';
import { SubjectItemType } from 'bnpl-shared/src/types';
import { images } from 'bnpl-shared/src/res';
import { Divider } from 'bnpl-shared/src/components/Divider';
import { GeneralArrowNext4Secondary
 } from '@zpi/looknfeel-icons';

export default function HelpOptionsItem({ tag, title, icon, onItemClick, lastItem }: HelpOptionsItemProps) {
  return (
    <>
      <TouchableOpacity
        testID={tag}
        onPress={() => onItemClick?.(tag)}
        style={styles.container}
        key={tag}>
        <View style={styles.left}>
          {icon}
          <AppText style={styles.title} center>{title}</AppText>
        </View>
        <GeneralArrowNext4Secondary />
      </TouchableOpacity>
      {!lastItem && <Divider />}
    </>
  )
}

interface HelpOptionsItemProps {
  tag: string;
  icon: any;
  title: string;
  onItemClick?: (tag: string) => void;
  lastItem?: boolean;
}

const styles = StyleSheet.create({
  container: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    height: 49,
  },
  left: {
    flex: 1,
    flexDirection: 'row',
    alignItems: 'center',
  },
  title: {
    marginLeft: 12,
  }
});

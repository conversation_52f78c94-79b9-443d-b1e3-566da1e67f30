import React from 'react';

export default function IconGeneralBuy({ color = '#0033C9', width, height }: IconGeneralBuyProps) {
  return (
    <svg width={width} height={height} viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
      <path fill-rule="evenodd" clip-rule="evenodd" d="M2.81615 4.59837C2.67704 4.21261 2.87354 3.78664 3.25729 3.64208L4.93621 3.00964C5.2547 2.88967 5.61405 2.99919 5.81149 3.2764C5.86709 3.35447 6.00618 3.49567 6.18888 3.58258C6.35223 3.66028 6.53022 3.68804 6.74072 3.60875C6.95121 3.52945 7.06666 3.39116 7.13814 3.225C7.2181 3.03915 7.22946 2.84127 7.21974 2.74593C7.18521 2.40734 7.38299 2.08795 7.70148 1.96798L9.47916 1.29834C9.864 1.15337 10.2938 1.34535 10.4427 1.7287L10.8446 2.76378C11.0209 2.74182 11.1509 2.66005 11.2577 2.54793C11.3973 2.40146 11.4767 2.21986 11.5007 2.12708C11.586 1.79747 11.8827 1.56669 12.2232 1.56505L14.322 1.55497C14.5215 1.55401 14.7132 1.6326 14.8547 1.77336C14.9961 1.91411 15.0756 2.10542 15.0756 2.30496V9.00085H16.424L17.1363 5.7464C17.495 4.10794 18.9459 2.94019 20.6232 2.94019H21.3633C21.7835 2.94019 22.1242 3.28084 22.1242 3.70105C22.1242 4.12127 21.7835 4.46192 21.3633 4.46192H20.6232C19.661 4.46192 18.8286 5.13183 18.6229 6.07176C18.1453 8.25372 16.5675 15.0061 16.5675 15.0061C16.5055 15.2994 16.4445 15.5725 16.3826 15.8224C16.0751 17.0653 15.046 17.892 13.8128 18.0204C11.9949 18.2096 9.23727 18.1932 7.26258 18.0081C5.87601 17.8782 4.74709 16.9807 4.31576 15.6528C3.92872 14.4613 3.48935 12.8939 3.16857 11.7107C2.87006 10.6096 3.47834 9.53453 4.45751 9.14969L2.81615 4.59837ZM5.95081 9.00085H11.6575L9.31315 2.96377L8.69253 3.19755C8.66208 3.39588 8.60638 3.60779 8.51604 3.81779C8.31395 4.28754 7.92808 4.76437 7.26949 5.01246C6.6109 5.26054 6.00633 5.1568 5.54454 4.93713C5.3381 4.83893 5.15643 4.71644 5.0027 4.58749L4.47806 4.78512L5.90613 8.745C5.93661 8.82952 5.95098 8.91592 5.95081 9.00085ZM13.2666 9.00085H13.5756V3.05857L12.7246 3.06266C12.6271 3.23797 12.5013 3.41721 12.3437 3.58265C12.1062 3.83194 11.7894 4.05529 11.3929 4.17575L13.2666 9.00085ZM5.2495 10.5226C4.82709 10.5226 4.53009 10.9171 4.63729 11.3125C4.9572 12.4925 5.38837 14.0292 5.76306 15.1827C6.00568 15.9296 6.61911 16.4194 7.40459 16.493C9.29699 16.6704 11.9529 16.6841 13.6552 16.5069C14.2914 16.4406 14.7615 16.0384 14.9055 15.4569C15.0376 14.9229 15.1664 14.2725 15.3204 13.4951C15.3522 13.3345 15.3851 13.1685 15.4193 12.9969C15.567 12.2563 15.7367 11.4272 15.9463 10.5226H5.2495Z" fill={color} />
      <path d="M3.7876 20.8157C3.7876 19.9933 4.4543 19.3266 5.27672 19.3266C6.09915 19.3266 6.76585 19.9933 6.76585 20.8157C6.76585 21.6381 6.09915 22.3049 5.27672 22.3049C4.4543 22.3049 3.7876 21.6381 3.7876 20.8157Z" fill={color} />
      <path d="M14.744 19.3266C13.9216 19.3266 13.2549 19.9933 13.2549 20.8157C13.2549 21.6381 13.9216 22.3049 14.744 22.3049C15.5664 22.3049 16.2331 21.6381 16.2331 20.8157C16.2331 19.9933 15.5664 19.3266 14.744 19.3266Z" fill={color} />
    </svg>
  );
}

interface IconGeneralBuyProps {
  color?: string;
  width?: number;
  height?: number;
}

import React from 'react';

export default function IconGeneralTerm({ color = '#0033C9', width, height }: IconGeneralTermProps) {
  return (
    <svg width={width} height={height} viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
      <path d="M12.0101 22.7403C11.3801 22.7403 10.7601 22.5303 10.2701 22.1103L8.69012 20.7603C8.53012 20.6203 8.13012 20.4803 7.92012 20.4803H6.17012C4.69012 20.4803 3.49012 19.2803 3.49012 17.8003V16.0903C3.49012 15.8803 3.35012 15.4803 3.22012 15.3303L1.86012 13.7303C1.04012 12.7603 1.04012 11.2403 1.86012 10.2603L3.22012 8.66031C3.35012 8.51031 3.49012 8.11031 3.49012 7.90031V6.20031C3.49012 4.72031 4.69012 3.52031 6.17012 3.52031H7.90012C8.11012 3.52031 8.50012 3.37031 8.67012 3.23031L10.2501 1.88031C11.2301 1.05031 12.7601 1.05031 13.7401 1.88031L15.3201 3.23031C15.4801 3.37031 15.8901 3.51031 16.1001 3.51031H17.8001C19.2801 3.51031 20.4801 4.71031 20.4801 6.19031V7.89031C20.4801 8.10031 20.6301 8.49031 20.7701 8.66031L22.1201 10.2403C22.9601 11.2303 22.9501 12.7603 22.1201 13.7303L20.7701 15.3103C20.6301 15.4803 20.4901 15.8703 20.4901 16.0803V17.7803C20.4901 19.2603 19.2901 20.4603 17.8101 20.4603H16.1101C15.9001 20.4603 15.5101 20.6103 15.3301 20.7503L13.7501 22.1003C13.2601 22.5303 12.6301 22.7403 12.0101 22.7403ZM6.17012 5.02031C5.52012 5.02031 4.99012 5.55031 4.99012 6.20031V7.90031C4.99012 8.47031 4.73012 9.19031 4.36012 9.63031L3.00012 11.2303C2.66012 11.6403 2.66012 12.3603 3.00012 12.7603L4.35012 14.3503C4.71012 14.7603 4.98012 15.5103 4.98012 16.0803V17.7903C4.98012 18.4403 5.51012 18.9703 6.16012 18.9703H7.90012C8.46012 18.9703 9.20012 19.2403 9.64012 19.6103L11.2301 20.9703C11.6401 21.3203 12.3501 21.3203 12.7701 20.9703L14.3501 19.6203C14.8001 19.2403 15.5301 18.9803 16.0901 18.9803H17.7901C18.4401 18.9803 18.9701 18.4503 18.9701 17.8003V16.1003C18.9701 15.5403 19.2401 14.8103 19.6101 14.3603L20.9701 12.7703C21.3201 12.3603 21.3201 11.6403 20.9701 11.2303L19.6201 9.65031C19.2401 9.20031 18.9801 8.47031 18.9801 7.91031V6.20031C18.9801 5.55031 18.4501 5.02031 17.8001 5.02031H16.1001C15.5301 5.02031 14.7901 4.75031 14.3501 4.38031L12.7601 3.02031C12.3501 2.67031 11.6401 2.67031 11.2201 3.02031L9.65012 4.38031C9.20012 4.75031 8.47012 5.02031 7.90012 5.02031H6.17012Z" fill={color} />
      <path d="M14.8096 16.2398L11.9996 9.90977L9.18957 16.2398L7.80957 15.6298L11.3096 7.75977H12.6896L16.1896 15.6298L14.8096 16.2398Z" fill={color} />
      <path d="M13.75 14.0596H10.25C9.84 14.0596 9.5 13.7196 9.5 13.3096C9.5 12.8996 9.84 12.5596 10.25 12.5596H13.75C14.16 12.5596 14.5 12.8996 14.5 13.3096C14.5 13.7196 14.16 14.0596 13.75 14.0596Z" fill={color} />
    </svg>
  );
}

interface IconGeneralTermProps {
  color?: string;
  width?: number;
  height?: number;
}

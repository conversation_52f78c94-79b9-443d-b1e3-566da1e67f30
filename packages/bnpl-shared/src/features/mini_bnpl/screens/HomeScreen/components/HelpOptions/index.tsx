import { View } from 'react-native';
import React from 'react';
import { StyleSheet } from 'bnpl-shared/src/shared/styles/StyleSheet';
import { AppColors } from 'bnpl-shared/src/constants';
import HelpOptionsItem from './HelpOptionsItem';
import { GeneralBankcardSecondary, GeneralQuestionLineSecondary, GeneralTransactionSecondary } from '@zpi/looknfeel-icons';
import IconGeneralTerm from './icons/IconGeneralTerm';
import IconGeneralBuy from './icons/IconGeneralBuy';
import * as Tracking from '../../tracking';

export default function HelpOptions() {
  return (
    <View style={styles.container}>
      {dataLotte.map((item, index) => (
        <HelpOptionsItem
          key={item.tag}
          tag={item.tag} title={item.title}
          icon={item.icon}
          lastItem={index === dataLotte.length - 1}
          onItemClick={(tag) => {
            Tracking.clickHelpOption({ option: tag });
            // WIP
          }}
        />
      ))}
    </View>
  );
};

interface HelpOption {
  testId?: string;
  title: string;
  icon: React.ReactNode;
  tag: string;
}

const dataLotte: HelpOption[] = [
  { title: 'Đăng ký tài khoản', icon: <GeneralTransactionSecondary color={AppColors.blue["500"]} />, tag: 'account' },
  { title: 'Mua sắm', icon: <GeneralBankcardSecondary color={AppColors.blue["500"]} />, tag: 'purchase' },
  { title: 'Thanh toán khoản đến hạn', icon: <IconGeneralBuy color={AppColors.blue["500"]} width={24} height={24} />, tag: 'repay' },
  { title: 'Thuật ngữ', icon: <IconGeneralTerm color={AppColors.blue["500"]} width={24} height={24} />, tag: 'term' },
  { title: 'Câu hỏi thường gặp', icon: <GeneralQuestionLineSecondary color={AppColors.blue["500"]} />, tag: 'faq' },
];

const styles = StyleSheet.create({
  container: {
    flexDirection: 'column',
    backgroundColor: AppColors.white,
    borderRadius: 12,
    borderWidth: 1,
    borderColor: AppColors.stroke,
    paddingHorizontal: 16,
    borderTopWidth: 1,
    borderTopColor: AppColors.stroke,
  },
  ic: { width: 40, height: 40, marginBottom: 12 },
});

import React, { useEffect, useState } from 'react';
import { useUtmHandler } from 'bnpl-shared/src/hooks/useUtmHandler';
import { isNavigationDeeplink, useDeeplinkHandler } from 'bnpl-shared/src/deeplink';
import { usePartnerUserData } from './usePartnerUserData';
import { useNavigationContext } from 'bnpl-shared/src/components/NavigationWrapper';
import { PartnerCode, ScreenKey } from 'bnpl-shared/src/constants';
import LinearGradient from 'react-native-linear-gradient';
import { StyleSheet } from 'bnpl-shared/src/shared/styles/StyleSheet';
import { View } from 'react-native';
import { Spacer } from 'bnpl-shared/src/components/Spacer';
import SectionHeader from '../../components/SectionHeader';
import SuggestedServices from '../../components/SuggestedServices';
import { useHomePreviewController } from 'bnpl-shared/src/utils/useHomePreviewController';
import HelpOptions from './components/HelpOptions';
import BalanceSummary from './components/BalanceSummary';
import BuyTutorialButton from './components/BuyTutorialButton';
import * as Tracking from './tracking';
import { useHistory } from 'react-router-dom';
import { RepaymentWidget } from 'bnpl-shared/src/components/RepaymentWidget';
import ProviderStatement from '../../components/ProviderStatement';

export default function MiniBNPLHomeScreenView(props: any) {
  const history = useHistory();
  const { utmCampaign, markUtmCampaignUsed } = useUtmHandler();
  const { handleDeeplink } = useDeeplinkHandler(props);
  const { isPreviewMode } = useHomePreviewController(PartnerCode.MINI_BNPL);
  const { state: navigationState } = useNavigationContext();
  const { dispatch: dispatchNavigation } = useNavigationContext();
  useEffect(() => {
    Tracking.load({ accountState: 'active' });
    dispatchNavigation({
      type: 'SET_TITLE',
      payload: 'Tài khoản trả sau',
    });
    dispatchNavigation({
      type: 'SET_CUSTOM_NAVIGATION_OPTIONS',
      payload: {
        path: ScreenKey.MPHomeScreen,
        options: {
          background: 'transparent',
          color: 'black',
          overlappingPageContent: true,
          scrollMask: {
            enable: true,
            background: 'white',
            fullHeightNavigationBar: true,
          },
        },
      },
    });
  }, []);

  useEffect(() => {
    if (utmCampaign && isNavigationDeeplink(utmCampaign)) {
      handleDeeplink(utmCampaign);
      markUtmCampaignUsed();
    }
  }, [utmCampaign]);

  return (
    <View style={styles.container}>
      <LinearGradient style={[styles.headerGradient, !navigationState.fullScreenData?.fullScreen && styles.headerGradientNonFullScreen]} colors={['rgba(213, 253, 237, 1)', 'rgba(213, 253, 237, 0)']} />
      <View style={[styles.paddingHorizontal, styles.balanceSummaryWrapper]}>
        <BalanceSummary isPreviewMode={isPreviewMode} />
        <Spacer height={12} />
      </View>
      <View style={styles.paddingHorizontal}>
        <SectionHeader
          title="Quản lý tài khoản trả sau"
          cta={{
            label: 'Xem tất cả',
            onClick: () => {
              Tracking.clickSeeAllBills({ accountState: 'active' });
              history.push(`/${ScreenKey.MiniRepaymentHistory}?tab=unpaid`);
            }
          }}
        />
        <Spacer height={12} />
        <RepaymentWidget partnerCode={PartnerCode.MINI_BNPL} isPreviewMode={isPreviewMode} />
      </View>
      <View style={styles.paddingHorizontal}>
        <SectionHeader title="Dịch vụ hỗ trợ thanh toán" />
        <Spacer height={12} />
        <SuggestedServices
          trackEventClickItem={(tag) => {
            Tracking.clickSuggestedService({ iconName: tag });
          }}
        />
        <Spacer height={11} />
        <BuyTutorialButton />
      </View>
      <Spacer height={11} />
      <View style={styles.paddingHorizontal}>
        <SectionHeader title="Bạn cần trợ giúp?" />
        <Spacer height={12} />
        <HelpOptions />
      </View>
      <ProviderStatement />
    </View>
  )
}

const styles = StyleSheet.create({
  container: {
    flexDirection: 'column',
  },
  balanceSummaryWrapper: {
    marginTop: -118,
  },
  paddingHorizontal: {
    paddingHorizontal: 16,
  },
  headerGradient: {
    width: 375,
    height: 234,
  },
  headerGradientNonFullScreen: {
    marginTop: -104,
  },
});

import { trackEvent } from 'bnpl-shared/src/utils/tracking/trackEvent';

type AccountState = 'active' | 'locked' | 'terminated';

export function load(params: { accountState: AccountState }) {
  // WIP
  trackEvent('6748.000', { account_state: params.accountState })
}

export function clickBalanceSummaryTooltip(params: { accountState: AccountState }) {
  // WIP
  trackEvent('6748.001', { account_state: params.accountState })
}

export function clickSeeAllBills(params: { accountState: AccountState }) {
  // WIP
  trackEvent('6748.002', { account_state: params.accountState })
}

export function loadRepaymentWidget(params: { accountState: AccountState, reminderState: 'no_due' | 'due' | 'overdue' }) {
  // WIP
  trackEvent('6748.003', { account_state: params.accountState, reminder_state: params.reminderState })
}

export function clickRepaymentWidgetCTA(params: { accountState: AccountState, reminderState: 'no_due' | 'due' | 'overdue' }) {
  // WIP
  trackEvent('6748.004', { account_state: params.accountState, reminder_state: params.reminderState })
}

export function clickSuggestedService(params: { iconName: string }) {
  trackEvent('6748.005', { icon_name: params.iconName })
}

export function clickBuyTutorialButton() {
  trackEvent('6748.006')
}

export function clickHelpOption(params: { option: string }) {
  trackEvent('6748.007', { icon_name: params.option })
}

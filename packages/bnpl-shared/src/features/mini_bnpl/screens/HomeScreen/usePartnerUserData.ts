import { getAccountInfoApi } from 'bnpl-shared/src/api/partner_lotte/getAccountInfoApi';
import { INVALID_ACCOUNT_ID, PartnerCode } from 'bnpl-shared/src/constants';
import { usePartnerData } from 'bnpl-shared/src/features/multipartner_integration/helpers';
import { HomeScreenControllerFactory } from 'bnpl-shared/src/features/multipartner_integration/screens/HomeScreen/models';
import { useUserBalanceController } from 'bnpl-shared/src/utils/useUserBalanceController';
import { useCallback, useEffect, useState } from 'react';

export const usePartnerUserData = () => {
  const { getChosenPartner } = usePartnerData();
  const chosenPartner = getChosenPartner();
  const homeScreenController = HomeScreenControllerFactory.get(PartnerCode.MINI_BNPL);
  const { fetchUserBalance } = useUserBalanceController();
  const [refreshing, setRefreshing] = useState(false);

  const request = useCallback(async (accountID: string) => {
    if (accountID && accountID !== INVALID_ACCOUNT_ID) {
      try {
        setRefreshing(true);
        fetchUserBalance(accountID);
        homeScreenController.fetchStatement(accountID);
        await getAccountInfoApi(accountID);
      } finally {
        setRefreshing(false);
      }

    }
  }, []);

  useEffect(() => {
    request(chosenPartner?.account_id || '');
  }, [chosenPartner?.account_id]);

  return {
    refreshing,
    onRequestRefreshData: request,
  }
}
import React from 'react';
import { calculateScaleDimension, StyleSheet } from 'bnpl-shared/src/shared/styles/StyleSheet';
import { TouchableOpacity, View } from 'react-native';
import { AppImage, AppText } from 'bnpl-shared/src/shared/react-native-customized';
import { AppColors } from 'bnpl-shared/src/constants';
import LinearGradient from 'react-native-linear-gradient';
import SelectedTriangleShape from './Triangle';

export default function IntroBottomSheetMenuItem({ title, image, selected, onClick }: IntroBottomSheetMenuItemProps) {
  return (
    <TouchableOpacity style={styles.touchable} onPress={onClick}>
      <View style={[styles.container, selected && styles.selectedContainer]}>
        <AppText size={12} height={16} bold style={styles.title}>{title}</AppText>
        <AppImage source={image} width={90} height={52} />
        {selected && <LinearGradient colors={['#E1F3FF', '#DCFDFA']} style={styles.gradient} />}
      </View>
      {selected && <SelectedTriangleShape width={calculateScaleDimension(28)} height={calculateScaleDimension(20)} style={styles.triangle} />}
    </TouchableOpacity>
  )
}

interface IntroBottomSheetMenuItemProps {
  title: string;
  image: string;
  selected: boolean;
  onClick: () => void;
}

const styles = StyleSheet.create({
  touchable: {
    flex: 1,
    alignItems: 'center',
    padding: 16,
  },
  container: {
    flex: 1,
    flexDirection: 'column',
    alignItems: 'flex-start',
    padding: 8,
    borderRadius: 8,
    borderWidth: 1,
    borderColor: '#F2F6F7',
    overflow: 'hidden',
  },
  selectedContainer: {
    borderWidth: 1,
    borderColor: 'transparent',
  },
  title: {
    height: 32,
    marginBottom: 12,
  },
  gradient: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    zIndex: -1,
  },
    triangle: {
    marginTop: -7,
  }
});

// PolygonShape.tsx
import React from 'react';
import { StyleProp, ViewStyle } from 'react-native';
import Svg, { Path } from 'react-native-svg';

interface PolygonShapeProps {
  width?: number;
  height?: number;
  style?: StyleProp<ViewStyle>;
}

export default function SelectedTriangleShape({ width = 24, height = 20, style }: PolygonShapeProps) {
  return (
    <Svg
      width={width}
      height={height}
      viewBox="0 0 21 14"
      fill="none"
      style={style}
    >
      <Path
        d="M12.0554 13.0757C11.2549 14.066 9.74506 14.066 8.94457 13.0757L1.00844 3.25724C-0.0487106 1.94936 0.882165 -1.57388e-07 2.56387 -1.03693e-08L18.4361 1.37723e-06C20.1178 1.52425e-06 21.0487 1.94936 19.9916 3.25724L12.0554 13.0757Z"
        fill="#DDFDFB"
      />
    </Svg>
  );
};

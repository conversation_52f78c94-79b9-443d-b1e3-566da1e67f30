import React, { useEffect, useState } from 'react';
import { BottomSheetLayout } from 'bnpl-shared/src/shared/BottomSheet/BottomSheetLayout';
import { AppImage, AppModal, AppText } from 'bnpl-shared/src/shared/react-native-customized';
import { TouchableOpacity, View } from 'react-native';
import { StyleSheet } from 'bnpl-shared/src/shared/styles/StyleSheet';
import IntroBottomSheetMenuItem from './IntroBottomSheetMenuItem';
import { images } from 'bnpl-shared/src/res';
import { Spacer } from 'bnpl-shared/src/components/Spacer';
import LinearGradient from 'react-native-linear-gradient';
import ProviderStatement from '../../../../components/ProviderStatement';
import { AppColors } from 'bnpl-shared/src/constants';
import StellaGradientButton from '../../../../components/StellaGradientButton';
import { useMiniBnplOnboardingScreenContext } from '../../context';
import PayTutorialBottomSheet from 'bnpl-shared/src/features/mini_bnpl/components/PayTutorialBottomSheet/PayTutorialContent';
import RepayTutorialBottomSheet from 'bnpl-shared/src/features/mini_bnpl/components/RepayTutorialBottomSheet/RepayTutorialContent';
import { postSubmitOnboardingApi } from 'bnpl-shared/src/api/mini-bnpl';
import { toast } from 'bnpl-shared/src/components/Toaster';

export default function IntroBottomSheet({ visible, onRequestClose }: IntroBottomSheetProps) {
  const { verifyStep } = useMiniBnplOnboardingScreenContext();
  const [innerContentType, setInnerContentType] = useState<'default' | 'pay' | 'repay'>('default');
  const [selectedFeature, setSelectedFeature] = useState<FeatureKey>(ListFeatures[0]);
  useEffect(() => {
    visible && setSelectedFeature(ListFeatures[0]);
  }, [visible]);

  function renderContent() {
    switch (selectedFeature) {
      case 'feat_0':
        return <AppImage source={images.MiniBNPLIntroNoFeeNoInterestContent} height={234} width={311} />;
      case 'feat_1':
        return (
          <>
            <AppImage source={images.MiniBNPLIntroBuyNowPayLaterContent} height={208} width={311} />
            <TextLinkButton
              onClick={() => {
                setInnerContentType('repay');
              }}>
              Hướng dẫn thanh toán khoản đến hạn
            </TextLinkButton>
          </>
        );
      case 'feat_2':
        return (
          <>
            <AppImage source={images.MiniBNPLIntroMultipleServicesContent} height={204} width={311} />
            <TextLinkButton
              onClick={() => {
                setInnerContentType('pay');
              }}>
              Hướng dẫn mua sắm
            </TextLinkButton>
          </>
        );
      default:
        return null;
    }
  }

  return (
    <AppModal transparent visible={visible} onRequestClose={onRequestClose}>
      {innerContentType === 'default' && (
        <BottomSheetLayout
          title="Giới thiệu về Tài khoản trả sau"
          onRequestClose={onRequestClose}
          content={
            <View style={styles.container}>
              <View style={styles.menu}>
                <IntroBottomSheetMenuItem
                  {...flattenItemProps('feat_0', selectedFeature)}
                  onClick={() => setSelectedFeature('feat_0')}
                />
                <Spacer width={12} />
                <IntroBottomSheetMenuItem
                  {...flattenItemProps('feat_1', selectedFeature)}
                  onClick={() => setSelectedFeature('feat_1')}
                />
                <Spacer width={12} />
                <IntroBottomSheetMenuItem
                  {...flattenItemProps('feat_2', selectedFeature)}
                  onClick={() => setSelectedFeature('feat_2')}
                />
              </View>
              <View style={styles.contentContainer}>
                <LinearGradient
                  colors={['#ECFFFE', '#F5FFFE']}
                  start={{ x: 0.5, y: 0 }}
                  end={{ x: 0.5, y: 1 }}
                  style={styles.gradientContent}
                />
                {renderContent()}
              </View>
              <ProviderStatement style={styles.providerStatement} />
              <StellaGradientButton
                onClick={() => {
                  postSubmitOnboardingApi()
                    .then(res => {
                      onRequestClose();
                      setTimeout(() => {
                        verifyStep(res.onboarding_next_step);
                      }, 100);
                    })
                    .catch(err => {
                      onRequestClose();
                      toast.error(err?.message || 'Có lỗi xảy ra, vui lòng thử lại sau.', { duration: 3000 });
                      console.log('submit onboarding error', err);
                    });
                }}>
                Tiếp tục
              </StellaGradientButton>
            </View>
          }
        />
      )}
      {innerContentType === 'pay' && <PayTutorialBottomSheet onRequestClose={() => setInnerContentType('default')} />}
      {innerContentType === 'repay' && (
        <RepayTutorialBottomSheet onRequestClose={() => setInnerContentType('default')} />
      )}
    </AppModal>
  );
}

function TextLinkButton({ children, onClick }: { children: string; onClick: () => void }) {
  return (
    <TouchableOpacity style={styles.textLinkButton} onPress={onClick}>
      <AppText size={14} height={18} color={AppColors.primary} bold style={styles.textLinkButtonText}>
        {children}
      </AppText>
      <AppImage source={images.IconArrowRight} width={16} height={16} />
    </TouchableOpacity>
  );
}

function flattenItemProps(featKey: FeatureKey, selectedKey: FeatureKey) {
  return {
    ...FEATURES[featKey],
    selected: selectedKey === featKey,
  };
}

interface IntroBottomSheetProps {
  visible: boolean;
  onRequestClose: () => void;
}

type FeatureKey = 'feat_0' | 'feat_1' | 'feat_2';
const ListFeatures: FeatureKey[] = ['feat_0', 'feat_1', 'feat_2'];

const FEATURES: Record<FeatureKey, { title: string; image: string }> = {
  ['feat_0']: {
    title: '0 phí, 0 lãi',
    image: images.MiniBNPLIntroNoFeeNoInterest,
  },
  ['feat_1']: {
    title: 'Dùng trước, trả sau 37 ngày',
    image: images.MiniBNPLIntroBuyNowPayLater,
  },
  ['feat_2']: {
    title: 'Đa dạng dịch vụ thanh toán',
    image: images.MiniBNPLIntroMultipleServices,
  },
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    flexDirection: 'column',
    paddingHorizontal: 16,
    paddingBottom: 16,
  },
  menu: {
    flexDirection: 'row',
    alignItems: 'flex-start',
  },
  contentContainer: {
    height: 266,
    width: 343,
    paddingHorizontal: 16,
    position: 'relative',
    justifyContent: 'center',
  },
  gradientContent: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    zIndex: -1,
  },
  providerStatement: {
    paddingBottom: 16,
  },
  textLinkButton: {
    marginTop: 12,
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
  },
  textLinkButtonText: {
    marginRight: 4,
  },
});

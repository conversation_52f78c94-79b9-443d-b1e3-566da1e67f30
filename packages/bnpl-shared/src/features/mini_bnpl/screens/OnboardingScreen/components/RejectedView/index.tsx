import React from 'react';
import { StyleSheet } from 'bnpl-shared/src/shared/styles/StyleSheet';
import { Linking, View } from 'react-native';
import { AppImage, AppText, AppButton } from 'bnpl-shared/src/shared/react-native-customized';
import { images } from 'bnpl-shared/src/res';
import { AppColors } from 'bnpl-shared/src/constants';
import { Spacer } from 'bnpl-shared/src/components/Spacer';
import { closeMiniApp } from 'bnpl-shared/src/utils/closeMiniApp';
import { ZLP_SUPPORT_TEL } from 'bnpl-shared/src/constants/PublicUrls';
import { useNavigationContext } from 'bnpl-shared/src/components/NavigationWrapper';
import { useMiniBnplOnboardingScreenContext } from '../../context';
import * as Tracking from '../../tracking';
import { openLink } from 'bnpl-shared/src/utils/openLink';

export default function RejectedView() {
  const { state: navigationState } = useNavigationContext();
  const { currentStep } = useMiniBnplOnboardingScreenContext();
  return (
    <View style={[styles.container, navigationState.fullScreenData?.fullScreen && styles.fullScreenContainer]}>
      <View style={styles.mainContent}>
        <AppImage source={images.MiniBNPLAccountRejected} width={200} height={200} />
        <Spacer height={20} />
        <AppText size={16} height={20} color={AppColors.dark[500]} style={styles.label}>Zalopay chưa thể cung cấp dịch vụ cho bạn</AppText>
        <Spacer height={12} />
        <AppText size={12} height={16} color={AppColors.dark[300]} style={styles.label}>Zalopay sẽ thông báo ngay khi bạn thoả điều kiện sử dụng dịch vụ. Vui lòng liên hệ Trung tâm trợ giúp của Zalopay nếu cần biết thêm chi tiết.</AppText>
      </View>
      <View style={styles.bottomView}>
        <AppButton
          title="Về trang chủ"
          buttonStyle={styles.primaryCTA}
          onPress={() => {
            Tracking.clickRejectedCTA({ currentStep: currentStep, buttonName: 'main_cta' });
            closeMiniApp();
          }}
        />
        <AppButton
          title="Gọi CSKH"
          variant="naked"
          onPress={async () => {
            Tracking.clickRejectedCTA({ currentStep: currentStep, buttonName: 'sub_cta' });
            openLink(`tel:${ZLP_SUPPORT_TEL}`);
          }}
        />
      </View>
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    paddingTop: 64,
  },
  fullScreenContainer: {
    paddingTop: 168,
    paddingBottom: 16,
  },
  mainContent: {
    flex: 1,
    alignItems: 'center',
    paddingHorizontal: 16,
  },
  label: {
    textAlign: 'center',
  },
  bottomView: {
    padding: 16,
    width: '100%',
    alignItems: 'center',
  },
  primaryCTA: {
    width: '100%',
  }
});

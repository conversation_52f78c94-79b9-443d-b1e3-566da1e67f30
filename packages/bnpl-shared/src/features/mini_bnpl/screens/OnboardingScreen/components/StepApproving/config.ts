import { MiniBNPLOnboardingNextStep } from 'bnpl-shared/src/types';

export const themes = {
  [MiniBNPLOnboardingNextStep.QUERY_STATUS as string]: {
    lottieAnimation: { uri: 'https://simg.zalopay.com.vn/fs/bnpl/animation/status-waiting.json' },
    image: undefined,
    gradientColors: ['#FFFAE9', '#FFF1D0'],
    animationStyle: {
      width: 200,
      height: 200,
    },
    title: '<PERSON>ồ sơ đang được duyệt',
    descriptions: 'Bạn sẽ nhận được thông báo khi có kết quả.',
    buttonText: '<PERSON><PERSON><PERSON> hình ch<PERSON>h',
  },
};

import React from 'react';
import { AppImage, AppText } from 'bnpl-shared/src/shared/react-native-customized';
import { View } from 'react-native';
import { images } from 'bnpl-shared/src/res';
import { StyleSheet } from 'bnpl-shared/src/shared/styles/StyleSheet';
import { AppColors } from 'bnpl-shared/src/constants';

export default function Benefits() {
  return (
    <View style={styles.container}>
      <Item image={images.MiniBNPLBenefitPay37Days} text={'Thanh toán sau\n37 ngày'} />
      <Item image={images.MiniBNPLBenefitFeeFree} text={'Miễn phí\nduy trì'} />
      <Item image={images.MiniBNPLBenefitEasy3Minutes} text={'Đăng ký\ndễ dàng'} />
    </View>
  );
}

function Item(props: { image: string, text: string }) {
  return (
    <View style={styles.item}>
      <AppImage source={props.image} width={30} height={30} />
      <AppText style={{ textAlign: 'center' }} size={14} height={18} color={AppColors.dark[500]}>{props.text}</AppText>
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    width: '100%',
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    padding: 16,
  },
  item: {
    flex: 1,
    alignItems: 'center',
    justifyContent: 'center',
  },
});

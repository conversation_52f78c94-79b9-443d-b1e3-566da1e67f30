import React, { useEffect, useState } from 'react';
import { View, ScrollView, TouchableOpacity } from 'react-native';
import { WebView } from 'react-native-webview';
import { AppImage, AppModal, AppText } from 'bnpl-shared/src/shared/react-native-customized';
import { images } from 'bnpl-shared/src/res';
import { calculateScaleDimension, StyleSheet } from 'bnpl-shared/src/shared/styles/StyleSheet';
import { AppColors } from 'bnpl-shared/src/constants';
import ProviderStatement from 'bnpl-shared/src/features/mini_bnpl/components/ProviderStatement';
import { useNavigationContext } from 'bnpl-shared/src/components/NavigationWrapper';
import Benefits from './Benefits';
import { useMiniBnplOnboardingScreenContext } from '../../context';
import { LinkButton } from 'bnpl-shared/src/shared/LinkButton';
import { BottomSheetLayout } from 'bnpl-shared/src/shared/BottomSheet/BottomSheetLayout';
import { avoidCacheImageUrl } from 'bnpl-shared/src/shared/utils';
import IntroBottomSheet from 'bnpl-shared/src/features/mini_bnpl/screens/OnboardingScreen/components/IntroBottomSheet';
import StellaGradientButton from 'bnpl-shared/src/features/mini_bnpl/components/StellaGradientButton';
import * as Tracking from '../../tracking';
import { postSubmitOnboardingApi } from 'bnpl-shared/src/api/mini-bnpl';
import { getOnboardingInfoApi } from 'bnpl-shared/src/api/mini-bnpl/getOnboardingInfoApi';
import { MiniBnplOnboardingInfo } from 'bnpl-shared/src/types';
import { Skeleton } from 'bnpl-shared/src/shared/animated-wrappers/Skeleton';
import { toast } from 'bnpl-shared/src/components/Toaster';
import { hideLoading, showLoading } from 'bnpl-shared/src/shared/ZaloPayModules';
import { windowHeight } from 'bnpl-shared/src/shared/styles/StyleUtils';

export default function StepSubmit() {
  const [contractVisible, setContractVisible] = useState(false);
  const [introBottomSheetVisible, setIntroBottomSheetVisible] = useState(false);
  const { state: navigationState } = useNavigationContext();
  const { currentStep, verifyStep } = useMiniBnplOnboardingScreenContext();
  const [onboardingInfo, setOnboardingInfo] = useState<MiniBnplOnboardingInfo | null>(null);
  useEffect(() => {
    (async () => {
      try {
        const res = await getOnboardingInfoApi();
        showLoading();
        setOnboardingInfo({
          fullName: res.full_name,
          phone: res.phone,
          permanentAddress: res.permanent_address,
        });
      } catch (err) {
        setOnboardingInfo(null);
        toast.error('Có lỗi xảy ra, vui lòng thử lại sau.', { duration: 3000 });
        console.log('get onboarding info error', err);
      } finally { 
        hideLoading();
      }
    })();
  }, []);

  useEffect(() => {
    if (contractVisible) {
      Tracking.loadDemoContractView({ currentStep });
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [contractVisible]);

  return (
    <View style={[styles.container, navigationState.fullScreenData?.fullScreen && styles.fullScreen]}>
      <ScrollView showsVerticalScrollIndicator={false}>
        <AppImage style={styles.headerBg} source={images.BackgroundMiniBNPLOnboardingStepRegister} width={375} height={390} />
        <View style={[styles.header, navigationState.fullScreenData?.fullScreen && styles.fullScreenHeader]}>
          <AppImage source={images.LogoMiniBNPLGradient} width={100} height={100} />
          <AppText size={18} height={28} bold style={styles.headerTitle}>
            {'Lợi ích khi sử dụng\nTài khoản trả sau Zalopay'}
          </AppText>
          <Benefits />
        </View>
        <View style={styles.body}>
          <View style={styles.mainContent}>
            <AppText size={16} height={20} bold>Thông tin đăng ký</AppText>
            <View style={styles.infoContainer}>
              {onboardingInfo ? (
                <>
                  <InfoItem label="Họ và tên" value={onboardingInfo?.fullName || '--'} />
                  <InfoItem label="Số điện thoại" value={onboardingInfo?.phone || '--'} />
                  <InfoItem label="Địa chỉ" value={onboardingInfo?.permanentAddress || '--'} />
                </>
              ) : (
                <>
                  <Skeleton width={calculateScaleDimension(311)} height={calculateScaleDimension(50)} />
                  <Skeleton width={calculateScaleDimension(311)} height={calculateScaleDimension(50)} />
                  <Skeleton width={calculateScaleDimension(311)} height={calculateScaleDimension(50)} />
                </>
              )}
            </View>
            <AppText>
              Bằng cách nhấn “Tiếp tục”, tôi xác nhận đã đọc, đồng ý với nội dung{' '}
              <LinkButton
                onPress={() => {
                  Tracking.clickDemoContract({ currentStep });
                  setContractVisible(true);
                }}>
                Hợp đồng mẫu
              </LinkButton>{' '}
              và{' '}
              <LinkButton
                onPress={() => {
                  // WIP: handle launch web view
                  Tracking.clickLandingTnc({ currentStep });
                }}>
                Chính sách bảo vệ quyền riêng tư của Zalopay
              </LinkButton>{' '}
            </AppText>
          </View>
          <ProviderStatement />
        </View>
      </ScrollView>
      <View style={styles.bottomContainer}>
        <StellaGradientButton onClick={() => {
          Tracking.clickLandingPageCTA({ currentStep });
          showLoading();
          postSubmitOnboardingApi().then((res) => {
            verifyStep(res.onboarding_next_step);
          }).catch((err) => {
            console.log("submit onboarding error", err);
            toast.error(err?.message || 'Có lỗi xảy ra, vui lòng thử lại sau.', { duration: 3000 });
          }).finally(() => {
            hideLoading();
          });
        }}>
          Đăng ký
        </StellaGradientButton>
        <TouchableOpacity
          activeOpacity={0.8}
          style={styles.secondaryCTA}
          onPress={() => {
            Tracking.clickDiscoverMore({ currentStep });
            setIntroBottomSheetVisible(true);
          }}
        >
          <AppText size={16} height={20} bold color={AppColors.blue[500]}>Khám phá thêm</AppText>
        </TouchableOpacity>
      </View>
      <AppModal transparent visible={contractVisible} onRequestClose={() => setContractVisible(false)}>
        <BottomSheetLayout
          title={'Xem hợp đồng'}
          onRequestClose={() => setContractVisible(false)}
          content={
            <View
              style={[
                styles.contractBody,
                navigationState.fullScreenData?.fullScreen && styles.contractBodyFullScreen,
              ]}>
              <WebView
                // WIP
                source={{ uri: avoidCacheImageUrl('https://simg.zalopay.com.vn/fs/bnpl/document/lotte_contract.html') }}
              />
            </View>
          }
        />
      </AppModal>
      <IntroBottomSheet
        visible={introBottomSheetVisible}
        onRequestClose={() => setIntroBottomSheetVisible(false)}
      />
    </View>
  )
}

function InfoItem(props: { label: string, value: string }) {
  return (
    <View style={styles.contentRow}>
      <AppText size={14} height={18} color={AppColors.dark[300]} style={styles.infoLabel}>{props.label}</AppText>
      <AppText size={14} height={18} color={AppColors.dark[500]} style={styles.infoValue}>{props.value}</AppText>
    </View>
  )
}
const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  fullScreen: {
    paddingBottom: 8,
  },
  headerBg: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
  },
  headerTitle: {
    marginTop: 2,
    zIndex: 2,
    textAlign: 'center',
  },
  header: {
    width: 375,
    height: 346,
    paddingTop: 56,
    position: 'relative',
    alignItems: 'center',
    paddingHorizontal: 16,
  },
  fullScreenHeader: {
    height: 390,
    paddingTop: 100,
  },
  body: {
    width: '100%',
    backgroundColor: AppColors.white,
    padding: 16,
  },
  mainContent: {
    borderRadius: 12,
    paddingTop: 12,
    paddingHorizontal: 16,
    paddingBottom: 24,
    borderWidth: 1,
    borderColor: AppColors.blue[25],
  },
  infoContainer: {
    marginTop: 12,
  },
  contentRow: {
    flexDirection: 'row',
    alignItems: 'flex-start',
    justifyContent: 'space-between',
    minHeight: 50,
    paddingVertical: 16,
  },
  infoLabel: {
    width: '40%',
    textAlign: 'left',
  },
  infoValue: {
    width: '60%',
    textAlign: 'right',
  },
  bottomContainer: {
    padding: 16,
    backgroundColor: AppColors.white,
  },
  mainCTA: {
    height: 48,
    width: '100%',
    borderRadius: 24,
    backgroundColor: AppColors.blue[500],
    position: 'relative',
    overflow: 'hidden',
    alignItems: 'center',
    justifyContent: 'center',
  },
  mainCTAContent: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
  },
  mainCTAText: {
    marginLeft: 10,
  },
  secondaryCTA: {
    height: 48,
    paddingHorizontal: 20,
    alignItems: 'center',
    justifyContent: 'center',
  },
  ctaGradient: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
  },
  contractBody: {
    height: windowHeight * 0.75,
  },
  contractBodyFullScreen: {
    height: windowHeight * 0.65,
  },
});

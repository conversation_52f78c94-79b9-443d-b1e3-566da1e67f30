import React from 'react';
import { View } from 'react-native';
import { AppText } from 'bnpl-shared/src/shared/react-native-customized';
import { StyleSheet } from 'bnpl-shared/src/shared/styles/StyleSheet';
import { AppColors } from 'bnpl-shared/src/constants';
import StepTreeItemLabel from './StepTreeItemLabel';
import { MiniBNPLOnboardingNextStep } from 'bnpl-shared/src/types';

export default function StepTreeItem({
  step,
  lastItem = false,
  doing,
  done,
}: StepTreeItemProps) {
  return (
    <View style={styles.container}>
      <View style={[styles.index, (done || doing) && styles.already]}>
        {done ? (
          <svg width="10" height="8" viewBox="0 0 10 8" fill="none" xmlns="http://www.w3.org/2000/svg">
            <path d="M9.78043 0.640352C10.0704 0.930352 10.0704 1.40035 9.78043 1.70035L4.11043 7.37035C3.97043 7.51035 3.78043 7.59035 3.58043 7.59035C3.38043 7.59035 3.19043 7.51035 3.05043 7.37035L0.22043 4.54035C-0.0695703 4.25035 -0.0695703 3.77035 0.22043 3.48035C0.51043 3.19035 0.99043 3.19035 1.28043 3.48035L3.58043 5.78035L8.72043 0.640352C9.01043 0.350352 9.49043 0.350352 9.78043 0.640352Z" fill="white" />
          </svg>
        ) : (
          <AppText size={10} height={12} color={doing ? AppColors.white : AppColors.dark[500]}>{STEP_CONFIG[step]?.index}</AppText>
        )}
      </View>
      <StepTreeItemLabel visible={doing} label={STEP_CONFIG[step]?.label} />
      {!lastItem && <View style={styles.line} />}
    </View>
  );
}

const STEP_CONFIG = {
  [MiniBNPLOnboardingNextStep.ONBOARDING as string]: {
    index: '1',
    label: 'Đăng ký trả sau',
  },
  [MiniBNPLOnboardingNextStep.VERIFY_FACE as string]: {
    index: '2',
    label: 'Xác thực',
  },
  [MiniBNPLOnboardingNextStep.QUERY_STATUS as string]: {
    index: '3',
    label: 'Duyệt hồ sơ',
  }
};

interface StepTreeItemProps {
  doing: boolean;
  done: boolean;
  step: MiniBNPLOnboardingNextStep;
  lastItem?: boolean;
}

const styles = StyleSheet.create({
  container: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  index: {
    width: 20,
    height: 20,
    backgroundColor: AppColors.white,
    borderRadius: 10,
    borderWidth: 1,
    borderColor: AppColors.blue[50],
    alignItems: 'center',
    justifyContent: 'center',
  },
  already: {
    backgroundColor: AppColors.blue[500],
  },
  line: {
    width: 16,
    height: 1,
    backgroundColor: AppColors.blue[50],
  },
  label: {
    marginLeft: 8,
    marginRight: 2,
  },
});

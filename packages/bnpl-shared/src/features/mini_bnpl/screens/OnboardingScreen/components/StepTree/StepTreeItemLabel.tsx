import React, { useEffect, useRef, useState } from 'react';
import { Animated, ViewStyle } from 'react-native';
import { calculateScaleDimension, StyleSheet } from 'bnpl-shared/src/shared/styles/StyleSheet';
import { AppColors } from 'bnpl-shared/src/constants';
import { AppText } from 'bnpl-shared/src/shared/react-native-customized';

export default function StepTreeItemLabel({ visible, label, style }: StepTreeItemLabelProps) {
  const [mounted, setMounted] = useState(visible);
  const opacity = useRef(new Animated.Value(0)).current;
  const maxWidth = useRef(new Animated.Value(0)).current;

  useEffect(() => {
    if (visible) {
      setMounted(true);
      Animated.parallel([
        Animated.timing(opacity, {
          toValue: 1,
          duration: 250,
          useNativeDriver: true,
        }),
        Animated.timing(maxWidth, {
          toValue: calculateScaleDimension(100),
          duration: 250,
          useNativeDriver: true,
        }),
      ]).start();
    } else {
      Animated.parallel([
        Animated.timing(opacity, {
          toValue: 0,
          duration: 250,
          useNativeDriver: true,
        }),
        Animated.timing(maxWidth, {
          toValue: 0,
          duration: 250,
          useNativeDriver: true,
        }),
      ]).start(() => {
        setMounted(false);
      });
    }
  }, [visible]);

  if (!mounted) {
    return null;
  }

  return (
    <>
      <Animated.View
        style={[
          styles.label,
          style,
          {
            opacity,
            maxWidth,
          },
        ]}
      >
        <AppText numberOfLines={1} size={12} height={16} color={AppColors.dark[500]}>{label}</AppText>
      </Animated.View>
    </>
  );
}

interface StepTreeItemLabelProps {
  visible: boolean;
  label: string;
  style?: ViewStyle;
};

const styles = StyleSheet.create({
  label: {
    paddingLeft: 4,
    paddingRight: 2,
    fontSize: 12,
    lineHeight: 16,
    color: AppColors.dark[500],
  },
});

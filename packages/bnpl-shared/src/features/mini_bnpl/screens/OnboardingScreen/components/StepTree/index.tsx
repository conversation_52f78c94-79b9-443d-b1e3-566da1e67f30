import React from 'react';
import { TouchableOpacity, View } from 'react-native';
import GeneralBackIc24 from 'bnpl-shared/src/components/SVGComponents/GeneralBackIc24';
import { StyleSheet } from 'bnpl-shared/src/shared/styles/StyleSheet';
import StepTreeItem from './StepTreeItem';
import { MiniBNPLOnboardingNextStep } from 'bnpl-shared/src/types';
import { useMiniBnplOnboardingScreenContext } from '../../context';
import { AppColors } from 'bnpl-shared/src/constants';

export default function StepTree({ allowGoBack = false, data }: StepTreeViewProps) {
  const { currentStep, verifyStep } = useMiniBnplOnboardingScreenContext();
  if (!currentStep) {
    return null;
  }
  return (
    <>
      <View style={styles.container}>
        {allowGoBack && (
          <TouchableOpacity
            activeOpacity={0.8}
            style={styles.backButton}
            onPress={() => {
              // WIP
              verifyStep(MiniBNPLOnboardingNextStep.ONBOARDING);
            }}
          >
            <GeneralBackIc24 color={AppColors.dark[500]} />
          </TouchableOpacity>
        )}
        {data.map((step, index) => {
          return (
            <StepTreeItem
              key={step}
              step={step}
              doing={step === currentStep}
              done={index < data.indexOf(currentStep)}
              lastItem={index === data.length - 1}
            />
          )
        })}
      </View>
    </>
  );
}

interface StepTreeViewProps {
  allowGoBack: boolean;
  data: MiniBNPLOnboardingNextStep[];
}

const styles = StyleSheet.create({
  container: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  backButton: {
    width: 24,
    height: 24,
    marginRight: 8,
  },
  hidden: {
    position: 'absolute',
    opacity: 0,
  },
});

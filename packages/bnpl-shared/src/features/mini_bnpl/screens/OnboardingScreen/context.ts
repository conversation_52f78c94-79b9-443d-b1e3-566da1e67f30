import { MiniBNPLOnboardingNextStep } from 'bnpl-shared/src/types';
import { createContext, useContext } from 'react';

export const MiniBnplOnboardingScreenContext = createContext<{
  currentStep: MiniBNPLOnboardingNextStep | null;
  verifyStep: (nextStep: MiniBNPLOnboardingNextStep) => void;
}>({
  currentStep: MiniBNPLOnboardingNextStep.ONBOARDING,
  verifyStep: (_nextStep: MiniBNPLOnboardingNextStep) => { },
});

export const useMiniBnplOnboardingScreenContext = () => {
  const context = useContext(MiniBnplOnboardingScreenContext);
  if (!context) {
    throw new Error('useMiniBnplOnboardingScreenContext must be used within a MiniBnplOnboardingScreenContext');
  }
  return context;
};

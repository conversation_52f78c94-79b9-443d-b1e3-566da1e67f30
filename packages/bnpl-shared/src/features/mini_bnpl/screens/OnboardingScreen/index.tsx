import React, { useCallback, useEffect, useMemo, useRef, useState } from 'react';
import { useNavigationContext } from 'bnpl-shared/src/components/NavigationWrapper';
import { ScreenKey } from 'bnpl-shared/src/constants';
import { StyleSheet } from 'bnpl-shared/src/shared/styles/StyleSheet';
import { Animated, View } from 'react-native';
import { MiniBnplOnboardingScreenContext } from './context';
import { MiniBNPLOnboardingNextStep } from 'bnpl-shared/src/types';
import StepTree from './components/StepTree';
import StepSubmit from './components/StepSubmit';
import StepVerifyFace from './components/StepVerifyFace';
import StepApproving from './components/StepApproving';
import * as Tracking from './tracking';
import RejectedView from './components/RejectedView';
import { Skeleton } from 'bnpl-shared/src/shared/animated-wrappers/Skeleton';
import { getOnboardingStatusApi } from 'bnpl-shared/src/api/mini-bnpl';
import { InfoModalService, ModalType } from 'bnpl-shared/src/services';
import OnboardingSuccessPopup from './components/OnboardingSuccessPopup';

const TOTAL_STEPS = 3;

const STEP_TREE_NEXT_STEPS = [
  MiniBNPLOnboardingNextStep.ONBOARDING,
  MiniBNPLOnboardingNextStep.VERIFY_FACE,
  MiniBNPLOnboardingNextStep.QUERY_STATUS,
];

export default function MiniBNPLOnboardingScreen(_props: any) {
  const { navigation } = _props;
  const [currentStep, setCurrentStep] = useState<MiniBNPLOnboardingNextStep | null>(null);
  const { dispatch: dispatchNavigation, state: navigationState } = useNavigationContext();
  const translateX = useRef(new Animated.Value(0)).current;

  const verifyStep = useCallback(
    async (nextStep: MiniBNPLOnboardingNextStep) => {
      const newIndex = STEP_TREE_NEXT_STEPS.indexOf(nextStep);

      if (newIndex >= 0 && newIndex < STEP_TREE_NEXT_STEPS.length) {
        Animated.timing(translateX, {
          toValue: -newIndex * window.screen.width,
          duration: 300,
          useNativeDriver: true,
        }).start();
      }
      setCurrentStep(nextStep);
      switch (nextStep) {
        case MiniBNPLOnboardingNextStep.FINISH:
        case MiniBNPLOnboardingNextStep.HOME:
          navigation.replace(ScreenKey.MPHomeScreen);
          setTimeout(() => {
            Tracking.loadOnboardingSuccessPopup();
            InfoModalService.showModal({
              screen: (
                <OnboardingSuccessPopup
                  onCTAClick={() => {
                    Tracking.clickOnboardingSuccessPopupCTA();
                    InfoModalService.hideModal();
                  }}
                />
              ),
              type: ModalType.MODAL,
              options: {
                transparent: true,
              },
            });
          }, 300);
          break;
        default:
          break;
      }
    },
    [translateX],
  );

  const getCurrentStepIndex = useCallback(() => {
    if (!currentStep) {
      return -1;
    }
    return STEP_TREE_NEXT_STEPS.indexOf(currentStep);
  }, [currentStep]);

  const shouldRenderStep = useCallback(
    (stepIndex: number) => {
      const currentIndex = getCurrentStepIndex();
      if (currentIndex === -1) {
        return false;
      }
      // Render current step and adjacent steps for smooth animation
      return Math.abs(stepIndex - currentIndex) <= 1;
    },
    [getCurrentStepIndex],
  );

  const renderStepContent = useCallback(
    (stepIndex: number) => {
      if (!shouldRenderStep(stepIndex)) {
        return <View style={styles.page} />;
      }

      switch (stepIndex) {
        case 0:
          return (
            <View style={styles.page}>
              <StepSubmit />
            </View>
          );
        case 1:
          return (
            <View style={styles.page}>
              <StepVerifyFace />
            </View>
          );
        case 2:
          return (
            <View style={styles.page}>
              <StepApproving navigation={navigation} />
            </View>
          );
        default:
          return <View style={styles.page} />;
      }
    },
    [shouldRenderStep],
  );

  const content = useMemo(() => {
    if (!currentStep) {
      return <PageSkeleton />;
    }
    switch (currentStep) {
      case MiniBNPLOnboardingNextStep.REJECT:
        return <RejectedView />;
      default:
        return (
          <View style={styles.container}>
            <Animated.View
              style={[
                styles.slider,
                {
                  width: window.screen.width * TOTAL_STEPS,
                  transform: [{ translateX }],
                },
              ]}>
              {renderStepContent(0)}
              {renderStepContent(1)}
              {renderStepContent(2)}
            </Animated.View>
            <View
              style={[
                styles.stepTreeContainer,
                navigationState.fullScreenData?.fullScreen && styles.fullScreenStepTree,
              ]}>
              <StepTree
                data={STEP_TREE_NEXT_STEPS}
                allowGoBack={currentStep === MiniBNPLOnboardingNextStep.VERIFY_FACE}
              />
            </View>
          </View>
        );
    }
  }, [currentStep, translateX, renderStepContent]);

  useEffect(() => {
    dispatchNavigation({
      type: 'SET_TITLE',
      payload: 'Tài khoản trả sau',
    });
    dispatchNavigation({
      type: 'SET_CUSTOM_NAVIGATION_OPTIONS',
      payload: {
        path: ScreenKey.MPOnboardingScreen,
        options: {
          noNavigationBar: true,
        },
      },
    });
    getOnboardingStatusApi().then((res) => {
      verifyStep(res.onboarding_next_step as MiniBNPLOnboardingNextStep);
      return;
    }).catch((err) => {
      console.log('err', err);
    });
  }, []);

  useEffect(() => {
    switch (currentStep) {
      case MiniBNPLOnboardingNextStep.ONBOARDING:
        Tracking.loadLandingPageView({ currentStep: currentStep });
        break;
      case MiniBNPLOnboardingNextStep.VERIFY_FACE:
        Tracking.loadFaceAuthenView({ currentStep: currentStep });
        break;
      case MiniBNPLOnboardingNextStep.QUERY_STATUS:
      case MiniBNPLOnboardingNextStep.FINISH:
        Tracking.loadWaitingApprovalView({ currentStep: currentStep });
        break;
      case MiniBNPLOnboardingNextStep.REJECT:
        // WIP: update error code
        Tracking.loadRejectedView({ currentStep: currentStep, errorCode: '123' });
        break;
      default:
        break;
    }
  }, [currentStep]);

  return (
    <MiniBnplOnboardingScreenContext.Provider value={{ currentStep: currentStep, verifyStep }}>
      {content}
    </MiniBnplOnboardingScreenContext.Provider>
  );
}

const PageSkeleton = () => {
  return (
    <View style={styles.container}>
      <View style={styles.skeletonContainer}>
        <Skeleton width={343} height={60} style={styles.skeletonHeader} />
        <Skeleton width={300} height={40} style={styles.skeletonSubheader} />
        <Skeleton width={343} height={200} style={styles.skeletonContent} />
        <Skeleton width={200} height={50} style={styles.skeletonButton} />
      </View>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flexDirection: 'column',
    height: '100%',
    overflow: 'hidden',
  },
  page: {
    flex: 1,
    overflow: 'hidden',
  },
  slider: {
    flexDirection: 'row',
    flex: 1,
    overflow: 'hidden',
  },
  stepTreeContainer: {
    position: 'absolute',
    top: 12,
    left: 16,
  },
  fullScreenStepTree: {
    top: 56,
  },
  skeletonContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    paddingHorizontal: 16,
  },
  skeletonHeader: {
    marginBottom: 16,
  },
  skeletonSubheader: {
    marginBottom: 24,
  },
  skeletonContent: {
    marginBottom: 32,
  },
  skeletonButton: {
    marginBottom: 16,
  },
});

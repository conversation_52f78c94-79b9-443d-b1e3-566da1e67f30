import { trackEvent } from 'bnpl-shared/src/utils/tracking/trackEvent';

export function loadLandingPageView(params: { currentStep: string | null }) {
  trackEvent('6743.000', { current_step: params.currentStep });
}

export function clickLandingPageCTA(params: { currentStep: string | null }) {
  trackEvent('6743.001', { current_step: params.currentStep });
}

export function clickDiscoverMore(params: { currentStep: string | null }) {
  trackEvent('6743.002', { current_step: params.currentStep });
}

export function clickDemoContract(params: { currentStep: string | null }) {
  trackEvent('6743.003', { current_step: params.currentStep });
}

export function loadDemoContractView(params: { currentStep: string | null }) {
  trackEvent('6743.004', { current_step: params.currentStep });
}

export function clickLandingTnc(params: { currentStep: string | null }) {
  trackEvent('6743.005', { current_step: params.currentStep });
}

export function loadRejectedView(params: { currentStep: string | null; errorCode: string | number }) {
  trackEvent('6744.000', { current_step: params.currentStep, error_code: params.errorCode });
}

export function clickRejectedCTA(params: { currentStep: string | null; buttonName: 'main_cta' | 'sub_cta' }) {
  trackEvent('6744.001', { current_step: params.currentStep, button_name: params.buttonName });
}

export function loadFaceAuthenView(params: { currentStep: string | null }) {
  trackEvent('6745.000', { current_step: params.currentStep });
}

export function clickCTATakePicture() {
  trackEvent('6745.002');
}

export function loadWaitingApprovalView(params: { currentStep: string | null }) {
  trackEvent('6746.000', { current_step: params.currentStep });
}

export function clickWaitingApprovalCTA(params: { currentStep: string | null }) {
  trackEvent('6746.001', { current_step: params.currentStep });
}

export function loadOnboardingSuccessPopup() {
  trackEvent('6747.000');
}

export function clickOnboardingSuccessPopupCTA() {
  trackEvent('6747.001');
}

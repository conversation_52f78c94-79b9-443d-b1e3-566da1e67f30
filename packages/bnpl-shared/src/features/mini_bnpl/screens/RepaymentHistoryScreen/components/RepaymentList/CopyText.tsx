import { Label } from "@zpi/looknfeel/typography";
import { toast } from "@zpi/looknfeel/toast";
import React from 'react';
import styled from 'styled-components';
import { writeText } from 'bnpl-shared/src/utils/clipboard';
import { local_images } from "bnpl-shared/src/res/images/local";

export default function CopyText({ children, onCopy }: CopyTextProps) {
  return (
    <Wrapper
      onClick={() => {
        writeText(children).then(() => {
          onCopy?.();
          toast.open({
            message: "Đã sao chép thành công",
            zIndex: 100,
            icon: <img style={{ width: 16, height: 16 }} src={local_images.IconCheckCircle} />
          })
        })
      }}
    >
      <Label>{children}</Label>
      <img src={local_images.IconCopy} />
    </Wrapper>
  )
}

const Wrapper = styled.button`
  display: flex;
  align-items: center;
  justify-content: flex-end;
  gap: 8px;
  img { 
    width: 18px;
    height: 18px;
  }
`

interface CopyTextProps {
  children: string;
  onCopy?: () => void;
}

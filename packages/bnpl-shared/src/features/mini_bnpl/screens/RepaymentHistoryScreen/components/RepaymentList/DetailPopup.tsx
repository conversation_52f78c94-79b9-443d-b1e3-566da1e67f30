import React, { useContext, useEffect } from "react";
import styled from "styled-components";
import { Label, Heading, LabelProps } from "@zpi/looknfeel/typography";
import { BottomSheet } from "@zpi/looknfeel/bottomSheet";
import { Image } from "@zpi/looknfeel/image";
import { Button } from "@zpi/looknfeel/button";
import { RepaymentDetailPopupProps } from "./types";
import { formatCurrency } from "bnpl-shared/src/shared/utils/formatCurrency";
import { format } from "date-fns";
import { RepaymentDisplayStatus } from "bnpl-shared/src/types/MiniBnpl";
import CopyText from "./CopyText";
import { launchDeeplink } from "bnpl-shared/src/lib/ZalopaySDK/launchDeeplink";
import { buildZlpPublicBaseUrl } from "bnpl-shared/src/api/withBaseUrl";
import { images } from "bnpl-shared/src/res";
import * as Tracking from '../../tracking';
import { MiniRepaymentHistoryContext } from "../../context";
import { AppColors } from 'bnpl-shared/src/constants';

export default function RepaymentDetailPopup({ item, open, onClose, onClickPay }: RepaymentDetailPopupProps) {
  const { tabKey } = useContext(MiniRepaymentHistoryContext);
  useEffect(() => {
    if (open) {
      Tracking.loadTransDetailPopup({ tab: tabKey, displayStatus: item.displayStatus, appId: item.appID })
    }
  }, [open]);

  return (
    <BottomSheet
      open={open}
      title="Chi tiết giao dịch"
      onClickCloseBtn={onClose}
    >
      <Wrapper>
        <Header>
          <Image src={item.iconURL} imgHeight="40px" imgWidth="40px" style={{ marginBottom: '8px' }} />
          <Label align="center">{item.title}</Label>
          <Heading size="Medium" align="center" bold>{formatCurrency(item.amount)}</Heading>
          <Label color={AppColors.dark[300]} align="center">{item.description}</Label>
        </Header>
        <Section style={{ padding: '8px 0px' }}>
          <Row disabled>
            <Label color={AppColors.dark[300]}>Hạn thanh toán</Label>
            <Label>{format(new Date(item.dueTime), "hh:mm - dd/MM/yyyy")}</Label>
          </Row>
          <Row disabled>
            <Label color={AppColors.dark[300]}>Trạng thái</Label>
            <Badge
              backgroundColor={MapDisplayStatusBgColor[item.displayStatus]}
              color={MapDisplayStatusFgColor[item.displayStatus]}
            >
              {item.statusDescription}
            </Badge>
          </Row>
        </Section>
        <Section style={{ padding: '8px 0px' }}>
          <Row>
            <Label color={AppColors.dark[300]}>Mã giao dịch</Label>
            <CopyText
              onCopy={() => {
                Tracking.clickCopyOrderTransID({ tab: tabKey, displayStatus: item.displayStatus, appId: item.appID })
              }}
            >
              {item.zpTransID}
            </CopyText>
          </Row>
          {item.orderTime && (
            <Row disabled>
              <Label color={AppColors.dark[300]}>Thời gian</Label>
              <Label>{format(new Date(item.orderTime), "hh:mm - dd/MM/yyyy")}</Label>
            </Row>
          )}
          <Row disabled>
            <Label color={AppColors.dark[300]}>Tài khoản/Thẻ</Label>
            <Label>Tài khoản trả sau</Label>
          </Row>
        </Section>
        <Section>
          <Row onClick={() => {
            Tracking.clickCallSupport({ tab: tabKey, displayStatus: item.displayStatus, appId: item.appID })
            openSupportCenter(item.zpTransID)
          }}>
            <div className="left">
              <img src={images.IconSupportCenter.uri} />
              <Label>Yêu cầu hỗ trợ</Label>
            </div>
            <svg width="18" height="18" viewBox="0 0 18 18" fill="none" xmlns="http://www.w3.org/2000/svg">
              <path d="M11.2464 8.25367L7.80394 4.81117C7.51144 4.51867 7.03894 4.51867 6.74644 4.81117C6.45394 5.10367 6.45394 5.57617 6.74644 5.86867L9.65645 8.78617L6.74644 11.6962C6.45394 11.9887 6.45394 12.4612 6.74644 12.7537C7.03894 13.0462 7.51144 13.0462 7.80394 12.7537L11.2464 9.31117C11.5389 9.02617 11.5389 8.54617 11.2464 8.25367Z" fill="#8995A1" />
            </svg>
          </Row>
        </Section>
        {(item.displayStatus !== 'paid' && item.displayStatus !== 'refunded') ? (
          <Button onClick={() => {
            onClickPay();
            onClose();
          }} style={{ marginTop: '16px' }} size="xl">Thanh toán ngay</Button>
        ) : (
          <Button onClick={() => {
            onClose();
          }} style={{ marginTop: '16px' }} size="xl">Đóng</Button>
        )}
      </Wrapper>
    </BottomSheet>
  )
}

function openSupportCenter(zpTransID: string) {
  const params = new URLSearchParams({
    transId: zpTransID,
    transType: '1',
    transOriginal: '0',
    provider: 'Transxdetail',
  })
  launchDeeplink({
    zpa: `zalopay://launch/app/-16?${params.toString()}`,
    zpi: `${buildZlpPublicBaseUrl()}/faq/solutions?${params.toString()}`
  })
}

const Badge = styled(Label) <LabelProps & { backgroundColor?: string }>`
  padding: 2px 8px;
  ${({ backgroundColor = "transparent" }) => `background-color: ${backgroundColor}`};
  border-radius: 4px;
`

const MapDisplayStatusFgColor: Partial<Record<RepaymentDisplayStatus, string>> = {
  "overdue": AppColors.red[700],
  "paid": AppColors.green[700],
  "pending": AppColors.dark[400],
  "refunded": AppColors.dark[400],
  "upcoming": AppColors.orange[700],
}

const MapDisplayStatusBgColor: Partial<Record<RepaymentDisplayStatus, string>> = {
  "overdue": AppColors.red[50],
  "paid": AppColors.green[50],
  "pending": AppColors.dark[50],
  "refunded": AppColors.dark[50],
  "upcoming": AppColors.orange[50],
}

const Wrapper = styled.div`
  display: flex;
  flex-direction: column;
  gap: 12px;
`

const Header = styled.div`
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 4px;
  padding: 8px 0px;
`

const Section = styled.div`
  display: flex;
  flex-direction: column;
  gap: 4px;
  border: 1px solid ${AppColors.strokeV2};
  border-radius: 8px;
`

const Row = styled.button`
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12px 16px;
  gap: 10px;
  .left {
    display: flex;
    align-items: center;
    gap: 4px;
  }
`

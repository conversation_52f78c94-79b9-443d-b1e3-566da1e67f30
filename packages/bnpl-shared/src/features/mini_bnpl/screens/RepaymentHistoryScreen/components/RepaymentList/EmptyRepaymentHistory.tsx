import React, { useContext } from 'react';
import { View } from 'react-native';
import { StyleSheet } from 'bnpl-shared/src/shared/styles/StyleSheet';
import * as Tracking from '../../tracking';
import { MiniRepaymentHistoryContext } from '../../context';
import { Spacer } from 'bnpl-shared/src/components/Spacer';
import SuggestedServices from 'bnpl-shared/src/features/mini_bnpl/components/SuggestedServices';
import SectionHeader from 'bnpl-shared/src/features/mini_bnpl/components/SectionHeader';
import { AppButton, AppImage, AppText } from 'bnpl-shared/src/shared/react-native-customized';
import { AppColors } from 'bnpl-shared/src/constants';

export default function EmptyRepaymentHistory({ imgSrc, title, description, cta }: EmptyRepaymentHistoryProps) {
  const { tabKey } = useContext(MiniRepaymentHistoryContext);
  return (
    <View>
      <View style={styles.stateView}>
        <AppImage source={{ uri: imgSrc }} width={180} height={180} />
        <Spacer height={24} />
        <AppText size={16} height={20} bold center>{title}</AppText>
        <Spacer height={8} />
        <AppText size={14} height={18} center color={AppColors.dark[300]}>{description}</AppText>
        <Spacer height={24} />
        <AppButton buttonStyle={styles.button} onPress={cta.onClick} title={cta.label} />
      </View>
      <Spacer height={24} />
      <SectionHeader title="Dịch vụ hỗ trợ thanh toán" />
      <Spacer height={12} />
      <SuggestedServices
        trackEventClickItem={(tag) => {
          Tracking.clickEmptyStateOtherService({ tab: tabKey, iconName: 'telco_topup' });
        }}
      />
    </View>
  )
}

interface EmptyRepaymentHistoryProps {
  imgSrc: string;
  title: string;
  description: string;
  cta: {
    label: string;
    onClick: () => void;
  }
}

const styles = StyleSheet.create({
  stateView: {
    flexDirection: 'column',
    padding: 24,
    alignItems: 'center',
  },
  button: {
    paddingHorizontal: 8,
  }
});

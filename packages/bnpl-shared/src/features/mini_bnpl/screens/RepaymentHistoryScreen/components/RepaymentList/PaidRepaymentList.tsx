import React, { useContext, useEffect } from "react";
import RepaymentList from "./RepaymentList";
import styled from "styled-components";
import EmptyRepaymentHistory from "./EmptyRepaymentHistory";
import { useHistory } from "react-router-dom";
import { images } from "bnpl-shared/src/res";
import { MiniRepaymentHistoryContext } from "../../context";
import * as Tracking from "../../tracking";
import { RepaymentItem } from 'bnpl-shared/src/types';

export default function PaidRepaymentList({ paidPayments, emptyUnpaid, }: PaidRepaymentListProps) {
  const history = useHistory();
  const { tabKey } = useContext(MiniRepaymentHistoryContext);

  useEffect(() => {
    Tracking.load({ tab: tabKey, empty: paidPayments.length === 0 })
  }, [paidPayments]);

  if (!Array.isArray(paidPayments) || paidPayments.length === 0) {
    return emptyUnpaid ? (
      <EmptyRepaymentHistory
        imgSrc={images.ImageEmptyMiniRepaymentList1.uri}
        title="Bạn chưa chi tiêu bằng Tài khoản trả sau"
        description="Khoản trả sau đến hạn thanh toán sẽ hiển thị ở đây"
        cta={{
          label: "Về trang trước",
          onClick: () => {
            Tracking.clickEmptyStateCTA({ tab: tabKey })
            history.goBack();
          }
        }}
      />
    ) : (
      <EmptyRepaymentHistory
        imgSrc={images.ImageEmptyMiniRepaymentList3.uri}
        title="Chưa có khoản trả sau nào được thanh toán"
        description="Bạn để ý hạn thanh toán để tránh bị trễ hạn nhé."
        cta={{
          label: "Về trang trước",
          onClick: () => {
            Tracking.clickEmptyStateCTA({ tab: tabKey })
            history.goBack();
          }
        }}
      />
    )
  }
  return (
    <Wrapper>
      {Array.isArray(paidPayments) && paidPayments.length > 0 && (
        <RepaymentList
          items={paidPayments}
        />
      )}
    </Wrapper>
  )
}

interface PaidRepaymentListProps {
  paidPayments: RepaymentItem[];
  emptyUnpaid: boolean;
  onRequestRepayItem?: (item: RepaymentItem) => void;
}

const Wrapper = styled.div`
  display: flex;
  flex-direction: column;
  gap: 16px;
`;

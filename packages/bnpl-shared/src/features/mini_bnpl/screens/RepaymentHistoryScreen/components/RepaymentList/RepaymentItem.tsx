import React, { useContext, useState } from 'react';
import styled from 'styled-components';
import { Image } from "@zpi/looknfeel/image";
import { Skeleton } from "@zpi/looknfeel/skeleton";
import { Label } from "@zpi/looknfeel/typography";
import { Button } from "@zpi/looknfeel/button";
import format from "date-fns/format";
import { RepaymentItemProps } from "./types";
import { formatCurrency } from 'bnpl-shared/src/shared/utils/formatCurrency';
import RepaymentDetailPopup from "./DetailPopup";
import { local_images } from "bnpl-shared/src/res/images/local";
import { RepaymentDisplayStatus } from "bnpl-shared/src/types/MiniBnpl";
import * as Tracking from '../../tracking';
import { MiniRepaymentHistoryContext } from '../../context';
import { AppColors } from 'bnpl-shared/src/constants';

export default function RepaymentItem(props: RepaymentItemProps) {
  const { displayStatus, appID } = props;
  const [detailPopupOpen, setDetailPopupOpen] = useState(false);
  const { tabKey } = useContext(MiniRepaymentHistoryContext);

  function renderItem() {
    switch (displayStatus) {
      case "paid":
        return <PaidItem {...props} />
      case "overdue":
      case "upcoming":
      case "pending":
        return <UnpaidItem {...props} onClickPay={() => {
          Tracking.clickItemCTA({ tab: tabKey, displayStatus: displayStatus });
          props.onRepayBtnClick?.()
        }} />
      case "refunded":
        return <RefundedItem {...props} />
      default:
        return null
    }
  }

  return (
    <>
      <Wrapper onClick={() => {
        Tracking.clickItem({ tab: tabKey, displayStatus: displayStatus });
        setDetailPopupOpen(true);
      }}>
        {renderItem()}
      </Wrapper>
      <RepaymentDetailPopup
        item={props}
        open={detailPopupOpen}
        onClose={() => {
          setDetailPopupOpen(false)
        }}
        onClickPay={() => {
          Tracking.clickPayNow({ tab: tabKey, displayStatus: displayStatus, appId: appID });
          props.onRepayBtnClick?.();
        }}
      />
    </>
  );
}

function PaidItem({ iconURL, amount, title, paidTime }: RepaymentItemProps) {
  return (
    <>
      <Image src={iconURL} imgHeight="40px" imgWidth="40px" />
      <MainContentWrapper>
        <Label size="Large">{title}</Label>
        {paidTime && <Label size="Small" color={AppColors.dark[300]}>Ngày thanh toán: {format(new Date(paidTime), "dd/MM/yyyy")}</Label>}
      </MainContentWrapper>
      <RightWrapper>
        <img src={local_images.IconRepaymentPaid} />
        <Label style={{ zIndex: 10 }}  size="Large" bold align="right">{formatCurrency(amount)}</Label>
      </RightWrapper>
    </>
  );
}

function RefundedItem({ iconURL, amount, title, paidTime }: RepaymentItemProps) {
  return (
    <>
      <Image src={iconURL} imgHeight="40px" imgWidth="40px" />
      <MainContentWrapper>
        <Label size="Large">{title}</Label>
        {paidTime && <Label size="Small" color={AppColors.dark[300]}>Ngày hoàn tiền: {format(new Date(paidTime), "dd/MM/yyyy")}</Label>}
      </MainContentWrapper>
      <RightWrapper>
        <img src={local_images.IconRepaymentRefunded} />
        <Label style={{ zIndex: 10 }} size="Large" bold align="right">{formatCurrency(amount)}</Label>
      </RightWrapper>
    </>
  );
}

function UnpaidItem({ dueTime, amount, title, orderTime, displayStatus, dueDateDescription, onClickPay }: RepaymentItemProps & { onClickPay: () => void }) {
  return (
    <>
      <DueDate dueTime={dueTime} />
      <MainContentWrapper>
        {dueDateDescription && <DueDateDescription displayStatus={displayStatus}>{dueDateDescription}</DueDateDescription>}
        <Label size="Large">{title}</Label>
        {orderTime && <Label size="Small" color={AppColors.dark[300]}>{format(new Date(orderTime), "hh:mm - dd/MM/yyyy")}</Label>}
      </MainContentWrapper>
      <RightWrapper>
        <Label size="Large" bold align="right">{formatCurrency(amount)}</Label>
        <Button
          size="md"
          onClick={(e) => {
            e.stopPropagation();
            onClickPay();
          }}
        >
          Thanh toán
        </Button>
      </RightWrapper>
    </>
  );
}

function DueDate({ dueTime }: DueTimeProps) {
  const dueDate = new Date(dueTime);
  return (
    <DueDateWrapper>
      <Label
        className="month"
        size="XSmall"
        bold
        align="center"
        color={AppColors.white}
      >
        {`Th ${format(dueDate, "M")}`}
      </Label>
      <Label className="date" size="Large">{format(dueDate, "dd")}</Label>
    </DueDateWrapper>
  );
}

function DueDateDescription({ displayStatus, children }: { displayStatus: RepaymentDisplayStatus, children: string }) {
  return (
    <Label size="Small" color={MapDisplayStatusFgColor[displayStatus]}>{children}</Label>
  )
}

const MapDisplayStatusFgColor: Partial<Record<RepaymentDisplayStatus, string>> = {
  "overdue": AppColors.red[500],
  "paid": AppColors.green[500],
  "pending": AppColors.dark[200],
  "refunded": AppColors.dark[400],
  "upcoming": AppColors.orange[500],
}

export function RepaymentItemSkeleton() {
  return (
    <Wrapper>
      <Skeleton width="40px" height="40px" />
      <MainContentWrapper>
        <Skeleton width="60px" height="20px" />
        <Skeleton width="88px" height="16px" />
      </MainContentWrapper>
      <RightWrapper>
        <Skeleton width="60px" height="20px" />
        <Skeleton width="88px" height="24px" />
      </RightWrapper>
    </Wrapper>
  );
}

interface DueTimeProps {
  dueTime: string;
}

const Wrapper = styled.button`
  display: flex;
  border: 1px solid ${AppColors.dark[50]};
  border-radius: 8px;
  padding: 16px;
  height: 84px;
  align-items: center;
  position: relative;
`;

const DueDateWrapper = styled.div`
  width: 40px;
  height: 40px;
  overflow: hidden;
  border: solid 1px ${AppColors.dark[50]};
  border-radius: 8px;
  display: flex;
  flex-direction: column;
  align-items: center;
  .month {
    background-color: ${AppColors.blue[500]};
    width: 100%;
    height: 14px;
  }
  .date {
    margin-top: 2px;
  }
`;

const MainContentWrapper = styled.div`
  display: flex;
  flex-direction: column;
  align-items: flex-start;
  gap: 4px;
  flex: 1;
  margin-left: 12px;
`;

const RightWrapper = styled.div`
  width: 88px;
  display: flex;
  flex-direction: column;
  align-items: flex;
  justify-content: center;
  gap: 8px;

  img {
    position: absolute;
    right: 16px;
    bottom: 0px;
    height: 40px;
    width: 52px
  }
`;

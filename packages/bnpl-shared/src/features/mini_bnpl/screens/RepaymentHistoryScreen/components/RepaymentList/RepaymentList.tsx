import React from 'react';
import Header, { SectionHeaderSkeleton } from './RepaymentListHeader';
import styled from 'styled-components';
import RepaymentItem, { RepaymentItemSkeleton } from './RepaymentItem';
import { RepaymentListProps } from './types';

export default function RepaymentList({ header, items, onItemRepayBtnClick }: RepaymentListProps) {
  return (
    <Wrapper>
      {header && <Header label={header.label} numberOfItems={header.numberOfItems} />}
      {items.map((item) => (
        <RepaymentItem
          {...item}
          key={item.zpTransID}
          onRepayBtnClick={() => onItemRepayBtnClick?.(item)}
        />
      ))}
    </Wrapper>
  );
}

export function RepaymentListSkeleton() {
  return (
    <Wrapper>
      <SectionHeaderSkeleton />
      <RepaymentItemSkeleton />
      <RepaymentItemSkeleton />
      <RepaymentItemSkeleton />
    </Wrapper>
  );
}

const Wrapper = styled.div`
  display: flex;
  flex-direction: column;
  gap: 16px;
`;

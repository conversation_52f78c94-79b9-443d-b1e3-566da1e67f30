import React from 'react';
import styled from 'styled-components';
import { Label, LabelProps } from '@zpi/looknfeel/typography';
import { Skeleton } from "@zpi/looknfeel/skeleton";
import { AppColors } from 'bnpl-shared/src/constants';

export default function RepaymentListHeader({ label, numberOfItems }: SectionHeaderProps) {
  return <Header size="Large" bold numberOfItems={numberOfItems}>{label}</Header>;
}

export function SectionHeaderSkeleton() {
  return <Skeleton width="150px" height="20px" style={{ marginBottom: '16px' }} />;
}

interface SectionHeaderProps {
  label: string;
  numberOfItems?: number;
}

const Header = styled(Label) <LabelProps & { numberOfItems?: number }>`
  display: flex;
  align-items: center;
  gap: 8px;
  &::before {
    content: '';
    display: block;
    width: 4px;
    height: 14px;
    border-top-left-radius: 2px;
    border-bottom-left-radius: 2px;
    background-color: ${AppColors.green[500]};
  }
  &::after {
    ${({ numberOfItems }) => isNaN(Number(numberOfItems)) ? `content: ""` : `content: "(${numberOfItems})"`}; 
    font-size: 16px;
    line-height: 20px;
    font-weight: 700;
    color: ${AppColors.blue[500]};
  }
`;

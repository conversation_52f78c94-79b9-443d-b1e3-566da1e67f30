import React, { useContext, useEffect } from "react";
import RepaymentList from "./RepaymentList";
import styled from "styled-components";
import { Label } from "@zpi/looknfeel/typography"
import { local_images } from "bnpl-shared/src/res/images/local";
import EmptyRepaymentHistory from "./EmptyRepaymentHistory";
import { useHistory } from "react-router-dom";
import { images } from "bnpl-shared/src/res";
import { MiniRepaymentHistoryContext } from "../../context";
import * as Tracking from '../../tracking';
import { RepaymentItem } from 'bnpl-shared/src/types';

export default function UnpaidRepaymentList({ emptyPaid, overduePayments, upcomingPayments, onRequestRepayItem }: UnpaidRepaymentListProps) {
  const history = useHistory();
  const { tabKey } = useContext(MiniRepaymentHistoryContext);

  useEffect(() => {
    Tracking.load({ tab: tabKey, empty: overduePayments.length === 0 && upcomingPayments.length === 0 })
  }, [overduePayments, upcomingPayments]);

  if ((!Array.isArray(overduePayments) || overduePayments.length === 0)
  && (!Array.isArray(upcomingPayments) || upcomingPayments.length === 0)) {
    return emptyPaid ? (
      <EmptyRepaymentHistory
        imgSrc={images.ImageEmptyMiniRepaymentList1.uri}
        title="Bạn chưa chi tiêu bằng Tài khoản trả sau"
        description="Khoản trả sau đến hạn thanh toán sẽ hiển thị ở đây"
        cta={{
          label: "Về trang trước",
          onClick: () => {
            Tracking.clickEmptyStateCTA({ tab: tabKey })
            history.goBack();
          }
        }}
      />
    ) : (
      <EmptyRepaymentHistory
        imgSrc={images.ImageEmptyMiniRepaymentList2.uri}
        title="Bạn đã thanh toán hết"
        description="Khoản trả sau đến hạn thanh toán sẽ hiển thị ở đây"
        cta={{
          label: "Về trang trước",
          onClick: () => {
            Tracking.clickEmptyStateCTA({ tab: tabKey })
            history.goBack();
          }
        }}
      />
    )
  }

  return (
    <Wrapper>
      {Array.isArray(overduePayments) && overduePayments.length > 0 && (
        <RepaymentList
          header={{
            label: "Đến hạn thanh toán",
            numberOfItems: overduePayments.length,
          }}
          items={overduePayments}
          onItemRepayBtnClick={(item) => onRequestRepayItem?.(item)}
        />
      )}
      {Array.isArray(upcomingPayments) && upcomingPayments.length > 0&& (<RepaymentList
        header={{
          label: "Chưa đến hạn",
          numberOfItems: upcomingPayments.length,
        }}
        items={upcomingPayments}
        onItemRepayBtnClick={(item) => onRequestRepayItem?.(item)}
      />)}
      {overduePayments.length > 0 && <Warning />}
    </Wrapper>
  )
}

function Warning() {
  const { tabKey } = useContext(MiniRepaymentHistoryContext);
  useEffect(() => {
    Tracking.loadWarningBanner({ tab: tabKey })
  }, []);
  return (
    <WarningWrapper>
      <img src={local_images.IconWarningFillGradient} />
      <Label size="Small">Chậm thanh toán sẽ ảnh hưởng đến việc đánh giá tăng hạn mức chi tiêu.</Label>
    </WarningWrapper>
  )
}

interface UnpaidRepaymentListProps {
  emptyPaid: boolean;
  overduePayments: RepaymentItem[];
  upcomingPayments: RepaymentItem[];
  onRequestRepayItem?: (item: RepaymentItem) => void;
}

const Wrapper = styled.div`
  display: flex;
  flex-direction: column;
  gap: 16px;
  padding-bottom: 70px;
`;

const WarningWrapper = styled.div`
  display: flex;
  align-items: flex-start;
  gap: 12px;
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  z-index: 20;
  padding: 16px;
  border-top-left-radius: 12px;
  border-top-right-radius: 12px;
  background: linear-gradient(
    to right, 
    rgba(252, 232, 236, 1), 
    rgba(254, 250, 251, 1), 
    rgba(252, 232, 236, 1)
  );
  img {
    width: 28px;
    height: 28px;
  }
`

import {HistoryItem, HistoryItemExtend, RepaymentItem} from 'bnpl-shared/src/types/MiniBnpl';

export type RepaymentItemProps = RepaymentItem & HistoryItemExtend;

export interface RepaymentListProps {
  header?: {
    label: string;
    numberOfItems: number;
  };
  items: RepaymentItem[];
  onItemRepayBtnClick?: (item: RepaymentItem) => void;
}

export interface RepaymentDetailPopupProps {
  item: RepaymentItem;
  open: boolean;
  onClickPay: () => void;
  onClose: () => void;
}

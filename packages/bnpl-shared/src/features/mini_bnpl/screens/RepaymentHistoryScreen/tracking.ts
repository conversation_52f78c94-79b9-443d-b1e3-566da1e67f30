import { RepaymentDisplayStatus } from 'bnpl-shared/src/types/MiniBnpl';
import { trackEvent } from 'bnpl-shared/src/utils/tracking/trackEvent';

type BillStatus = 'no_due' | 'due' | 'overdue' | 'paid' | 'refunded';

// TODO: define case map for upcoming/pending, paid and refunded
const MapBillStatusByDisplayStatus: Record<RepaymentDisplayStatus, BillStatus> = {
  'paid': "paid",
  'upcoming': "due",
  'pending': "due",
  'overdue': "overdue",
  'refunded': "refunded"
}

export function load(params: { tab: string, empty: boolean }) {
  trackEvent('6749.000', { tab: params.tab, state: params.empty ? 'empty' : 'bill_list' })
}

export function clickItem(params: { tab: string, displayStatus: RepaymentDisplayStatus }) {
  trackEvent('6749.001', { tab: params.tab, bill_status: MapBillStatusByDisplayStatus[params.displayStatus] })
}

export function clickItemCTA(params: { tab: string, displayStatus: RepaymentDisplayStatus }) {
  trackEvent('6749.002', { tab: params.tab, bill_status: MapBillStatusByDisplayStatus[params.displayStatus] })
}

export function loadTransDetailPopup(params: { tab: string, displayStatus: RepaymentDisplayStatus, appId: string | number }) {
  trackEvent('6749.003', { tab: params.tab, bill_status: MapBillStatusByDisplayStatus[params.displayStatus], app_id: params.appId })
}

export function clickCopyOrderTransID(params: { tab: string, displayStatus: RepaymentDisplayStatus, appId: string | number }) {
  trackEvent('6749.004', { tab: params.tab, bill_status: MapBillStatusByDisplayStatus[params.displayStatus], app_id: params.appId })
}

export function clickCallSupport(params: { tab: string, displayStatus: RepaymentDisplayStatus, appId: string | number }) {
  trackEvent('6749.005', { tab: params.tab, bill_status: MapBillStatusByDisplayStatus[params.displayStatus], app_id: params.appId })
}

export function clickPayNow(params: { tab: string, displayStatus: RepaymentDisplayStatus, appId: string | number }) {
  trackEvent('6749.006', { tab: params.tab, bill_status: MapBillStatusByDisplayStatus[params.displayStatus], app_id: params.appId })
}

export function clickEmptyStateCTA(params: { tab: string }) {
  trackEvent('6749.007', { tab: params.tab })
}

export function clickEmptyStateOtherService(params: { tab: string, iconName: string }) {
  trackEvent('6749.008', { tab: params.tab, icon_name: params.iconName })
}

export function loadWarningBanner(params: { tab: string }) {
  trackEvent('6749.009', { tab: params.tab })
}


import { getRepaymentHistoryApi } from "bnpl-shared/src/api/mini-bnpl/getHistoryApi";
import { usePartnerData } from "bnpl-shared/src/features/multipartner_integration/helpers";
import { HistoryItem, HistoryItemExtend, RepaymentItem } from "bnpl-shared/src/types/MiniBnpl";
import { differenceInDays, differenceInHours } from "date-fns";
import { useEffect, useState } from "react";
import {hideLoading, showLoading} from "bnpl-shared/src/shared/ZaloPayModules";
import {postCreateMiniBnplRepayOrder} from "bnpl-shared/src/api/mini-bnpl/postCreateMiniBnplRepayOrder";
import {launchCashierFlow} from "bnpl-shared/src/utils/launchCashierFlow";
import {setAppToast, ToastType} from "bnpl-shared/src/redux/appToastReducer";
import {useDispatch} from "react-redux";

const MINIMUM_DAYS_BEFORE_DUE = 7;

export default function useRepaymentHistory(): {history:ProcessedHistory | null, submitRepay:(billId:number) => void} {
  const [history, setHistory] = useState<ProcessedHistory | null>(null);
  const { getChosenPartner } = usePartnerData();
  const dispatch = useDispatch();
  const chosenPartnerAccountId = getChosenPartner()?.account_id;

  const submitRepay = (billId: number) => {
    (async () => {
      try {
        if(chosenPartnerAccountId){
          showLoading();
          const resp = await postCreateMiniBnplRepayOrder({
            account_id: chosenPartnerAccountId,
            bill_ids: {
              bill_ids: [billId],
            }
          });
          hideLoading();
          if(resp){
            await launchCashierFlow({
              app_id: resp.app_id,
              zp_trans_token: resp.zp_trans_token
            })
          }
        }
      } catch (e) {
        const message = 'Có lỗi khi thực hiện yêu cầu, vui lòng thử lại sau';
        dispatch(setAppToast({ message, type: ToastType.ERROR, duration: 3000 }));
      } finally {
        hideLoading();
      }
    })();
  };

  useEffect(() => {
    async function fetchHistory() {
      if (!chosenPartnerAccountId) {
        return;
      }
      try {
        const response = await getRepaymentHistoryApi({ account_id: chosenPartnerAccountId });
        setHistory(processHistory(response));
      } catch (error: any) {
        console.log("get mini bnpl repayment list failed", error);
        setHistory({
          overduePayments: [],
          upcomingPayments: [],
          paidPayments: [],

        })
      }
    };
    fetchHistory();
  }, [chosenPartnerAccountId]);

  return {history, submitRepay };
}

function processHistory(history: HistoryItem[]): ProcessedHistory {
  const now = new Date();
  const overduePayments: ListItems[] = [];
  const upcomingPayments: ListItems[] = [];
  const paidPayments: ListItems[] = [];
  history.forEach((item) => {
    const defaultData = {
      billId: item.id,
      title: item.service_name,
      description: item.description,
      iconURL: item.service_icon,
      dueTime: item.due_date,
      amount: Number(item.amount),
      orderTime: item.repayable_date,
      paidTime: item.full_repaid_date,
      zpTransID: item.zp_trans_id,
      appID: item.app_id,
    }
    switch (item.status) {
      case "CREATED":
        const { due_date } = item;
        const dueDate = new Date(due_date);
        const diffInDays = differenceInDays(dueDate, now);
        const dueDateMs = dueDate.getTime();
        const nowMs = now.getTime();
        // overdue
        if (dueDateMs < nowMs) {
          overduePayments.push({
            ...defaultData,
            displayStatus: "overdue",
            dueDateDescription: `Quá hạn ${Math.abs(diffInDays) || 1} ngày`,
            statusDescription: "Quá hạn",
          });
          break;
        }
        // not yet
        if (diffInDays < MINIMUM_DAYS_BEFORE_DUE && diffInDays >= 0) {
          const diffInHours = Math.abs(differenceInHours(dueDate, now));
          overduePayments.push({
            ...defaultData,
            displayStatus: "upcoming",
            dueDateDescription: diffInDays < 1 ? `Còn ${diffInHours || 1} giờ` : `Còn ${diffInDays} ngày`,
            statusDescription: diffInDays < 1 ? `Còn ${diffInHours || 1} giờ` : `Còn ${diffInDays} ngày`,
          });
          break;
        }
        upcomingPayments.push({
          ...defaultData,
          displayStatus: "pending",
          dueDateDescription: "Chưa đến hạn",
          statusDescription: "Chưa đến hạn",
        });
        break;
      case "SETTLED":
        paidPayments.push({
          ...defaultData,
          displayStatus: "paid",
          statusDescription: "Đã thanh toán",
        });
        break;
      case "REFUND_FULL":
        paidPayments.push({
          ...defaultData,
          displayStatus: "refunded",
          statusDescription: "Đã hoàn trả",
        });
        break;
      default:
        break;
    }
  });

  return {
    overduePayments,
    upcomingPayments,
    paidPayments,
  };
}

type ListItems = RepaymentItem & HistoryItemExtend;

interface ProcessedHistory {
  overduePayments: ListItems[];
  upcomingPayments: ListItems[];
  paidPayments: ListItems[];
}

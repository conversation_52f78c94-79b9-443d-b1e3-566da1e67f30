import { ScreenRouterModel } from './ScreenRouterModel';
import { ScreenKey } from 'bnpl-shared/src/constants';
import {
  BindingStatus,
  BindingStep,
  LockOutReason,
  OnboardingStatus,
  PartnerData,
  RiskAction,
  UserIdType,
  UtmCampaign,
} from 'bnpl-shared/src/types';
import { compareVersions, getNativeVersion } from 'bnpl-shared/src/shared/utils/versionUtils';
import { getPermissionApi } from 'bnpl-shared/src/api/getPermissionApi';
import { getBindingInfoApi } from 'bnpl-shared/src/api/getBindingInfoApi';
import { getSubmissionStatusV2Api } from 'bnpl-shared/src/api/getSubmissionStatusV2Api';
import { store } from 'bnpl-shared/src/redux/store';
import { getEmbeddedScreenKey } from './getEmbeddedScreenKey';

const REJECTED_BINDING_STEPS = [BindingStep.BOD1_REJECTED, BindingStep.BOD2_REJECTED, BindingStep.REJECTED];
const PREVIEW_MODE_ONBOARDING_STATUSES = [OnboardingStatus.MANUAL_APPROVE, OnboardingStatus.WAITING_APPROVE];

export class CIMBScreenRouterModel extends ScreenRouterModel {
  async execute(_partnerData?: PartnerData) {
    try {
      /*
       * Because below v9.21 Authen Challenge is not fully support from UM team, which is required from CIMB
       * for onboarding, submit nfc, renew flow,... so we need to lock out user if they are using old version
       */
      if (compareVersions(getNativeVersion(), '9.21') === -1) {
        return this.navigation?.replace?.(ScreenKey.OldVersionLockOutScreen);
      }

      const { is_fraud, is_whitelisted, binding_status, id_type, is_allow_resubmit, risk_info } =
        await getPermissionApi();

      //Embedded Onboarding rule check
      const utmCampaign = store.getState().utm.utmCampaign;
      const isEmbeddedOnboarding = utmCampaign && UtmCampaign.EmbeddedOnboarding === utmCampaign;

      const homeScreenParams = buildHomeScreenParams({
        risk_info,
      });

      if (!is_whitelisted) {
        return this.navigation?.replace?.(ScreenKey.LockOutScreen, { issue: LockOutReason.non_whitelisted });
      }

      if (risk_info?.is_risk && risk_info?.action === RiskAction.REJECT) {
        return this.navigation?.replace?.(ScreenKey.LockOutScreen, { issue: LockOutReason.risk_rejected });
      }

      if (is_fraud) {
        return this.navigation?.replace?.(ScreenKey.LockOutScreen, { issue: LockOutReason.blacklist });
      }

      if (
        binding_status !== BindingStatus.open &&
        binding_status !== BindingStatus.locked &&
        [UserIdType.OfficerCert, UserIdType.Passport].includes(id_type)
      ) {
        return this.navigation?.replace?.(ScreenKey.LockOutScreen, { issue: LockOutReason.passport_officer });
      }

      if ([BindingStatus.open, BindingStatus.locked].includes(binding_status)) {
        if (isEmbeddedOnboarding) {
          const embeddedScreenKey = await getEmbeddedScreenKey();
          return this.navigation?.replace?.(embeddedScreenKey);
        }
        return this.navigation?.replace?.(
          ScreenKey.MainScreen,
          homeScreenParams,
        );
      }

      const { binding_step, request_id } = await getBindingInfoApi();
      if ([BindingStatus.new, BindingStatus.processing].includes(binding_status)) {
        const requestId = request_id === '0' ? '' : request_id;
        const bindingStep =
          binding_step?.current_step?.int_value === undefined ? null : binding_step.current_step.int_value;

        let onboarding_status;
        if (requestId) {
          const response = await getSubmissionStatusV2Api({ request_id: requestId });
          onboarding_status = response.onboarding_status;
        }

        if (onboarding_status && PREVIEW_MODE_ONBOARDING_STATUSES.includes(onboarding_status)) {
          if (isEmbeddedOnboarding) {
            const embeddedScreenKey = await getEmbeddedScreenKey();
            return this.navigation?.replace?.(embeddedScreenKey);
          }
          return this.navigation?.replace?.(
            ScreenKey.MainScreen,
            homeScreenParams,
          );
        }

        if (bindingStep === null) {
          return this.navigation?.replace?.(ScreenKey.OnboardingScreen);
        }

        if (REJECTED_BINDING_STEPS.includes(bindingStep)) {
          if (is_allow_resubmit) {
            return this.navigation?.replace?.(ScreenKey.ResubmitOnboarding, { request_id });
          } else {
            return this.navigation?.replace?.(ScreenKey.LockOutScreen, {
              issue: LockOutReason.rejected,
              request_id,
            });
          }
        }

        return this.navigation?.replace?.(ScreenKey.HomeUnbindScreen);
      } else if (binding_status === BindingStatus.reject) {
        if (is_allow_resubmit) {
          return this.navigation?.replace?.(ScreenKey.ResubmitOnboarding, { request_id });
        } else {
          return this.navigation?.replace?.(ScreenKey.LockOutScreen, {
            issue: LockOutReason.rejected,
            request_id,
          });
        }
      }

      return this.navigation?.replace?.(ScreenKey.Error, { error: { message: 'Invalid state' } });
    } catch (e: any) {
      return this.navigation?.replace?.(ScreenKey.Error, { error: e });
    }
  }
}

const buildHomeScreenParams = (params: Record<string, any>) => {
  return Object.keys(params).reduce((result, key) => {
    if (params[key]) {
      result[key] = params[key];
    }
    return result;
  }, {} as Record<string, any>);
};

import { ScreenRouterModel } from './ScreenRouterModel';
import { MPOnboardingStatus, ScreenKey } from 'bnpl-shared/src/constants';
import { PartnerData } from 'bnpl-shared/src/types';
import { getOnboardingDataApi } from 'bnpl-shared/src/api/partner_lotte';
import { compareVersions, getNativeVersion } from 'bnpl-shared/src/shared/utils/versionUtils';
import { UtmCampaign } from 'bnpl-shared/src/types';
import { store } from 'bnpl-shared/src/redux/store';
import { getEmbeddedScreenKey } from './getEmbeddedScreenKey';

export class LotteScreenRouterModel extends ScreenRouterModel {
  async execute(partnerData?: PartnerData) {
    /*
     * Because below v9.21 Authen Challenge is not fully support from UM team, which is required from Lotte Finance
     * for new onboarding flow (nfc, face challenge, ...), so we need to lock out user if they are using old version
     */
    if (compareVersions(getNativeVersion(), '9.21') === -1) {
      return this.navigation?.replace?.(ScreenKey.OldVersionLockOutScreen);
    } else if (partnerData) {
      const utmCampaign = store.getState().utm.utmCampaign;
      const isEmbeddedOnboarding = utmCampaign && UtmCampaign.EmbeddedOnboarding === utmCampaign;
      if (!partnerData?.request_id || parseInt(partnerData?.request_id, 10) === 0) {
        return this.navigation.replace(ScreenKey.MPOnboardingScreen, { partnerCode: partnerData?.partner_code });
      } else {
        switch (partnerData?.status) {
          case MPOnboardingStatus.INIT:
          case MPOnboardingStatus.ELIGIBLE:
            return this.navigation.replace(ScreenKey.MPOnboardingScreen, {
              partnerCode: partnerData?.partner_code,
              requestId: partnerData?.request_id,
            });
          case MPOnboardingStatus.OTP_VERIFIED:
          case MPOnboardingStatus.WAITING_APPROVE:
          case MPOnboardingStatus.APPROVED:
            if (isEmbeddedOnboarding) {
              const embeddedScreenKey = await getEmbeddedScreenKey();
              return this.navigation?.replace?.(embeddedScreenKey);
            }
            return this.navigation.replace(
              ScreenKey.MPMainScreen,
              {
                partnerCode: partnerData?.partner_code,
              },
            );
          case MPOnboardingStatus.SUBMITTED:
            const response = await getOnboardingDataApi(partnerData?.request_id);
            return this.navigation.replace(ScreenKey.MPVerifyOTPScreen, {
              partnerCode: partnerData?.partner_code,
              requestId: partnerData?.request_id,
              phoneNumber: response?.basic_profile?.phone_number || '',
            });
          case MPOnboardingStatus.SUBMIT_FAIL:
          case MPOnboardingStatus.SUBMIT_PROCESSING:
            return this.navigation.replace(ScreenKey.MPSubmitDetailInfoScreen, {
              partnerCode: partnerData?.partner_code,
              requestId: partnerData?.request_id,
            });
          case MPOnboardingStatus.REJECTED:
            return this.navigation.replace(ScreenKey.MPResubmitScreen, {
              partnerCode: partnerData?.partner_code,
            });
        }
      }
    }
  }
}

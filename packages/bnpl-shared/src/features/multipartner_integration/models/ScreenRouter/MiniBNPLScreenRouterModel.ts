import { ScreenRouterModel } from './ScreenRouterModel';
import { ScreenKey } from 'bnpl-shared/src/constants';
import { PartnerData } from 'bnpl-shared/src/types';
import { compareVersions, getNativeVersion } from 'bnpl-shared/src/shared/utils/versionUtils';
import { getMiniBNPLScreenKeyFromOnboardingNextStep } from 'bnpl-shared/src/features/multipartner_integration/helpers';

export class MiniBNPLScreenRouterModel extends ScreenRouterModel {
  async execute(partnerData?: PartnerData) {
    /*
     * Because below v9.9 there is no support for face challenge flow from UM team, which is required from MiniBNPL Finance
     * (since 07/2024) for new onboarding flow, so we need to lock out user if they are using old version
     */
    if (compareVersions(getNativeVersion(), '9.9') === -1) {
      return this.navigation?.replace?.(ScreenKey.OldVersionLockOutScreen);
    } else if (partnerData) {
      const onboarding_next_step = partnerData?.onboarding_next_step;
      const screenKey = getMiniBNPLScreenKeyFromOnboardingNextStep(onboarding_next_step);
      this.navigation.replace(screenKey, { partnerCode: partnerData?.partner_code, partnerName: partnerData?.partner_name });
    }
  }
}

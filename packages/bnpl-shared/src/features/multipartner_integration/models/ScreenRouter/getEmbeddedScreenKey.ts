import { getRenewOverDraftStatusApi } from 'bnpl-shared/src/api/renew_overdraft';
import { ScreenKey } from 'bnpl-shared/src/constants';
import { setRenewInfo } from 'bnpl-shared/src/redux/featureRenewOverDraftReducer';
import { store } from 'bnpl-shared/src/redux/store';
import { RenewInfo, RenewStatusType, ResourceState } from 'bnpl-shared/src/types';

export const getEmbeddedScreenKey = async () => {
  const [renewScreenKey] = await Promise.all([
    getEmbeddedRenewScreenKey(),
  ]);

  return renewScreenKey || ScreenKey.MPWaitingApproval;
};

async function getEmbeddedRenewScreenKey() {
  try {
    const resp = await getRenewOverDraftStatusApi();
    if (resp) {
      const renewInfo: RenewInfo = {
        appendixId: resp.appendix_id,
        renewable: resp.is_renewable,
        renewStatus: resp.renew_status,
        expiredDate: resp.account_expired_date,
        validDate: resp.valid_at,
        isExpired: resp.is_expired,
        ctas: resp.ctas,
        description: resp.description,
      };
      store.dispatch(
        setRenewInfo({
          state: ResourceState.READY,
          data: renewInfo,
        }),
      );
      if (resp.is_renewable) {
        switch (resp.renew_status) {
          case RenewStatusType.OTP_VERIFIED:
          case RenewStatusType.WAITING_APPROVE:
            return '';
          case RenewStatusType.USER_SIGNED:
          case RenewStatusType.SUBMITTED_TO_BANK:
            return ScreenKey.EmbeddedRenewFaceAuthenScreen;
          case RenewStatusType.USER_CONFIRMED:
            return ScreenKey.EmbeddedRenewSignScreen;
          case RenewStatusType.NOT_STARTED:
          case RenewStatusType.INIT:
          case RenewStatusType.SUBMITTED_FAILED:
          case RenewStatusType.TEMPORARY_REJECTED:
          case RenewStatusType.PERMANENT_REJECTED:
          case RenewStatusType.APPROVED:
            return ScreenKey.EmbeddedRenewalInfoScreen;
          default:
            return '';
        }
      }
    }
  } catch (e: any) {
    return '';
  }
}

import {
  CIMBScreenRouterModel,
  LotteScreenRouterModel,
  ScreenRouterModel,
  MiniBNPLScreenRouterModel,
} from 'bnpl-shared/src/features/multipartner_integration/models';
import { PartnerCode } from 'bnpl-shared/src/constants';
import { TUseNavigation } from 'bnpl-shared/src/shared/navigation';

export * from './CIMBScreenRouterModel';
export * from './LotteScreenRouterModel';
export * from './ScreenRouterModel';
export * from './MiniBNPLScreenRouterModel';

export const ScreenRouterModelFactory = {
  get(navigation: TUseNavigation, partnerCode: PartnerCode): ScreenRouterModel {
    switch (partnerCode) {
      case PartnerCode.CIMB:
        return new CIMBScreenRouterModel(navigation);
      case PartnerCode.LOTTE:
        return new LotteScreenRouterModel(navigation);
      case PartnerCode.MINI_BNPL:
        return new MiniBNPLScreenRouterModel(navigation);
    }
  },
};

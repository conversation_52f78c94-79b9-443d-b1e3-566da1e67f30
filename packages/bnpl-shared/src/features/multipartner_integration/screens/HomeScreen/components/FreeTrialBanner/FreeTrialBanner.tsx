import React, { lazy, useRef, useState } from 'react';
import { View, TouchableOpacity, Animated } from 'react-native';
import { Spacer } from 'bnpl-shared/src/components/Spacer';
import { AppColors } from 'bnpl-shared/src/constants';
import { images } from 'bnpl-shared/src/res';
import { AppImage, AppText } from 'bnpl-shared/src/shared/react-native-customized';
import { calculateScaleDimension, StyleSheet } from 'bnpl-shared/src/shared/styles/StyleSheet';
import LinearGradient from 'react-native-linear-gradient';
import { avoidCacheImageUrl } from 'bnpl-shared/src/shared/utils';
import { TimeUnit } from 'bnpl-shared/src/types';
import LogoZ from 'bnpl-shared/src/components/SVGComponents/LogoZ';

const Rive = lazy(() => import('@rive-app/react-canvas'));

const ANIMATION_URL = avoidCacheImageUrl('https://simg.zalopay.com.vn/fs/bnpl/animation/fee_free_trial.riv', { cacheIn: TimeUnit.DAYS });
const EXPANDING_HEIGHT = calculateScaleDimension(94);
const COLLAPSED_HEIGHT = calculateScaleDimension(44);
const EXPANDING_LOGO_Z_CONTAINER_MARGIN_TOP = calculateScaleDimension(-16);

export default function FreeTrialBanner({ remainingDays, expireDate }: FreeTrialBannerProps) {
  const [expanding, setExpanding] = useState(false);
  const animationHeight = useRef(new Animated.Value(expanding ? EXPANDING_HEIGHT : COLLAPSED_HEIGHT)).current;
  const animationRotateArrow = useRef(new Animated.Value(expanding ? 0 : 1)).current;
  const animationRiveContainerMarginTop = useRef(new Animated.Value(expanding ? 0 : EXPANDING_LOGO_Z_CONTAINER_MARGIN_TOP)).current;

  function handleToggle() {
    Animated.parallel([
      Animated.timing(animationHeight, {
        toValue: expanding ? COLLAPSED_HEIGHT : EXPANDING_HEIGHT,
        duration: 200,
        useNativeDriver: false,
      }),
      Animated.timing(animationRotateArrow, {
        toValue: expanding ? 1 : 0,
        duration: 200,
        useNativeDriver: false,
      }),
      Animated.timing(animationRiveContainerMarginTop, {
        toValue: expanding ? EXPANDING_LOGO_Z_CONTAINER_MARGIN_TOP : 0,
        duration: 200,
        useNativeDriver: false,
      }),
    ]).start();
    setExpanding(expanding => !expanding);
  }
  const description = `Từ ngày ${expireDate}, áp dụng phí 20.000đ/tháng nếu có giao dịch qua Tài khoản Trả sau`
  return (
    <>
      <TouchableOpacity testID="free-trial-banner" activeOpacity={1} onPress={handleToggle}>
        <Animated.View style={[styles.root, { height: animationHeight }]}>
          <LinearGradient colors={['#FFFFFF', '#CCFEF4']} style={styles.linearGradient} />
          {expanding && (
            <View style={styles.logoZContainer}>
              <LogoZ width={calculateScaleDimension(118)} height={calculateScaleDimension(139)} opacity={0.08} color="#00CF6A" />
            </View>
          )}
          <View style={styles.content}>
            <View style={styles.description}>
              <View style={styles.titleWrapper}>
                <AppText size={14} height={18} bold color={AppColors.dark[500]}>
                  Bạn có <AppText size={14} height={18} bold color={AppColors.green[500]}>{remainingDays} ngày</AppText> miễn phí dịch vụ
                </AppText>
              </View>
              <Spacer height={expanding ? 4 : 12} />
              <AppText style={{ width: '100%' }} numberOfLines={3} size={12} height={16} color={AppColors.dark[300]}>
                {description}
              </AppText>
            </View>
            <Animated.View style={[styles.animationContainer, { marginTop: animationRiveContainerMarginTop }]}>
              <Rive src={ANIMATION_URL} />
            </Animated.View>
            <Animated.View
              style={{
                position: 'absolute',
                right: 16,
                top: 16,
                transform: [
                  {
                    rotate: animationRotateArrow.interpolate({
                      inputRange: [0, 1],
                      outputRange: ['-90deg', '90deg'],
                    }),
                  },
                ],
              }}>
              <AppImage source={images.IconArrowRight} width={16} height={16} />
            </Animated.View>
          </View>
        </Animated.View>
      </TouchableOpacity>
      <Spacer height={8} />
    </>
  );
}
interface FreeTrialBannerProps {
  remainingDays: number;
  expireDate: string;
}

const styles = StyleSheet.create({
  root: {
    position: 'relative',
    justifyContent: 'space-around',
    width: 343,
    borderRadius: 8,
    overflow: 'hidden',
    marginHorizontal: 16,
  },
  linearGradient: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
  },
  logoZContainer: {
    position: 'absolute',
    top: -8,
    right: -32,
  },
  animationContainer: {
    width: 70,
    height: 70,
    marginRight: 16,
  },
  animationContainerCollapsed: {
    marginTop: -16,
  },
  content: {
    position: 'absolute',
    top: 0,
    left: 0,
    width: '100%',
    flexDirection: 'row',
    justifyContent: 'space-between',
    paddingTop: 12,
    paddingHorizontal: 16,
  },
  titleWrapper: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  remaining: {
    backgroundColor: AppColors.background,
    borderRadius: 8,
    padding: 4,
    width: 67,
    height: 60,
    justifyContent: 'center',
    alignItems: 'center',
  },
  description: {
    flexDirection: 'column',
    alignItems: 'flex-start',
    flexShrink: 1,
  },
});

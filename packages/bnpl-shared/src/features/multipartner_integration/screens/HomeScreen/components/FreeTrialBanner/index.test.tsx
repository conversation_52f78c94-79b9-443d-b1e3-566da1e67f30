import React, { Suspense } from 'react';
import { fireEvent } from '@testing-library/react-native';
import { renderWithRedux } from 'bnpl-shared/src/jest/renderWithRedux';
import FreeTrialBanner from './index';
import { ExperimentName, AccountBenefit, ABTestingGroup } from 'bnpl-shared/src/types';
import { addDays, subDays, format } from 'date-fns';
import { AccountStatus } from 'bnpl-shared/src/constants';
import { store } from 'bnpl-shared/src/redux/store';
import { setAbTestingResult } from 'bnpl-shared/src/redux/abTestingReducer';
import { setAccountInfo } from 'bnpl-shared/src/redux/accountInfoReducer';
import { View } from 'react-native';

// Mock the lazy-loaded Rive component
jest.mock('@rive-app/react-canvas', () => {
  return {
    __esModule: true,
    default: () => null,
  };
});

// Mock the lazy import itself
jest.mock('react', () => {
  const actualReact = jest.requireActual('react');
  return {
    ...actualReact,
    lazy: (importFunction: () => Promise<any>) => {
      // Return a mock component instead of actually doing the lazy import
      return () => null;
    },
  };
});

// Mock usePartnerData hook
const mockGetChosenPartner = jest.fn();
jest.mock('bnpl-shared/src/features/multipartner_integration/helpers', () => ({
  ...jest.requireActual('bnpl-shared/src/features/multipartner_integration/helpers'),
  usePartnerData: () => ({
    getChosenPartner: mockGetChosenPartner,
    persistPartnerData: jest.fn(),
    persistChosenPartner: jest.fn(),
    getPartnerDataByCode: jest.fn(),
    fetchPartnerData: jest.fn(),
  }),
}));

describe('FreeTrialBanner', () => {
  const mockPartnerCode = 'LOTTE';
  const currentDate = new Date('2024-03-20');
  const endDate = addDays(currentDate, 5);

  beforeEach(() => {
    // Mock Date.now and constructor while preserving all static methods
    const OriginalDate = global.Date;
    jest.spyOn(Date, 'now').mockReturnValue(currentDate.getTime());
    
    // Create a mock constructor that returns the fixed date when called without arguments
    const MockDate = jest.fn((dateString?: string | number | Date) => {
      if (dateString === undefined) {
        return new OriginalDate(currentDate);
      }
      return new OriginalDate(dateString);
    }) as any;
    
    // Copy all static methods and properties from the original Date
    Object.setPrototypeOf(MockDate, OriginalDate);
    Object.getOwnPropertyNames(OriginalDate).forEach(name => {
      if (name !== 'prototype' && name !== 'length' && name !== 'name') {
        (MockDate as any)[name] = (OriginalDate as any)[name];
      }
    });
    
    // Override specific methods we want to mock
    MockDate.now = jest.fn(() => currentDate.getTime());
    
    global.Date = MockDate;
    
    // Mock getChosenPartner to return the partner code
    mockGetChosenPartner.mockReturnValue({
      partner_code: mockPartnerCode,
    });

    // Reset Redux state before each test
    store.dispatch(setAbTestingResult({
      experiment_key: ExperimentName.LOTTE_FREE_TRIAL,
      result: undefined as any
    }));
    
    store.dispatch(setAccountInfo({
      partner: mockPartnerCode,
      data: undefined as any
    }));
  });

  afterEach(() => {
    jest.clearAllMocks();
    jest.restoreAllMocks();
  });

  describe('Return null scenarios - AB Testing conditions', () => {

    it('should return null when user is not in free trial whitelist (control group)', () => {
      store.dispatch(setAbTestingResult({
        experiment_key: ExperimentName.LOTTE_FREE_TRIAL,
        result: ABTestingGroup.Control_Group
      }));
      
      store.dispatch(setAccountInfo({
        partner: mockPartnerCode,
        data: {
          id: '123',
          zalopay_id: '456',
          partner_code: mockPartnerCode,
          status: AccountStatus.ACTIVE,
          total_limit: '1000000',
          created_at: currentDate.toISOString(),
          statement_date: currentDate.toISOString(),
          statement_grace_full_date: endDate.toISOString(),
          benefits: [{
            code: AccountBenefit.FREE_TRIAL,
            start_time: currentDate.toISOString(),
            end_time: endDate.toISOString(),
            description: 'Free trial benefit'
          }]
        }
      }));

      const { queryByTestId } = renderWithRedux(
        <Suspense fallback={<View />}>
          <FreeTrialBanner />
        </Suspense>
      );
      expect(queryByTestId('free-trial-banner')).toBeNull();
    });

    it('should return null when AB testing result is undefined', () => {
      store.dispatch(setAbTestingResult({
        experiment_key: ExperimentName.LOTTE_FREE_TRIAL,
        result: undefined as any
      }));
      
      store.dispatch(setAccountInfo({
        partner: mockPartnerCode,
        data: {
          id: '123',
          zalopay_id: '456',
          partner_code: mockPartnerCode,
          status: AccountStatus.ACTIVE,
          total_limit: '1000000',
          created_at: currentDate.toISOString(),
          statement_date: currentDate.toISOString(),
          statement_grace_full_date: endDate.toISOString(),
          benefits: [{
            code: AccountBenefit.FREE_TRIAL,
            start_time: currentDate.toISOString(),
            end_time: endDate.toISOString(),
            description: 'Free trial benefit'
          }]
        }
      }));

      const { queryByTestId } = renderWithRedux(
        <Suspense fallback={<View />}>
          <FreeTrialBanner />
        </Suspense>
      );
      expect(queryByTestId('free-trial-banner')).toBeNull();
    });

    it('should return null when AB testing result is null', () => {
      store.dispatch(setAbTestingResult({
        experiment_key: ExperimentName.LOTTE_FREE_TRIAL,
        result: null as any
      }));
      
      store.dispatch(setAccountInfo({
        partner: mockPartnerCode,
        data: {
          id: '123',
          zalopay_id: '456',
          partner_code: mockPartnerCode,
          status: AccountStatus.ACTIVE,
          total_limit: '1000000',
          created_at: currentDate.toISOString(),
          statement_date: currentDate.toISOString(),
          statement_grace_full_date: endDate.toISOString(),
          benefits: [{
            code: AccountBenefit.FREE_TRIAL,
            start_time: currentDate.toISOString(),
            end_time: endDate.toISOString(),
            description: 'Free trial benefit'
          }]
        }
      }));

      const { queryByTestId } = renderWithRedux(
        <Suspense fallback={<View />}>
          <FreeTrialBanner />
        </Suspense>
      );
      expect(queryByTestId('free-trial-banner')).toBeNull();
    });

    it('should return null when AB testing result is empty string', () => {
      store.dispatch(setAbTestingResult({
        experiment_key: ExperimentName.LOTTE_FREE_TRIAL,
        result: ''
      }));
      
      store.dispatch(setAccountInfo({
        partner: mockPartnerCode,
        data: {
          id: '123',
          zalopay_id: '456',
          partner_code: mockPartnerCode,
          status: AccountStatus.ACTIVE,
          total_limit: '1000000',
          created_at: currentDate.toISOString(),
          statement_date: currentDate.toISOString(),
          statement_grace_full_date: endDate.toISOString(),
          benefits: [{
            code: AccountBenefit.FREE_TRIAL,
            start_time: currentDate.toISOString(),
            end_time: endDate.toISOString(),
            description: 'Free trial benefit'
          }]
        }
      }));

      const { queryByTestId } = renderWithRedux(
        <Suspense fallback={<View />}>
          <FreeTrialBanner />
        </Suspense>
      );
      expect(queryByTestId('free-trial-banner')).toBeNull();
    });

    it('should return null when AB testing result has different case but not variation_1', () => {
      store.dispatch(setAbTestingResult({
        experiment_key: ExperimentName.LOTTE_FREE_TRIAL,
        result: ABTestingGroup.Control_Group
      }));
      
      store.dispatch(setAccountInfo({
        partner: mockPartnerCode,
        data: {
          id: '123',
          zalopay_id: '456',
          partner_code: mockPartnerCode,
          status: AccountStatus.ACTIVE,
          total_limit: '1000000',
          created_at: currentDate.toISOString(),
          statement_date: currentDate.toISOString(),
          statement_grace_full_date: endDate.toISOString(),
          benefits: [{
            code: AccountBenefit.FREE_TRIAL,
            start_time: currentDate.toISOString(),
            end_time: endDate.toISOString(),
            description: 'Free trial benefit'
          }]
        }
      }));

      const { queryByTestId } = renderWithRedux(
        <Suspense fallback={<View />}>
          <FreeTrialBanner />
        </Suspense>
      );
      expect(queryByTestId('free-trial-banner')).toBeNull();
    });

    it('should render when AB testing result is variation_1', () => {
      store.dispatch(setAbTestingResult({
        experiment_key: ExperimentName.LOTTE_FREE_TRIAL,
        result: ABTestingGroup.Variation_1
      }));
      
      store.dispatch(setAccountInfo({
        partner: mockPartnerCode,
        data: {
          id: '123',
          zalopay_id: '456',
          partner_code: mockPartnerCode,
          status: AccountStatus.ACTIVE,
          total_limit: '1000000',
          created_at: currentDate.toISOString(),
          statement_date: currentDate.toISOString(),
          statement_grace_full_date: endDate.toISOString(),
          benefits: [{
            code: AccountBenefit.FREE_TRIAL,
            start_time: currentDate.toISOString(),
            end_time: endDate.toISOString(),
            description: 'Free trial benefit'
          }]
        }
      }));

      const { queryByTestId } = renderWithRedux(
        <Suspense fallback={<View />}>
          <FreeTrialBanner />
        </Suspense>
      );
      expect(queryByTestId('free-trial-banner')).toBeTruthy();
    });
  });

  describe('Return null scenarios - Free trial availability conditions', () => {
    beforeEach(() => {
      // Setup valid AB testing for these tests
      store.dispatch(setAbTestingResult({
        experiment_key: ExperimentName.LOTTE_FREE_TRIAL,
        result: ABTestingGroup.Variation_1
      }));
    });

    it('should return null when account info is undefined', () => {
      store.dispatch(setAccountInfo({
        partner: mockPartnerCode,
        data: undefined as any
      }));

      const { queryByTestId } = renderWithRedux(
        <Suspense fallback={<View />}>
          <FreeTrialBanner />
        </Suspense>
      );
      expect(queryByTestId('free-trial-banner')).toBeNull();
    });

    it('should return null when account info has no benefits', () => {
      store.dispatch(setAccountInfo({
        partner: mockPartnerCode,
        data: {
          id: '123',
          zalopay_id: '456',
          partner_code: mockPartnerCode,
          status: AccountStatus.ACTIVE,
          total_limit: '1000000',
          created_at: currentDate.toISOString(),
          statement_date: currentDate.toISOString(),
          statement_grace_full_date: endDate.toISOString(),
          benefits: []
        }
      }));

      const { queryByTestId } = renderWithRedux(
        <Suspense fallback={<View />}>
          <FreeTrialBanner />
        </Suspense>
      );
      expect(queryByTestId('free-trial-banner')).toBeNull();
    });

    it('should return null when account info has undefined benefits', () => {
      store.dispatch(setAccountInfo({
        partner: mockPartnerCode,
        data: {
          id: '123',
          zalopay_id: '456',
          partner_code: mockPartnerCode,
          status: AccountStatus.ACTIVE,
          total_limit: '1000000',
          created_at: currentDate.toISOString(),
          statement_date: currentDate.toISOString(),
          statement_grace_full_date: endDate.toISOString(),
          benefits: undefined as any
        }
      }));

      const { queryByTestId } = renderWithRedux(
        <Suspense fallback={<View />}>
          <FreeTrialBanner />
        </Suspense>
      );
      expect(queryByTestId('free-trial-banner')).toBeNull();
    });

    it('should return null when free trial benefit is not found', () => {
      store.dispatch(setAccountInfo({
        partner: mockPartnerCode,
        data: {
          id: '123',
          zalopay_id: '456',
          partner_code: mockPartnerCode,
          status: AccountStatus.ACTIVE,
          total_limit: '1000000',
          created_at: currentDate.toISOString(),
          statement_date: currentDate.toISOString(),
          statement_grace_full_date: endDate.toISOString(),
          benefits: [{
            code: 'other_benefit' as AccountBenefit,
            start_time: currentDate.toISOString(),
            end_time: endDate.toISOString(),
            description: 'Other benefit'
          }]
        }
      }));

      const { queryByTestId } = renderWithRedux(
        <Suspense fallback={<View />}>
          <FreeTrialBanner />
        </Suspense>
      );
      expect(queryByTestId('free-trial-banner')).toBeNull();
    });

    it('should return null when free trial has expired', () => {
      const pastStartDate = subDays(currentDate, 10);
      const pastEndDate = subDays(currentDate, 1);

      store.dispatch(setAccountInfo({
        partner: mockPartnerCode,
        data: {
          id: '123',
          zalopay_id: '456',
          partner_code: mockPartnerCode,
          status: AccountStatus.ACTIVE,
          total_limit: '1000000',
          created_at: currentDate.toISOString(),
          statement_date: currentDate.toISOString(),
          statement_grace_full_date: endDate.toISOString(),
          benefits: [{
            code: AccountBenefit.FREE_TRIAL,
            start_time: pastStartDate.toISOString(),
            end_time: pastEndDate.toISOString(),
            description: 'Free trial benefit'
          }]
        }
      }));

      const { queryByTestId } = renderWithRedux(
        <Suspense fallback={<View />}>
          <FreeTrialBanner />
        </Suspense>
      );
      expect(queryByTestId('free-trial-banner')).toBeNull();
    });

    it('should return null when free trial has not started yet', () => {
      const futureStartDate = addDays(currentDate, 1);
      const futureEndDate = addDays(currentDate, 10);

      store.dispatch(setAccountInfo({
        partner: mockPartnerCode,
        data: {
          id: '123',
          zalopay_id: '456',
          partner_code: mockPartnerCode,
          status: AccountStatus.ACTIVE,
          total_limit: '1000000',
          created_at: currentDate.toISOString(),
          statement_date: currentDate.toISOString(),
          statement_grace_full_date: endDate.toISOString(),
          benefits: [{
            code: AccountBenefit.FREE_TRIAL,
            start_time: futureStartDate.toISOString(),
            end_time: futureEndDate.toISOString(),
            description: 'Free trial benefit'
          }]
        }
      }));

      const { queryByTestId } = renderWithRedux(
        <Suspense fallback={<View />}>
          <FreeTrialBanner />
        </Suspense>
      );
      expect(queryByTestId('free-trial-banner')).toBeNull();
    });

    it('should return null when chosen partner is undefined', () => {
      const originalMock = mockGetChosenPartner.getMockImplementation();
      mockGetChosenPartner.mockReturnValue(undefined);

      store.dispatch(setAccountInfo({
        partner: mockPartnerCode,
        data: {
          id: '123',
          zalopay_id: '456',
          partner_code: mockPartnerCode,
          status: AccountStatus.ACTIVE,
          total_limit: '1000000',
          created_at: currentDate.toISOString(),
          statement_date: currentDate.toISOString(),
          statement_grace_full_date: endDate.toISOString(),
          benefits: [{
            code: AccountBenefit.FREE_TRIAL,
            start_time: currentDate.toISOString(),
            end_time: endDate.toISOString(),
            description: 'Free trial benefit'
          }]
        }
      }));

      const { queryByTestId } = renderWithRedux(
        <Suspense fallback={<View />}>
          <FreeTrialBanner />
        </Suspense>
      );
      expect(queryByTestId('free-trial-banner')).toBeNull();

      // Reset mock
      mockGetChosenPartner.mockImplementation(originalMock || (() => ({ partner_code: mockPartnerCode })));
    });

    it('should return null when chosen partner has no partner_code', () => {
      const originalMock = mockGetChosenPartner.getMockImplementation();
      mockGetChosenPartner.mockReturnValue({
        partner_code: undefined,
      });

      store.dispatch(setAccountInfo({
        partner: mockPartnerCode,
        data: {
          id: '123',
          zalopay_id: '456',
          partner_code: mockPartnerCode,
          status: AccountStatus.ACTIVE,
          total_limit: '1000000',
          created_at: currentDate.toISOString(),
          statement_date: currentDate.toISOString(),
          statement_grace_full_date: endDate.toISOString(),
          benefits: [{
            code: AccountBenefit.FREE_TRIAL,
            start_time: currentDate.toISOString(),
            end_time: endDate.toISOString(),
            description: 'Free trial benefit'
          }]
        }
      }));

      const { queryByTestId } = renderWithRedux(
        <Suspense fallback={<View />}>
          <FreeTrialBanner />
        </Suspense>
      );
      expect(queryByTestId('free-trial-banner')).toBeNull();

      // Reset mock
      mockGetChosenPartner.mockImplementation(originalMock || (() => ({ partner_code: mockPartnerCode })));
    });
  });

  describe('Return banner scenarios - Valid conditions', () => {
    beforeEach(() => {
      // Setup valid conditions for banner to render
      store.dispatch(setAbTestingResult({
        experiment_key: ExperimentName.LOTTE_FREE_TRIAL,
        result: ABTestingGroup.Variation_1
      }));
      
      store.dispatch(setAccountInfo({
        partner: mockPartnerCode,
        data: {
          id: '123',
          zalopay_id: '456',
          partner_code: mockPartnerCode,
          status: AccountStatus.ACTIVE,
          total_limit: '1000000',
          created_at: currentDate.toISOString(),
          statement_date: currentDate.toISOString(),
          statement_grace_full_date: endDate.toISOString(),
          benefits: [{
            code: AccountBenefit.FREE_TRIAL,
            start_time: currentDate.toISOString(),
            end_time: endDate.toISOString(),
            description: 'Free trial benefit'
          }]
        }
      }));
    });

    it('should render banner when all conditions are met', () => {
      const { queryByTestId, getByText } = renderWithRedux(
        <Suspense fallback={<View />}>
          <FreeTrialBanner />
        </Suspense>
      );
      expect(queryByTestId('free-trial-banner')).toBeTruthy();
      expect(getByText(/5 ngày/)).toBeTruthy();
    });

    it('should render banner with correct days remaining calculation', () => {
      const { getByText } = renderWithRedux(
        <Suspense fallback={<View />}>
          <FreeTrialBanner />
        </Suspense>
      );
      expect(getByText(/5 ngày/)).toBeTruthy();
    });

    it('should render banner with correct description and end date', () => {
      const { getByText } = renderWithRedux(
        <Suspense fallback={<View />}>
          <FreeTrialBanner />
        </Suspense>
      );
      const expectedEndDate = format(addDays(endDate, 1), 'dd/MM/yyyy');
      expect(getByText(new RegExp(expectedEndDate))).toBeTruthy();
    });

    it('should calculate remaining days correctly for different end times', () => {
      const oneDay = addDays(currentDate, 1);
      
      store.dispatch(setAccountInfo({
        partner: mockPartnerCode,
        data: {
          id: '123',
          zalopay_id: '456',
          partner_code: mockPartnerCode,
          status: AccountStatus.ACTIVE,
          total_limit: '1000000',
          created_at: currentDate.toISOString(),
          statement_date: currentDate.toISOString(),
          statement_grace_full_date: endDate.toISOString(),
          benefits: [{
            code: AccountBenefit.FREE_TRIAL,
            start_time: currentDate.toISOString(),
            end_time: oneDay.toISOString(),
            description: 'Free trial benefit'
          }]
        }
      }));

      const { getByText } = renderWithRedux(
        <Suspense fallback={<View />}>
          <FreeTrialBanner />
        </Suspense>
      );
      expect(getByText(/1 ngày/)).toBeTruthy();
    });

    it('should handle multiple benefits and find free trial benefit', () => {
      store.dispatch(setAccountInfo({
        partner: mockPartnerCode,
        data: {
          id: '123',
          zalopay_id: '456',
          partner_code: mockPartnerCode,
          status: AccountStatus.ACTIVE,
          total_limit: '1000000',
          created_at: currentDate.toISOString(),
          statement_date: currentDate.toISOString(),
          statement_grace_full_date: endDate.toISOString(),
          benefits: [
            {
              code: 'other_benefit_1' as AccountBenefit,
              start_time: currentDate.toISOString(),
              end_time: endDate.toISOString(),
              description: 'Other benefit 1'
            },
            {
              code: AccountBenefit.FREE_TRIAL,
              start_time: currentDate.toISOString(),
              end_time: endDate.toISOString(),
              description: 'Free trial benefit'
            },
            {
              code: 'other_benefit_2' as AccountBenefit,
              start_time: currentDate.toISOString(),
              end_time: endDate.toISOString(),
              description: 'Other benefit 2'
            }
          ]
        }
      }));

      const { queryByTestId } = renderWithRedux(
        <Suspense fallback={<View />}>
          <FreeTrialBanner />
        </Suspense>
      );
      expect(queryByTestId('free-trial-banner')).toBeTruthy();
    });
  });

  describe('Edge case scenarios', () => {
    beforeEach(() => {
      // Setup valid AB testing for these tests
      store.dispatch(setAbTestingResult({
        experiment_key: ExperimentName.LOTTE_FREE_TRIAL,
        result: ABTestingGroup.Variation_1
      }));
    });

    it('should handle numeric timestamp dates correctly', () => {
      const numericStartTime = currentDate.getTime().toString();
      const numericEndTime = endDate.getTime().toString();

      store.dispatch(setAccountInfo({
        partner: mockPartnerCode,
        data: {
          id: '123',
          zalopay_id: '456',
          partner_code: mockPartnerCode,
          status: AccountStatus.ACTIVE,
          total_limit: '1000000',
          created_at: currentDate.toISOString(),
          statement_date: currentDate.toISOString(),
          statement_grace_full_date: endDate.toISOString(),
          benefits: [{
            code: AccountBenefit.FREE_TRIAL,
            start_time: numericStartTime,
            end_time: numericEndTime,
            description: 'Free trial benefit'
          }]
        }
      }));

      const { queryByTestId } = renderWithRedux(
        <Suspense fallback={<View />}>
          <FreeTrialBanner />
        </Suspense>
      );
      expect(queryByTestId('free-trial-banner')).toBeTruthy();
    });

    it('should handle case-insensitive AB testing result matching', () => {
      store.dispatch(setAbTestingResult({
        experiment_key: ExperimentName.LOTTE_FREE_TRIAL,
        result: ABTestingGroup.Variation_1
      }));
      
      store.dispatch(setAccountInfo({
        partner: mockPartnerCode,
        data: {
          id: '123',
          zalopay_id: '456',
          partner_code: mockPartnerCode,
          status: AccountStatus.ACTIVE,
          total_limit: '1000000',
          created_at: currentDate.toISOString(),
          statement_date: currentDate.toISOString(),
          statement_grace_full_date: endDate.toISOString(),
          benefits: [{
            code: AccountBenefit.FREE_TRIAL,
            start_time: currentDate.toISOString(),
            end_time: endDate.toISOString(),
            description: 'Free trial benefit'
          }]
        }
      }));

      const { queryByTestId } = renderWithRedux(
        <Suspense fallback={<View />}>
          <FreeTrialBanner />
        </Suspense>
      );
      expect(queryByTestId('free-trial-banner')).toBeTruthy();
    });

    it('should return null when both conditions fail', () => {
      store.dispatch(setAbTestingResult({
        experiment_key: ExperimentName.LOTTE_FREE_TRIAL,
        result: ABTestingGroup.Control_Group
      }));
      
      store.dispatch(setAccountInfo({
        partner: mockPartnerCode,
        data: {
          id: '123',
          zalopay_id: '456',
          partner_code: mockPartnerCode,
          status: AccountStatus.ACTIVE,
          total_limit: '1000000',
          created_at: currentDate.toISOString(),
          statement_date: currentDate.toISOString(),
          statement_grace_full_date: endDate.toISOString(),
          benefits: []
        }
      }));

      const { queryByTestId } = renderWithRedux(
        <Suspense fallback={<View />}>
          <FreeTrialBanner />
        </Suspense>
      );
      expect(queryByTestId('free-trial-banner')).toBeNull();
    });
  });
}); 

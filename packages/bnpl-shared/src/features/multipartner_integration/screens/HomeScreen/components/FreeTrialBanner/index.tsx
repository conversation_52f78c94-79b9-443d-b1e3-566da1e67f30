import React from 'react';
import { Spacer } from 'bnpl-shared/src/components/Spacer';
import { ABTestingGroup, ExperimentName } from 'bnpl-shared/src/types';
import { useAppSelector } from 'bnpl-shared/src/redux/store';
import { usePartnerData } from 'bnpl-shared/src/features/multipartner_integration/helpers';
import { addDays, differenceInMinutes, format } from 'date-fns';
import FreeTrialBanner from './FreeTrialBanner';
import { checkFreeTrialAvailable } from 'bnpl-shared/src/utils/checkFreeTrialAvailable';

export default function FreeTrialBannerContainer() {
  const abTestingFreeTrial = useAppSelector(state => state.abTesting[ExperimentName.LOTTE_FREE_TRIAL]);
  const isInFreeTrialWhiteList = abTestingFreeTrial?.toLowerCase() === ABTestingGroup.Variation_1.toLowerCase();
  const { getChosenPartner } = usePartnerData();
  const chosenPartnerCode = getChosenPartner()?.partner_code;
  const accountInfo = useAppSelector(state => state.accountInfo[chosenPartnerCode || '']);
  const freeTrialResult = checkFreeTrialAvailable(accountInfo);
  
  if (!isInFreeTrialWhiteList || !freeTrialResult.available) {
    return null;
  }
  
  const { benefit } = freeTrialResult;
  const remainingDays = Math.ceil(differenceInMinutes(new Date(benefit.endTime), new Date()) / (24 * 60));
  return (
    <>
      <FreeTrialBanner
        remainingDays={remainingDays}
        // endTime is 23:59:59, so we need to add 1 day to get the correct date
        expireDate={format(addDays(new Date(benefit.endTime), 1), 'dd/MM/yyyy')}
      />
      <Spacer height={8} />
    </>
  );
}

import withViewModel from 'bnpl-shared/src/hooks/withViewModel';
import { useHomeScreenViewModel } from 'bnpl-shared/src/features/multipartner_integration/screens/HomeScreen/useHomeScreenViewModel';
import React, { FC, useEffect, useMemo } from 'react';
import { AppColors, MULTI_PARTNER_TAB_BAR_CONFIG, PartnerCode } from 'bnpl-shared/src/constants';
import { Platform, ScrollView, View } from 'react-native';
import { HeaderSlider } from 'bnpl-shared/src/screens/HomeScreen/components/HeaderSlider';
import { AdsInventoryId } from 'bnpl-shared/src/types';
import { BalanceSummarySection } from 'bnpl-shared/src/screens/HomeScreen/components/BalanceSummarySection';
import { Spacer } from 'bnpl-shared/src/components/Spacer';
import { ServicesSection } from 'bnpl-shared/src/screens/HomeScreen/components/ServicesSection';
import { StyleSheet } from 'bnpl-shared/src/shared/styles/StyleSheet';
import { Colors } from 'bnpl-shared/src/shared/styles/StyleUtils';
import { withZpiBottomTabbar } from 'bnpl-shared/src/components/ZPITabs/withZpiBottomTabbar';
import { AppRefreshControl, RepaymentWidget } from 'bnpl-shared/src/components';
import { NavigationContext } from 'bnpl-shared/src/utils/NavigationContext';
import PromotionSection from 'bnpl-shared/src/features/promotion_section';
import { ExternalComponentIntegration } from 'bnpl-shared/src/screens/HomeScreen/components/ExternalComponentIntegration';
import { RecentTransactionSection } from 'bnpl-shared/src/screens/HomeScreen/components/RecentTransactionSection';
import { throttle } from 'lodash';
import { usePartnerData } from '../../helpers';
import MiniBNPLHomeScreenView from 'bnpl-shared/src/features/mini_bnpl/screens/HomeScreen'
import FreeTrialBanner from './components/FreeTrialBanner';

const HomeScreenView: FC<ReturnType<typeof useHomeScreenViewModel>> = props => {
  const { getChosenPartner, fetchPartnerData } = usePartnerData();
  const chosenPartner = getChosenPartner();
  useEffect(() => {
    fetchPartnerData();
  }, []);

  if (chosenPartner?.partner_code === PartnerCode.MINI_BNPL) {
    return <MiniBNPLHomeScreenView {...props} />;
  }

  return (
    <WithBottomTabBarHomeScreen
      {...props}
    />
  )
};

const DefaultHomeScreen: FC<ReturnType<typeof useHomeScreenViewModel>> = props => {
  const handleScroll = useMemo(() => {
    return throttle(
      (height: number, offset: number) => {
        props.scrollRef.current = (offset / height) * 100;
      },
      300,
      { leading: true },
    );
  }, []);

  return (
    <NavigationContext.Provider value={props.navigation}>
      <ScrollView
        style={styles.root}
        showsVerticalScrollIndicator={false}
        onScroll={e => {
          const height = e.nativeEvent.contentSize.height - e.nativeEvent.layoutMeasurement.height;
          const offset = e.nativeEvent.contentOffset.y;
          handleScroll(height, offset);
        }}
        contentContainerStyle={styles.contentContainer}
        refreshControl={
          <AppRefreshControl refreshing={props.isRefreshing} onRefresh={props.actions.onRequestRefreshData} />
        }>
        <>
          <HeaderSlider
            inventoryId={AdsInventoryId.HEADER_SLIDER_LOTTE}
            onBannerPress={url => {
              props.actions.handleAdBannerPress(url);
            }}
          />
          <View style={{ ...styles.sectionWrapper, marginBottom: 0, marginHorizontal: 16, marginTop: 160 }}>
            <BalanceSummarySection
              showOutStandingBalanceTooltip={props.obTooltipVisible !== 0}
              onRequestToogleOverRepaymentModal={props.actions.showOverRepaymentModal}
              isPreviewMode={props.isPreviewMode}
            />
            {props.chosenPartner && (
              <RepaymentWidget
                partnerCode={props.chosenPartner.partner_code}
                onAction={props.actions.handleAction}
                isPreviewMode={props.isPreviewMode}
              />
            )}
          </View>
          <FreeTrialBanner />

          {!props.isPreviewMode && Platform.OS === 'web' ? <ExternalComponentIntegration /> : null}

          <PromotionSection onItemPress={props?.actions?.handleAdBannerPress} />

          <View style={styles.body}>
            {!props.isPreviewMode && (
              <RecentTransactionSection
                onDataReady={transactions => {
                  props.actions.onTransactionDataReady(transactions);
                }}
              />
            )}
          </View>

          <View style={styles.body}>
            <Spacer height={8} />
            <ServicesSection />
          </View>
        </>
      </ScrollView>
    </NavigationContext.Provider>
  );
};

const styles = StyleSheet.create({
  pattern: {
    height: 97,
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
  },
  body: {
    backgroundColor: Colors.background2,
    overflow: 'hidden',
    paddingHorizontal: 16,
  },
  root: {
    flex: 1,
    backgroundColor: Colors.background2,
  },
  container: {},
  contentContainer: {
    paddingBottom: 0,
  },
  moneyIcon: {
    width: 16,
    height: 16,
    marginRight: 4,
  },
  sectionWidget: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginBottom: 16,
  },
  sectionWrapper: {
    margin: 16,
  },
  sectionGutter: {
    marginBottom: 16,
  },
  autoRepayController: {
    backgroundColor: AppColors.background,
    borderRadius: 8,
    marginBottom: 16,
  },
});

const WithBottomTabBarHomeScreen = withZpiBottomTabbar(withViewModel(useHomeScreenViewModel, DefaultHomeScreen), MULTI_PARTNER_TAB_BAR_CONFIG);

export default HomeScreenView;

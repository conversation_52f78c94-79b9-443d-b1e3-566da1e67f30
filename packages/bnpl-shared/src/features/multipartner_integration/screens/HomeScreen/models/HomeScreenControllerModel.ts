import { AppColors, MPOnboardingStatus } from 'bnpl-shared/src/constants';
import { getOnboardingDataApi } from 'bnpl-shared/src/api/partner_lotte';
import { getAccountStatementApi } from 'bnpl-shared/src/api/partner_lotte/getAccountStatementApi';
import { setLotteStatement } from 'bnpl-shared/src/redux/lotteStatementReducer';
import { ABTestingGroup, ExperimentName, ResourceState } from 'bnpl-shared/src/types';
import { store } from 'bnpl-shared/src/redux/store';
import { getOnboardingStatusApi } from 'bnpl-shared/src/api/mini-bnpl';
import { getTotalOutstandingApi } from "bnpl-shared/src/api/mini-bnpl/getTotalOutstandingApi";
import { setMiniBnplTotalOutstanding } from "bnpl-shared/src/redux/miniBnplTotalOutstandingReducer";
import { ReactElement, ReactNode } from 'react';
import { getABTestExperimentKeyByName } from 'bnpl-shared/src/utils/getABTestExperimentKey';
import { View } from 'react-native';

export abstract class HomeScreenController {
  abstract getOnboardingStatus(requestId?: string): Promise<MPOnboardingStatus>;
  abstract fetchStatement(account_id: string): void;
  abstract getApprovedDialogBadgeContent(): {
    content: string;
    backgroundColor: string;
    color: string;
  } | null;
}

export class LotteHomeScreenController extends HomeScreenController {
  async getOnboardingStatus(requestId: string): Promise<MPOnboardingStatus> {
    const resp = await getOnboardingDataApi(requestId);
    return resp.binding_log_status;
  }

  fetchStatement(account_id: string): void {
    (async () => {
      try {
        store.dispatch(setLotteStatement({ state: ResourceState.LOADING }));
        const statement = await getAccountStatementApi(account_id);
        store.dispatch(setLotteStatement({ state: ResourceState.READY, data: statement }));
      } catch (err) {
        store.dispatch(setLotteStatement({ state: ResourceState.FAIL }));
      }
    })();
  }

  getApprovedDialogBadgeContent(): {
    content: string;
    backgroundColor: string;
    color: string;
  } | null {
    const lotteFreeTrialTesting = store.getState().abTesting[ExperimentName.LOTTE_FREE_TRIAL];
    if (lotteFreeTrialTesting?.toLowerCase() === ABTestingGroup.Variation_1.toLowerCase()) {
      return {
        content: 'Miễn phí dịch vụ 30 ngày',
        backgroundColor: AppColors.green[50],
        color: AppColors.green[700],
      }
    }
    return null;
  }
}

export class MiniBNPLHomeScreenController extends HomeScreenController {
  async getOnboardingStatus(): Promise<MPOnboardingStatus> {
    const resp = await getOnboardingStatusApi();
    return resp.status;
  }

  //WIP
  fetchStatement(_account_id: string): void {
    (async () => {
      try {
        store.dispatch(setMiniBnplTotalOutstanding({ state: ResourceState.LOADING }));
        const totalOutstandingResp = await getTotalOutstandingApi(_account_id);
        store.dispatch(setMiniBnplTotalOutstanding({
          state: ResourceState.READY,
          data: {
            dueStatus: totalOutstandingResp.due_status,
            totalDueAmount: Number(totalOutstandingResp.total_due_amount),
            dueDistance: Number(totalOutstandingResp.due_distance),
          },
        }));
      } catch (err) {
        store.dispatch(setMiniBnplTotalOutstanding({ state: ResourceState.FAIL }));
      }
    })();
  }

  getApprovedDialogBadgeContent(): {
    content: string;
    backgroundColor: string;
    color: string;
  } | null {
    return null;
  }
}

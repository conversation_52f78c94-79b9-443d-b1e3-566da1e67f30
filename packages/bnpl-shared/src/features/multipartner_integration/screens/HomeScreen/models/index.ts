import { HomeScreenController, LotteHomeScreenController, MiniBNPLHomeScreenController } from './HomeScreenControllerModel';
import { PartnerCode } from 'bnpl-shared/src/constants';

export * from './HomeScreenControllerModel';

export const HomeScreenControllerFactory = {
  get(partnerCode: PartnerCode): HomeScreenController {
    switch (partnerCode) {
      default:
      case PartnerCode.LOTTE:
        return new LotteHomeScreenController();
      case PartnerCode.MINI_BNPL:
        return new MiniBNPLHomeScreenController();
    }
  },
};

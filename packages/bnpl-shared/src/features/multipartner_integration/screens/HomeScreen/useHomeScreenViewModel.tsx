import { INVALID_ACCOUNT_ID, MPOnboardingStatus, PartnerCode, ScreenKey } from 'bnpl-shared/src/constants';
import { useNavigation } from 'bnpl-shared/src/shared/navigation';
import { closeApp, launchDeepLink } from 'bnpl-shared/src/shared/ZaloPayModules';
import React, { useCallback, useEffect, useRef, useState } from 'react';
import { Platform } from 'react-native';
import { isZLPDeeplink } from 'bnpl-shared/src/shared/utils/isZLPDeeplink';
import { avoidCacheImageUrl } from 'bnpl-shared/src/shared/utils';
import { ContentType } from 'bnpl-shared/src/screens/Webview/Webview';
import { usePartnerData } from 'bnpl-shared/src/features/multipartner_integration/helpers/usePartnerData';
import { useUserBalanceController } from 'bnpl-shared/src/utils/useUserBalanceController';
import { getAccountInfoApi } from 'bnpl-shared/src/api/partner_lotte/getAccountInfoApi';
import {
  AdsInventoryId,
  ConfigurableScreenProps,
  PartnerData,
  PredefinedCTA,
  Transaction,
  TransactionType,
} from 'bnpl-shared/src/types';
import { HomeScreenControllerFactory } from 'bnpl-shared/src/features/multipartner_integration/screens/HomeScreen/models';
import { useHomePreviewController } from 'bnpl-shared/src/utils/useHomePreviewController';
import { InfoModalService, ModalType } from 'bnpl-shared/src/services';
import { AllServicesModal } from 'bnpl-shared/src/screens/HomeScreen/components/ServicesSection';
import { windowHeight } from 'bnpl-shared/src/shared/styles/StyleUtils';
import { EdgeCaseModalUI } from 'bnpl-shared/src/screens/onboarding-flow/OnboardingScreen/bod1/EdgeCaseModal/EdgeCaseModalUI';
import { useAdPopupHandler } from 'bnpl-shared/src/components/AdPopup/useAdPopupHandler';
import {
  trackHomePreviewApprovedDialogShow,
  trackHomePreviewLoad,
  trackHomeScreenCTAClick,
} from 'bnpl-shared/src/screens/HomeScreen/tracking';
import { Event, useTouchTracker } from 'bnpl-shared/src/utils/tracking';
import { useMultiPartnerNavigate } from 'bnpl-shared/src/features/multipartner_integration/helpers';
import { isNavigationDeeplink, useDeeplinkHandler } from 'bnpl-shared/src/deeplink';
import { useUtmHandler } from 'bnpl-shared/src/hooks/useUtmHandler';
import { ApprovedDialog } from 'bnpl-shared/src/screens/HomeScreen/components/ApprovedDialog';
import { OverRepaymentBalanceUI } from 'bnpl-shared/src/screens/HomeScreen/components/OverRepaymentBalanceModal';
import { toNumber } from 'lodash';
import { getCurrentDate } from 'bnpl-shared/src/utils';
import { useRemoteConfigs } from 'bnpl-shared/src/remote_configs/useRemoteConfigs';

type NavigationParams = {
  partnerCode: PartnerCode;
};

const PENDING_STATUS = [MPOnboardingStatus.WAITING_APPROVE, MPOnboardingStatus.OTP_VERIFIED];

export type HomeScreenProps = {
  isRefreshing: boolean;
  chosenPartner?: PartnerData;
  isPreviewMode: boolean;
  obTooltipVisible: number;
  scrollRef: any;
  actions: {
    closeApp: () => void;
    onRequestRefreshData: () => Promise<void>;
    handleAdBannerPress: (url: string) => void;
    handleAction: (actionName: string) => void;
    showOverRepaymentModal?: () => void;
    onTransactionDataReady: (transactions: Transaction[]) => void;
  };
} & ConfigurableScreenProps<NavigationParams, any>;

export const useHomeScreenViewModel = (props: any): HomeScreenProps => {
  const { checkAndShowAdPopup } = useAdPopupHandler(AdsInventoryId.POPUP_HOME_LOTTE);
  const [refreshing, setRefreshing] = useState(false);
  const navigation = useNavigation<NavigationParams>(props);
  const { handleDeeplink } = useDeeplinkHandler(navigation);
  const { getConfig } = useRemoteConfigs();
  const { getChosenPartner, fetchPartnerData } = usePartnerData();
  const chosenPartner = getChosenPartner();
  const { utmCampaign, markUtmCampaignUsed } = useUtmHandler();
  const { isPreviewMode } = useHomePreviewController(chosenPartner?.partner_code || PartnerCode.LOTTE);
  const { fetchUserBalance } = useUserBalanceController();
  const intervalIdRef = useRef<any>();
  const currentScrollPercent = useRef<number>(0);
  const [obTooltipVisible, setObTooltipVisible] = useState(0); // 0 || > 1 : not show, 1: show,
  const homeScreenController = HomeScreenControllerFactory.get(chosenPartner?.partner_code || PartnerCode.LOTTE);
  useTouchTracker(payload => {
    if (payload.event === Event.CTA_CLICK) {
      trackHomeScreenCTAClick({ ...payload.data, cta: payload.source });
    }
  });
  const mpNavigator = useMultiPartnerNavigate(navigation);

  const handleAction = (actionName: string) => {
    switch (actionName) {
      case 'open-repayment':
        mpNavigator.navigateTo('repay');
        break;
    }
  };

  const fetchAccountInfo = (accountId: string) => {
    (async () => {
      try {
        await getAccountInfoApi(accountId);
      } catch (err) { }
    })();
  };

  const fetchAccountData = useCallback(async () => {
    if (chosenPartner?.account_id && chosenPartner.account_id !== INVALID_ACCOUNT_ID) {
      fetchUserBalance(chosenPartner?.account_id);
      homeScreenController.fetchStatement(chosenPartner?.account_id);
      fetchAccountInfo(chosenPartner?.account_id);
    }
  }, [chosenPartner?.account_id]);

  const onRequestRefreshData = async () => {
    try {
      setRefreshing(true);
      await fetchAccountData();
    } finally {
      setRefreshing(false);
    }
  };

  const onTransactionDataReady = (transactions: Transaction[]) => {
    const availableLimitDelayInMinute = toNumber(getConfig('available_limit_delay_in_minute') || 0);
    if (transactions.length && availableLimitDelayInMinute > 0) {
      let hasRecentRepayTransaction =
        transactions.find(t => {
          const updateTimeIsBelowDelayLimit =
            getCurrentDate().getTime() - new Date(t.updated_at).getTime() < availableLimitDelayInMinute * 60 * 1000;
          return t.type === TransactionType.REPAYMENT && updateTimeIsBelowDelayLimit;
        }) !== undefined;
      if (hasRecentRepayTransaction && currentScrollPercent.current < 20) {
        setObTooltipVisible(prevState => prevState + 1);
      }
    }
  };

  const showOverRepaymentModal = () => {
    InfoModalService.showModal({
      screen: (
        <OverRepaymentBalanceUI
          onRequestOpenHistory={() => {
            mpNavigator.navigateTo('trans_his');
          }}
          onRequestClose={InfoModalService.hideModal}
        />
      ),
      type: ModalType.BOTTOM_SHEET,
      bottomSheetProps: {
        title: 'Chi tiết thanh toán dư',
      },
    });
  };

  const handleAdBannerPress = (url: string) => {
    if (Platform.OS === 'web') {
      window?.ZPI_SPA_SDK?.navigateTo?.(url);
    } else {
      if (isZLPDeeplink(url)) {
        launchDeepLink(url);
      } else {
        navigation?.navigate(ScreenKey.WebviewScreen, {
          link: avoidCacheImageUrl(url),
          name: 'Tài Khoản Trả Sau',
          type: ContentType.LINK,
        });
      }
    }
  };

  const showAllServicesModal = () =>
    InfoModalService.showModal({
      screen: <AllServicesModal />,
      type: ModalType.BOTTOM_SHEET,
      bottomSheetProps: {
        title: 'Dịch vụ thanh toán',
        containerStyle: { height: windowHeight - 240 },
      },
    });

  const showApprovedDialog = () => {
    trackHomePreviewApprovedDialogShow();
    InfoModalService.showModal({
      screen: (
        <ApprovedDialog
          onContinue={() => {
            setTimeout(showAllServicesModal, 300);
          }}
          badgeContent={homeScreenController.getApprovedDialogBadgeContent()}
        />
      ),
      type: ModalType.BOTTOM_SHEET,
      options: {
        dequeue: true,
      },
    });
  };

  const showRejectedDialog = () =>
    InfoModalService.showModal({
      screen: (
        <EdgeCaseModalUI
          edgeCaseInfo={{
            ctas: [PredefinedCTA.BACK_HOMEPAGE],
            title: 'Rất tiếc',
            description: `Bạn không đáp ứng đầy đủ điều kiện của ${chosenPartner?.partner_name}. Để biết thêm chi tiết, vui lòng liên hệ ${chosenPartner?.partner_name}.`,
          }}
          handleCtaAction={(_: string) => {
            InfoModalService.hideModal();
          }}
        />
      ),
      type: ModalType.BOTTOM_SHEET,
      bottomSheetProps: {
        showHeader: false,
      },
    });

  const handleOnboardingStatus = useCallback(
    (requestId?: string) => {
      (async () => {
        try {
          const status = await homeScreenController.getOnboardingStatus(requestId);
          if (status === MPOnboardingStatus.APPROVED) {
            showApprovedDialog();
            //fetch lastest routing info data, which is the only way to get created account_id
            await fetchPartnerData();
            await fetchAccountData();
          } else if (status === MPOnboardingStatus.REJECTED) {
            showRejectedDialog();
          }
          if (status === MPOnboardingStatus.APPROVED || status === MPOnboardingStatus.REJECTED) {
            clearInterval(intervalIdRef.current);
          }
        } catch (e) { }
      })();
    },
    [fetchUserBalance, chosenPartner],
  );

  useEffect(() => {
    (async () => {
      await fetchAccountData();
    })();
  }, [chosenPartner?.account_id]);

  useEffect(() => {
    const requestId = chosenPartner?.request_id;
    if (chosenPartner?.status && PENDING_STATUS.includes(chosenPartner?.status)) {
      trackHomePreviewLoad({ status: chosenPartner?.status });
      handleOnboardingStatus(requestId);
      intervalIdRef.current = setInterval(() => {
        handleOnboardingStatus(requestId);
      }, 20000);
      return () => clearInterval(intervalIdRef.current);
    }
    checkAndShowAdPopup();
  }, []);

  useEffect(() => {
    if (utmCampaign && isNavigationDeeplink(utmCampaign)) {
      handleDeeplink(utmCampaign);
      markUtmCampaignUsed();
    }
  }, [utmCampaign]);

  return {
    obTooltipVisible,
    isRefreshing: refreshing,
    isPreviewMode,
    chosenPartner,
    navigation,
    uiConfig: {},
    scrollRef: currentScrollPercent,
    actions: {
      closeApp,
      onRequestRefreshData,
      handleAdBannerPress,
      handleAction,
      onTransactionDataReady,
      showOverRepaymentModal,
    },
  };
};

import React, { FC, useEffect, useLayoutEffect, useMemo, useRef, useState } from 'react';
import { AppButton, AppImage, AppText } from 'bnpl-shared/src/shared/react-native-customized';
import { images } from 'bnpl-shared/src/res';
import { View } from 'react-native';
import { CloseButton } from 'bnpl-shared/src/shared/CloseButton';
import { Colors, StyleUtils } from 'bnpl-shared/src/shared/styles/StyleUtils';
import { avoidCacheImageUrl } from 'bnpl-shared/src/shared/utils';
import ScaledImage from 'bnpl-shared/src/shared/ScaledImage/ScaledImage';
import { LinkButton } from 'bnpl-shared/src/shared/LinkButton';
import { ChevronIcon } from 'bnpl-shared/src/shared/ChevronIcon';
import { Spacer } from 'bnpl-shared/src/components/Spacer';
import { StyleSheet } from 'bnpl-shared/src/shared/styles/StyleSheet';
import { AppColors } from 'bnpl-shared/src/constants';
import { PartnerDisclaimerText } from './components/PartnerDisclaimerText';
import { useOnboardingViewModel } from './useOnboardingViewModel';
import { FullScreenFormLayout } from 'bnpl-shared/src/components/layouts/FullScreenFormLayout';
import { getBottomSafe } from 'bnpl-shared/src/utils';
import {
  trackOnboardingCloseButton,
  trackOnboardingCta,
  trackOnboardingLoad,
  trackOnboardingRewardBannerClickBanner,
  trackOnboardingRewardBannerClickShowAd,
  trackOnboardingRewardBannerLoad,
  trackOnboardingScroll,
} from 'bnpl-shared/src/screens/onboarding-flow/OnboardingScreen/tracking';
import { throttle } from 'lodash';
import { LinkType, openCommonLink } from 'bnpl-shared/src/utils/openCommonLink';
import { ABTestingGroup, ExperimentName, RewardBannerKey, TimeUnit } from 'bnpl-shared/src/types';
import RewardBanner, { styles as rewardBannerStyle } from 'bnpl-shared/src/features/reward_banner';
import AssetFrameMock from 'bnpl-shared/src/components/AssetFrame/AssetFrameMock';
import { FeeFreeTrialBanner, FeeFreeTrialSection } from './components/FeeFreeTrial';
import { useAppSelector } from 'bnpl-shared/src/redux/store';

const FREE_TRIAL_IMAGE_URL_1 = 'https://simg.zalopay.com.vn/fs/bnpl/image/ab_free_trial_landing_lotte_1.png';
const FREE_TRIAL_IMAGE_URL_2 = 'https://simg.zalopay.com.vn/fs/bnpl/image/ab_free_trial_landing_lotte_2.png';
const FREE_TRIAL_IMAGE_URL_3 = 'https://simg.zalopay.com.vn/fs/bnpl/image/ab_free_trial_landing_lotte_3.png';


const LotteOnboardingScreen: FC<ReturnType<typeof useOnboardingViewModel>> = props => {
  const abTestingFreeTrial = useAppSelector(state => state.abTesting[ExperimentName.LOTTE_FREE_TRIAL]);
  const isInFreeTrialWhiteList = abTestingFreeTrial?.toLowerCase() === ABTestingGroup.Variation_1.toLowerCase();
  const [disableButton, setDisableButton] = useState(false);
  const [isInView, setIsInView] = useState(true);
  const freeTrialMainRef = useRef<HTMLDivElement>(null);

  const createImageInjectScript = (imageUrl: string) => {
    return `
      document.getElementById("image").src = "${imageUrl}";
      sendMessageToRN(document.getElementById("image").height);
      true;
     `;
  };

  const handleScroll = useMemo(() => {
    return throttle(
      (height: number, offset: number) => {
        trackOnboardingScroll({ percents: (offset / height) * 100 });
      },
      300,
      { leading: true },
    );
  }, []);

  useEffect(() => {
    trackOnboardingLoad('default');
  }, []);

  useLayoutEffect(() => {
    if (!isInFreeTrialWhiteList || !freeTrialMainRef.current) return;

    let intersectionObserver: IntersectionObserver | null = null;


    const setupIntersectionObserver = () => {
      intersectionObserver = new IntersectionObserver(
        ([entry]) => {
          setIsInView(entry.isIntersecting);
        },
        {
          threshold: 0.1,
        }
      );

      if (freeTrialMainRef.current) {
        intersectionObserver.observe(freeTrialMainRef.current);
      }
    };

    const timeoutId = setTimeout(setupIntersectionObserver, 50);

    return () => {
      clearTimeout(timeoutId);
      if (intersectionObserver) {
        if (freeTrialMainRef.current) {
          intersectionObserver.unobserve(freeTrialMainRef.current);
        }
        intersectionObserver.disconnect();
      }
    };
  }, [isInFreeTrialWhiteList]);

  return (
    <FullScreenFormLayout
      style={styles.root}
      contentStyles={styles.content}
      actionsStyles={rewardBannerStyle.action}
      onScroll={e => {
        // contentOffset: distance between the current viewport to the beginning of the scrollable content
        // contentSize: size of the scrollable content
        // layoutMeasurement: size of the current viewport.
        const height = e.nativeEvent.contentSize.height - e.nativeEvent.layoutMeasurement.height;
        const offset = e.nativeEvent.contentOffset.y;
        handleScroll(height, offset);
      }}
      content={
        <>
          <AppImage
            resizeMode={'cover'}
            style={styles.coverImage}
            source={images.ImageOnboardingBackground}
            height={220}
            width={375}
          />
          <View style={styles.welcome}>
            <CloseButton
              onPress={() => {
                trackOnboardingCloseButton('default');
                props.actions.closeApp();
              }}
              style={styles.close}
            />
            <AppText color={Colors.text2} height={18}>
              Chào mừng {props.displayName}
            </AppText>
          </View>
          {isInFreeTrialWhiteList ? (
            <View>
              <ScaledImage
                uri={avoidCacheImageUrl(FREE_TRIAL_IMAGE_URL_1, { cacheIn: TimeUnit.DAYS })}
                width={375}
              />
              <Spacer height={16} />
              <div ref={freeTrialMainRef}>
                <FeeFreeTrialSection initialExpanding={false} />
              </div>
              <Spacer height={16} />
              <ScaledImage
                uri={avoidCacheImageUrl(FREE_TRIAL_IMAGE_URL_2, { cacheIn: TimeUnit.DAYS })}
                width={375}
              />
              <Spacer height={16} />
              <ScaledImage
                uri={avoidCacheImageUrl(FREE_TRIAL_IMAGE_URL_3, { cacheIn: TimeUnit.DAYS })}
                width={375}
              />
            </View>
          ) : (
            <ScaledImage
              uri={avoidCacheImageUrl(props.uiConfig!!.getLandingImageUrl(), { cacheIn: TimeUnit.DAYS })}
              width={375}
            />
          )}
          <View style={styles.detailLink}>
            <LinkButton
              onPress={() => {
                openCommonLink(LinkType.LANDING_PAGE, props.navigation);
              }}
              bold>
              Chi tiết sản phẩm
            </LinkButton>
            <ChevronIcon direction={'right'} />
          </View>

          <Spacer height={12} />
          <AssetFrameMock assetInventoryId={props?.assetFrameInventoryId} />

          <View style={styles.disclaimer}>
            {props.uiConfig && (
              <PartnerDisclaimerText
                setDisableActionButton={setDisableButton}
                partner={props.uiConfig!!.getPartnerCode()}
                data={props.uiConfig}
              />
            )}
          </View>
          <View style={[StyleUtils.flexRow, StyleUtils.centered, styles.section]}>
            <AppText size={12} height={16}>
              Cung cấp bởi
            </AppText>
            <Spacer width={4} />
            <AppImage source={props.uiConfig!!.getProviderLogo()} height={37} width={87} />
          </View>
          {isInFreeTrialWhiteList && !isInView && <Spacer height={60} style={{ backgroundColor: AppColors.background4 }} />}
        </>
      }
      actions={
        <View style={isInFreeTrialWhiteList && !isInView && styles.bottom}>
          {isInFreeTrialWhiteList && !isInView && (
            <View style={{ position: 'relative' }}>
              <FeeFreeTrialBanner initialExpanding={false} style={{ position: 'absolute', left: 0, right: 0, bottom: 0 }} />
            </View>
          )}
          {!isInFreeTrialWhiteList && (
            <RewardBanner
              onLoad={trackOnboardingRewardBannerLoad}
              onPressAdBanner={trackOnboardingRewardBannerClickBanner}
              onPressShowAd={trackOnboardingRewardBannerClickShowAd}
              partner={props?.navigation?.getParam('partnerCode')?.toLocaleLowerCase()}
              bannerKey={RewardBannerKey.Onboarding}
            />
          )}
          <View style={rewardBannerStyle.actionBtn}>
            <AppButton
              disabled={disableButton}
              buttonStyle={styles.actionButton}
              onPress={() => {
                trackOnboardingCta('default');
                props.actions.submitOnboarding();
              }}
              title={isInFreeTrialWhiteList ? 'Mở tài khoản miễn phí ngay' : 'Kích hoạt tài khoản - miễn phí sử dụng'}
            />
          </View>
        </View>
      }
    />
  );
}

const styles = StyleSheet.create({
  featureSlider: { height: 340 },
  landingImageWrapper: {
    paddingHorizontal: 16,
  },
  root: {
    backgroundColor: Colors.background,
  },
  content: {
    paddingHorizontal: 0,
  },
  welcome: {
    marginTop: 52,
    marginHorizontal: 16,
    marginBottom: 16,
  },
  welcomeSecondaryText: {
    width: 275,
  },
  close: {
    position: 'absolute',
    top: 0,
    right: 0,
  },
  section: {
    padding: 16,
    backgroundColor: AppColors.background4,
  },
  disclaimer: {
    paddingHorizontal: 16,
    marginBottom: 16,
    backgroundColor: AppColors.background,
  },
  coverImage: { position: 'absolute', top: 0, left: 0, right: 0, width: 375, height: 132 },
  detailLink: {
    flex: 1,
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    paddingTop: 16,
  },
  actionButton: {
    marginBottom: getBottomSafe() + 16,
  },
  bottom: {
    position: 'relative',
    backgroundColor: AppColors.white,
  }
});

export default LotteOnboardingScreen;

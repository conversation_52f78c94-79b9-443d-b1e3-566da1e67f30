import { Divider } from 'bnpl-shared/src/components/Divider';
import { Spacer } from 'bnpl-shared/src/components/Spacer';
import { AppColors } from 'bnpl-shared/src/constants';
import { images } from 'bnpl-shared/src/res';
import { AppImage, AppText } from 'bnpl-shared/src/shared/react-native-customized';
import { calculateScaleDimension, StyleSheet } from 'bnpl-shared/src/shared/styles/StyleSheet';
import React from 'react';
import { View, Animated } from 'react-native';

const EXPANDED_HEIGHT = calculateScaleDimension(148);

interface OnboardingFeeFreeTrialContentProps {
  expanding: boolean;
  animatedValue: Animated.Value;
}

export const OnboardingFeeFreeTrialContent: React.FC<OnboardingFeeFreeTrialContentProps> = ({ expanding, animatedValue }) => {
  const animatedStyle = {
    height: animatedValue.interpolate({
      inputRange: [0, 1],
      outputRange: [0, EXPANDED_HEIGHT],
    }),
    opacity: animatedValue.interpolate({
      inputRange: [0, 1],
      outputRange: [0, 1],
    }),
    marginTop: animatedValue.interpolate({
      inputRange: [0, 1],
      outputRange: [0, calculateScaleDimension(8)],
    }),
  };

  return (
    <View style={styles.container}>
      <Animated.View style={[styles.contentContainer, animatedStyle]}>
        <Item title="Không lãi suất khi thanh toán đúng hạn" icon={images.IconFreeTrialNoInterest} />
        <Divider variant="dotted" color={AppColors.blue[200]} thick={1} />
        <Item title="Không tính phí khi chưa sử dụng" icon={images.IconFreeTrialFree} />
        <Divider variant="dotted" color={AppColors.blue[200]} thick={1} />
        <Item title="Nhắc bạn 2 ngày trước khi hết miễn phí" icon={images.IconFreeTrialNoti} />
      </Animated.View>
    </View>
  );
};

const Item: React.FC<ItemProps> = ({ title, icon }) => {
  return (
    <View style={styles.item}>
      <AppImage source={icon} width={24} height={24} />
      <Spacer width={12} />
      <AppText size={14} height={18} color={AppColors.dark[500]} numberOfLines={1}>
        {title}
      </AppText>
    </View>
  );
};

interface ItemProps {
  title: string;
  icon: string;
}

const styles = StyleSheet.create({
  container: {
    flexDirection: 'column',
    alignItems: 'center',
    width: '100%',
  },
  title: {
    fontSize: 16,
    fontWeight: '600',
    color: '#333',
    flex: 1,
  },
  icon: {
    fontSize: 12,
    color: '#666',
  },
  contentContainer: {
    width: '100%',
    alignItems: 'flex-start',
    backgroundColor: 'white',
    borderRadius: 12,
    borderWidth: 1,
    borderColor: AppColors.blue[50],
    overflow: 'hidden',
  },
  item: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'flex-start',
    height: 48,
    paddingHorizontal: 16,
  },
  textContainer: {
    flex: 1,
    flexShrink: 1,
  },
  dividerContainer: {
    width: '100%',
    overflow: 'hidden',
    paddingHorizontal: 16,
  },
  itemIcon: {
    width: 24,
    height: 24,
  },
  dividerStyle: {
    width: '100%',
    maxWidth: '100%',
    overflow: 'hidden',
  },
});

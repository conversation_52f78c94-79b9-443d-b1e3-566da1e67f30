import React, { useRef, useState, useEffect, lazy } from 'react';
import { Animated, StyleProp, TouchableOpacity, View, ViewStyle } from 'react-native';
import { calculateScaleDimension, StyleSheet } from 'bnpl-shared/src/shared/styles/StyleSheet';
import { AppImage, AppText } from 'bnpl-shared/src/shared/react-native-customized';
import { AppColors } from 'bnpl-shared/src/constants';
import { Spacer } from 'bnpl-shared/src/components/Spacer';
import LinearGradient from 'react-native-linear-gradient';
import { OnboardingFeeFreeTrialContent } from './CollapsibleContent';
import { ChevronIcon } from 'bnpl-shared/src/shared/ChevronIcon';
import { avoidCacheImageUrl } from 'bnpl-shared/src/shared/utils';
import { TimeUnit } from 'bnpl-shared/src/types';
import { images } from 'bnpl-shared/src/res';

const Rive = lazy(() => import('@rive-app/react-canvas'));
const ANIMATION_URL = avoidCacheImageUrl('https://simg.zalopay.com.vn/fs/bnpl/animation/fee_free_trial.riv', { cacheIn: TimeUnit.DAYS });
const ANIMATION_DURATION = 300;
const FADE_IN_DURATION = 300;

interface FeeFreeTrialProps {
  expandable?: boolean;
  initialExpanding?: boolean;
  noIconAnimation?: boolean;
  autoplay?: boolean;
  borderTopRadius?: boolean;
  style?: StyleProp<ViewStyle>;
}

export default function FeeFreeTrialBanner({ initialExpanding, expandable = true, noIconAnimation = false, borderTopRadius = true, style }: FeeFreeTrialProps) {
  const animatedValue = useRef(new Animated.Value(initialExpanding ? 1 : 0)).current;
  const fadeInValue = useRef(new Animated.Value(0)).current;
  const [expanding, setExpanding] = useState(initialExpanding ?? false);

  function toggleExpanding() {
    if (!expandable) return;
    setExpanding(prev => !prev);
  }

  useEffect(() => {
    Animated.timing(animatedValue, {
      toValue: expanding ? 1 : 0,
      duration: ANIMATION_DURATION,
      useNativeDriver: false,
    }).start();
  }, [expanding, animatedValue]);

  useEffect(() => {
    Animated.timing(fadeInValue, {
      toValue: 1,
      duration: FADE_IN_DURATION,
      useNativeDriver: true,
    }).start();
  }, []);

  const rotateIcon = animatedValue.interpolate({
    inputRange: [0, 1],
    outputRange: ['0deg', '180deg'],
  });

  return (
    <TouchableOpacity
      onPress={expandable ? toggleExpanding : undefined}
      activeOpacity={expandable ? 0.7 : 1}
      disabled={!expandable}
    >
      <Animated.View style={[styles.container, borderTopRadius && styles.borderTopRadius, { opacity: fadeInValue }, style]}>
        <LinearGradient colors={['#E4FBFF', '#FFFFFF']} style={styles.linearBg} />
        <View style={styles.content}>
          <View style={styles.mainContent}>
            <View style={styles.animationContainer}>
              {noIconAnimation ? (
                <AppImage source={images.ImageFeeFreeTrial} width={45} height={45} />
              ) : (
              <Rive
                src={ANIMATION_URL}
                width={calculateScaleDimension(45)}
                height={calculateScaleDimension(45)}
              />
              )}
            </View>
            <Spacer width={8} />
            <AppText size={14} height={18} color={AppColors.dark[500]} bold>
              Miễn phí dịch vụ 30 ngày
            </AppText>
            {expandable && (
              <Animated.View style={[styles.arrowIcon, { transform: [{ rotate: rotateIcon }] }]}>
                <ChevronIcon size={16} direction={'up'} onPress={toggleExpanding} />
              </Animated.View>
            )}
          </View>
          <OnboardingFeeFreeTrialContent expanding={expanding} animatedValue={animatedValue} />
        </View>
      </Animated.View>
    </TouchableOpacity>
  );
};

const styles = StyleSheet.create({
  container: {
    flexDirection: 'column',
    alignItems: 'center',
    paddingHorizontal: 16,
    overflow: 'hidden',
    position: 'relative',
    backgroundColor: AppColors.white,
  },
  borderTopRadius: {
    borderTopLeftRadius: 16,
    borderTopRightRadius: 16,
  },
  animationContainer: {
    width: 45,
    height: 45,
    alignItems: 'center',
    justifyContent: 'center',
  },
  linearBg: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    zIndex: -1,
  },
  content: {
    width: '100%',
    position: 'relative',
  },
  mainContent: {
    flexDirection: 'row',
    alignItems: 'center',
    height: 50,
  },
  arrowIcon: {
    position: 'absolute',
    right: 0,
    top: 17,
  },
});

import React, { lazy, forwardRef, useState, useRef, useEffect } from 'react';
import { Animated, TouchableOpacity, View } from 'react-native';
import { calculateScaleDimension, StyleSheet } from 'bnpl-shared/src/shared/styles/StyleSheet';
import { AppImage, AppText } from 'bnpl-shared/src/shared/react-native-customized';
import { AppColors } from 'bnpl-shared/src/constants';
import { Spacer } from 'bnpl-shared/src/components/Spacer';
import LinearGradient from 'react-native-linear-gradient';
import { OnboardingFeeFreeTrialContent } from './CollapsibleContent';
import { avoidCacheImageUrl } from 'bnpl-shared/src/shared/utils';
import { TimeUnit } from 'bnpl-shared/src/types';
import { GeneralArrowNext4Secondary } from '@zpi/looknfeel-icons';
import TagBg from './TagBg';

const Rive = lazy(() => import('@rive-app/react-canvas'));

const ANIMATION_URL = avoidCacheImageUrl('https://simg.zalopay.com.vn/fs/bnpl/animation/fee_free_trial.riv', { cacheIn: TimeUnit.DAYS });
const ANIMATION_DURATION = 300;

interface FeeFreeTrialProps {
  initialExpanding?: boolean;
}

function FeeFreeTrialSection({ initialExpanding }: FeeFreeTrialProps, ref: React.Ref<View>) {
  const animatedValue = useRef(new Animated.Value(initialExpanding ? 1 : 0)).current;
  const [expanding, setExpanding] = useState(initialExpanding ?? false);
  function toggleExpanding() {
    setExpanding(prev => !prev);
  }
  useEffect(() => {
    Animated.timing(animatedValue, {
      toValue: expanding ? 1 : 0,
      duration: ANIMATION_DURATION,
      useNativeDriver: false,
    }).start();
  }, [expanding, animatedValue]);

  const rotateIcon = animatedValue.interpolate({
    inputRange: [0, 1],
    outputRange: ['90deg', '-90deg'],
  });
  return (
    <View ref={ref} style={styles.container}>
      <LinearGradient colors={['#FFFFFF', '#E4FBFF']} style={styles.linearBg} />
      <View style={styles.tag}>
        <View style={styles.tagContainer}>
          <View style={styles.linearBg}>
            <TagBg width={calculateScaleDimension(120)} height={calculateScaleDimension(24)} />
          </View>
          <AppText size={12} height={16} color={AppColors.teal[600]}>Dành cho bạn</AppText>
        </View>
      </View>
      <View style={styles.content}>
        <View style={styles.animationContainer}>
          <Rive src={ANIMATION_URL} width={calculateScaleDimension(64)} height={calculateScaleDimension(64)} />
        </View>
        <Spacer height={8} />
        <AppText size={16} height={20} color={AppColors.dark[500]} bold>
          Miễn phí dịch vụ 30 ngày
        </AppText>
        <Spacer height={4} />
        <AppText size={12} height={16} color={AppColors.dark[300]}>
          <AppText size={12} height={16} color={AppColors.dark[300]} style={{ textDecorationLine: 'line-through' }}>
            20,000đ
          </AppText>
          <Spacer width={2} />
          <AppText size={12} height={16} color={AppColors.green[4]} bold>
            0đ/tháng
          </AppText>
          <Spacer width={2} />
          <AppText size={12} height={16} color={AppColors.dark[300]}>
            {'cho mọi giao dịch'}
          </AppText>
        </AppText>
        <Spacer height={8} />
        <OnboardingFeeFreeTrialContent expanding={expanding} animatedValue={animatedValue} />
        <TouchableOpacity style={styles.bottom} onPress={toggleExpanding} activeOpacity={0.7}>
          <AppText size={14} height={18} color={AppColors.blue[500]}>
            {expanding ? 'Thu gọn' : 'Xem chi tiết'}
          </AppText>
          <Animated.View style={{ transform: [{ rotate: rotateIcon }] }}>
            <GeneralArrowNext4Secondary width={calculateScaleDimension(16)} height={calculateScaleDimension(16)} color={AppColors.blue[500]} />
          </Animated.View>
        </TouchableOpacity>
      </View>
    </View>
  );
};

export default forwardRef<View, FeeFreeTrialProps>(FeeFreeTrialSection)

const styles = StyleSheet.create({
  container: {
    flexDirection: 'column',
    alignItems: 'center',
    borderRadius: 16,
    paddingTop: 40,
    paddingHorizontal: 16,
    paddingBottom: 16,
    borderWidth: 1,
    borderColor: AppColors.blue[50],
    overflow: 'hidden',
    position: 'relative',
    marginHorizontal: 16,
  },
  tag: {
    position: 'absolute',
    top: -2,
    display: 'flex',
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
  },
  tagContainer: {
    position: 'relative',
    height: 24,
    width: 120,
    alignItems: 'center',
    justifyContent: 'center',
  },
  bottom: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    borderRadius: 8,
    marginTop: 16,
  },
  animationContainer: {
    width: 64,
    height: 64,
    alignItems: 'center',
    justifyContent: 'center',
  },
  linearBg: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    zIndex: -1,
  },
  content: {
    position: 'relative',
    flexDirection: 'column',
    alignItems: 'center',
    width: '100%',
  },
});

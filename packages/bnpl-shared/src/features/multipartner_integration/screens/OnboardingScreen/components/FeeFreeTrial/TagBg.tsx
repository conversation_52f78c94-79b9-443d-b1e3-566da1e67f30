import React from 'react';

export default function TagBg({ width = 120, height = 24 }: TagBgProps) {
  return (
    <svg width={width} height={height} viewBox="0 0 121 25" fill="none" xmlns="http://www.w3.org/2000/svg">
      <g filter="url(#filter0_i_16546_525031)">
        <path d="M0.5 0.5H120.5L113.883 16.9729C112.056 21.5202 107.648 24.5 102.748 24.5H20.8801C16.5195 24.5 12.5021 22.1345 10.3867 18.3214L0.5 0.5Z" fill="url(#paint0_linear_16546_525031)" />
      </g>
      <path d="M119.761 1L113.419 16.7861C111.668 21.1439 107.444 23.9999 102.748 24H20.8799C16.8316 23.9999 13.0923 21.8722 11.0195 18.417L10.8242 18.0791L1.34961 1H119.761Z" stroke="#D3EEFF" />
      <defs>
        <filter id="filter0_i_16546_525031" x="0.5" y="0.5" width="120" height="28" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
          <feFlood flood-opacity="0" result="BackgroundImageFix" />
          <feBlend mode="normal" in="SourceGraphic" in2="BackgroundImageFix" result="shape" />
          <feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha" />
          <feOffset dy="5" />
          <feGaussianBlur stdDeviation="2" />
          <feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1" />
          <feColorMatrix type="matrix" values="0 0 0 0 1 0 0 0 0 1 0 0 0 0 1 0 0 0 0.4 0" />
          <feBlend mode="normal" in2="shape" result="effect1_innerShadow_16546_525031" />
        </filter>
        <linearGradient id="paint0_linear_16546_525031" x1="34.9418" y1="-26.3267" x2="37.8942" y2="36.4401" gradientUnits="userSpaceOnUse">
          <stop stop-color="#E6FBFF" />
          <stop offset="1" stop-color="#D3F8FF" />
        </linearGradient>
      </defs>
    </svg>
  );
}

interface TagBgProps {
  width?: number;
  height?: number;
}

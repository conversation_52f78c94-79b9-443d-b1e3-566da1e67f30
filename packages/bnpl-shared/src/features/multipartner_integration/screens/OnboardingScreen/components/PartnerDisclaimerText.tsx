import React, { FC, useEffect, useState } from 'react';
import { Spacer } from 'bnpl-shared/src/components/Spacer';
import { AppText } from 'bnpl-shared/src/shared/react-native-customized';
import { LinkButton } from 'bnpl-shared/src/shared/LinkButton';
import { LayoutChangeEvent, LayoutRectangle } from 'react-native';
import { PartnerCode } from 'bnpl-shared/src/constants';
import { OnboardingScreenUIConfig } from 'bnpl-shared/src/features/multipartner_integration/screens/OnboardingScreen/models';
import { trackOnboardingClickTnc } from 'bnpl-shared/src/screens/onboarding-flow/OnboardingScreen/tracking';
import { StyleUtils } from 'bnpl-shared/src/shared/styles/StyleUtils';
import { CheckBox } from 'bnpl-shared/src/shared/CheckBox/CheckBox';
import { StyleSheet } from 'bnpl-shared/src/shared/styles/StyleSheet';
import StickyTooltip from 'bnpl-shared/src/components/StickyTooltip';
import { launchInAppWebView } from 'bnpl-shared/src/shared/ZaloPayModules';

type Props = {
  partner: PartnerCode;
  data: OnboardingScreenUIConfig;
  setDisableActionButton?: (disable: boolean) => void;
};

export const PartnerDisclaimerText: FC<Props> = ({ partner, data, setDisableActionButton }) => {
  const handleOpenUrl = (url: string, _title: string) => {
    launchInAppWebView(url);
  };

  switch (partner) {
    case PartnerCode.LOTTE:
      return (
        <LotteDisclaimerText setDisableActionButton={setDisableActionButton} data={data} urlHandler={handleOpenUrl} />
      );
    case PartnerCode.CIMB:
      return <CIMBDisclaimerText data={data} urlHandler={handleOpenUrl} />;
    case PartnerCode.MINI_BNPL:
      return <MiniBNPLDisclaimerText data={data} urlHandler={handleOpenUrl} />;
  }
};

const CIMBDisclaimerText: FC<{
  data: OnboardingScreenUIConfig;
  urlHandler: (url: string, title: string) => void;
}> = () => {
  return (
    <>
      <Spacer height={24} />
      <AppText center>
        Bằng cách nhấn “Đăng ký ngay” , tôi đồng ý với các{' '}
        <LinkButton onPress={() => {}}>Chính sách bảo vệ quyền riêng tư của Zalopay</LinkButton> và chia sẻ thông tin
        với CIMB.
      </AppText>
      <Spacer height={12} />
    </>
  );
};

const LotteDisclaimerText: FC<{
  setDisableActionButton?: (disable: boolean) => void;
  data: OnboardingScreenUIConfig;
  urlHandler: (url: string, title: string) => void;
}> = ({ urlHandler, data, setDisableActionButton }) => {
  const [agreeToShare, setAgreeToShare] = React.useState(true);
  const [agreeToSaveContract, setAgreeToSaveContract] = React.useState(true);
  const [personalInfoTooltipVisible, setPersonalInfoTooltipVisible] = useState(false);
  const [dataTooltipVisible, setDataInfoTooltipVisible] = useState(false); // 0 || > 1 : not show, 1: show,
  const [tooltipAnchor, setTooltipAnchor] = useState<LayoutRectangle>();

  const onLayout = (event: LayoutChangeEvent) => {
    setTooltipAnchor(event.nativeEvent.layout);
  };

  const handlePersonalInfoTooltipToggle = () => {
    if (personalInfoTooltipVisible) {
      // If personal info tooltip is already visible, close it
      setPersonalInfoTooltipVisible(false);
    } else {
      // Show personal info tooltip and hide data tooltip
      setPersonalInfoTooltipVisible(true);
      setDataInfoTooltipVisible(false);
    }
  };

  const handleDataTooltipToggle = () => {
    if (dataTooltipVisible) {
      // If data tooltip is already visible, close it
      setDataInfoTooltipVisible(false);
    } else {
      // Show data tooltip and hide personal info tooltip
      setDataInfoTooltipVisible(true);
      setPersonalInfoTooltipVisible(false);
    }
  };
  useEffect(() => {
    if (!agreeToShare || !agreeToSaveContract) {
      setDisableActionButton?.(true);
    } else {
      setDisableActionButton?.(false);
    }
  }, [agreeToShare, agreeToSaveContract]);

  return (
    <>
      <Spacer height={24} />
      <AppText>
        Tôi xác nhận và đồng ý{' '}
        <LinkButton
          onPress={() => {
            trackOnboardingClickTnc('zlp_privacy');
            urlHandler(data.getPrivacyUrl(), 'Chính sách của Zalopay');
          }}>
          chính sách bảo vệ quyền riêng tư của Zalopay
        </LinkButton>
        ,{' '}
        <LinkButton
          onPress={() => {
            trackOnboardingClickTnc('tnc');
            urlHandler(data.getPartnerUrl(), 'Điều khoản & điều kiện');
          }}>
          {' '}
          điều kiện điều khoản của{' '}
        </LinkButton>
        {data.getPartnerName()}, và:
      </AppText>
      <CheckBox
        testID="agree-to-share"
        value={agreeToShare}
        onChangeValue={value => {
          setAgreeToShare(value);
        }}
        style={styles.checkbox}
        title={
          <AppText style={StyleUtils.flexOne} onLayout={onLayout}>
            Cho phép Zalopay chia sẻ{' '}
            <LinkButton onPress={handlePersonalInfoTooltipToggle}>thông tin của tôi</LinkButton> cho{' '}
            {data.getPartnerName()} và cho phép {data.getPartnerName()} chia sẻ các{' '}
            <LinkButton onPress={handleDataTooltipToggle}>thông tin, dữ liệu của tôi</LinkButton> cho Zalopay.
          </AppText>
        }
      />
      <CheckBox
        testID="agree-to-save"
        value={agreeToSaveContract}
        onChangeValue={value => {
          setAgreeToSaveContract(value);
        }}
        style={styles.checkbox}
        title={
          <AppText style={StyleUtils.flexOne}>
            Lưu, hiển thị, sử dụng các thông tin thực hiện Hợp đồng đã ký (nếu được duyệt) trên Zalopay cho mục đích tại
            Chính sách bảo vệ quyền riêng tư.
          </AppText>
        }
      />
      <Spacer height={12} />
      <StickyTooltip
        visible={personalInfoTooltipVisible}
        anchorView={{ ...tooltipAnchor, x: 186, height: 78 } as LayoutRectangle}
        onTooltipPress={handlePersonalInfoTooltipToggle}
        content={'Họ tên, số CCCD, ngày\n sinh, số điện thoại'}
      />
      <StickyTooltip
        visible={dataTooltipVisible}
        anchorView={{ ...tooltipAnchor, x: 60, height: 110 } as LayoutRectangle}
        onTooltipPress={handleDataTooltipToggle}
        content={'Sao kê, hợp đồng,\nthông tin khách hàng'}
      />
    </>
  );
};

const MiniBNPLDisclaimerText: FC<{
  data: OnboardingScreenUIConfig;
  urlHandler: (url: string, title: string) => void;
}> = () => {
  return (
    <>
      <Spacer height={24} />
      <AppText center>
        Bằng cách nhấn “Đăng ký ngay” , tôi đồng ý với các{' '}
        <LinkButton onPress={() => {}}>Chính sách bảo vệ quyền riêng tư của Zalopay</LinkButton>
      </AppText>
      <Spacer height={12} />
    </>
  );
};

const styles = StyleSheet.create({
  checkbox: {
    marginTop: 12,
    alignItems: 'flex-start',
  },
});

import React, { FC, useEffect, useMemo, useState } from 'react';
import { AppButton, AppImage, AppText } from 'bnpl-shared/src/shared/react-native-customized';
import { images } from 'bnpl-shared/src/res';
import { Platform, View } from 'react-native';
import { CloseButton } from 'bnpl-shared/src/shared/CloseButton';
import { Colors, StyleUtils } from 'bnpl-shared/src/shared/styles/StyleUtils';
import { AutoHeightWebView } from 'bnpl-shared/src/components/AutoHeightWebView';
import { avoidCacheImageUrl } from 'bnpl-shared/src/shared/utils';
import ScaledImage from 'bnpl-shared/src/shared/ScaledImage/ScaledImage';
import { LinkButton } from 'bnpl-shared/src/shared/LinkButton';
import { ChevronIcon } from 'bnpl-shared/src/shared/ChevronIcon';
import { Spacer } from 'bnpl-shared/src/components/Spacer';
import { StyleSheet } from 'bnpl-shared/src/shared/styles/StyleSheet';
import { AppColors, PartnerCode } from 'bnpl-shared/src/constants';
import { PartnerDisclaimerText } from './components/PartnerDisclaimerText';
import withViewModel from 'bnpl-shared/src/hooks/withViewModel';
import { useOnboardingViewModel } from './useOnboardingViewModel';
import { FullScreenFormLayout } from 'bnpl-shared/src/components/layouts/FullScreenFormLayout';
import { getBottomSafe } from 'bnpl-shared/src/utils';
import {
  trackOnboardingCloseButton,
  trackOnboardingCta,
  trackOnboardingLoad,
  trackOnboardingRewardBannerClickBanner,
  trackOnboardingRewardBannerClickShowAd,
  trackOnboardingRewardBannerLoad,
  trackOnboardingScroll,
} from 'bnpl-shared/src/screens/onboarding-flow/OnboardingScreen/tracking';
import { throttle } from 'lodash';
import { LinkType, openCommonLink } from 'bnpl-shared/src/utils/openCommonLink';
import { RewardBannerKey, TimeUnit } from 'bnpl-shared/src/types';
import RewardBanner, { styles as rewardBannerStyle } from 'bnpl-shared/src/features/reward_banner';
import AssetFrameMock from 'bnpl-shared/src/components/AssetFrame/AssetFrameMock';
import MiniBNPLOnboardingScreen from 'bnpl-shared/src/features/mini_bnpl/screens/OnboardingScreen';
import { usePartnerData } from '../../helpers';
import LotteOnboardingScreen from './LotteOnboardingScreen';

const OnboardingScreenView: FC<ReturnType<typeof useOnboardingViewModel>> = props => {
  const { getChosenPartner, fetchPartnerData } = usePartnerData();
  const chosenPartner = getChosenPartner();
  useEffect(() => {
    fetchPartnerData();
  }, []);

  if (chosenPartner?.partner_code === PartnerCode.MINI_BNPL) {
    return <MiniBNPLOnboardingScreen {...props} />;
  }

  if (chosenPartner?.partner_code === PartnerCode.LOTTE) {
    return <LotteOnboardingScreen {...props} />
  }

  return (
    <DefaultOnboardingScreen
      {...props}
    />
  )
};

const DefaultOnboardingScreen: FC<ReturnType<typeof useOnboardingViewModel>> = props => {
  const [disableButton, setDisableButton] = useState(false);

  const createImageInjectScript = (imageUrl: string) => {
    return `
      document.getElementById("image").src = "${imageUrl}";
      sendMessageToRN(document.getElementById("image").height);
      true;
     `;
  };

  const handleScroll = useMemo(() => {
    return throttle(
      (height: number, offset: number) => {
        trackOnboardingScroll({ percents: (offset / height) * 100 });
      },
      300,
      { leading: true },
    );
  }, []);

  useEffect(() => {
    trackOnboardingLoad('default');
  }, []);

  return (
    <FullScreenFormLayout
      style={styles.root}
      contentStyles={styles.content}
      actionsStyles={rewardBannerStyle.action}
      onScroll={e => {
        // contentOffset: distance between the current viewport to the beginning of the scrollable content
        // contentSize: size of the scrollable content
        // layoutMeasurement: size of the current viewport.
        const height = e.nativeEvent.contentSize.height - e.nativeEvent.layoutMeasurement.height;
        const offset = e.nativeEvent.contentOffset.y;
        handleScroll(height, offset);
      }}
      content={
        <>
          <AppImage
            resizeMode={'cover'}
            style={styles.coverImage}
            source={images.ImageOnboardingBackground}
            height={220}
            width={375}
          />
          <View style={styles.welcome}>
            <CloseButton
              onPress={() => {
                trackOnboardingCloseButton('default');
                props.actions.closeApp();
              }}
              style={styles.close}
            />
            <AppText color={Colors.text2} height={18}>
              Chào mừng {props.displayName}
            </AppText>
          </View>
          {/*Workaround: because scaling image on Android is decrease image quality, */}
          {/*so we use webview to load a html page then inject scripts to load image :(*/}
          {Platform.OS === 'android' ? (
            <AutoHeightWebView
              injectJsScripts={createImageInjectScript(avoidCacheImageUrl(props.uiConfig!!.getLandingImageUrl()))}
              url="https://simg.zalopay.com.vn/fs/bnpl/document/landing_page.html"
            />
          ) : (
            <ScaledImage
              uri={avoidCacheImageUrl(props.uiConfig!!.getLandingImageUrl(), { cacheIn: TimeUnit.DAYS })}
              width={375}
            />
          )}
          <View style={styles.detailLink}>
            <LinkButton
              onPress={() => {
                openCommonLink(LinkType.LANDING_PAGE, props.navigation);
              }}
              bold>
              Chi tiết sản phẩm
            </LinkButton>
            <ChevronIcon direction={'right'} />
          </View>

          <Spacer height={12} />
          <AssetFrameMock assetInventoryId={props?.assetFrameInventoryId} />

          <View style={styles.disclaimer}>
            {props.uiConfig && (
              <PartnerDisclaimerText
                setDisableActionButton={setDisableButton}
                partner={props.uiConfig!!.getPartnerCode()}
                data={props.uiConfig}
              />
            )}
          </View>
          <View style={[StyleUtils.flexRow, StyleUtils.centered, styles.section]}>
            <AppText size={12} height={16}>
              Cung cấp bởi
            </AppText>
            <Spacer width={4} />
            <AppImage source={props.uiConfig!!.getProviderLogo()} height={37} width={87} />
          </View>
        </>
      }
      actions={
        <>
          <RewardBanner
            onLoad={trackOnboardingRewardBannerLoad}
            onPressAdBanner={trackOnboardingRewardBannerClickBanner}
            onPressShowAd={trackOnboardingRewardBannerClickShowAd}
            partner={props?.navigation?.getParam('partnerCode')?.toLocaleLowerCase()}
            bannerKey={RewardBannerKey.Onboarding}
          />

          <View style={rewardBannerStyle.actionBtn}>
            <AppButton
              disabled={disableButton}
              buttonStyle={styles.actionButton}
              onPress={() => {
                trackOnboardingCta('default');
                props.actions.submitOnboarding();
              }}
              title={'Kích hoạt tài khoản - miễn phí sử dụng'}
            />
          </View>
        </>
      }
    />
  );
};

const styles = StyleSheet.create({
  featureSlider: { height: 340 },
  root: {
    backgroundColor: Colors.background,
  },
  content: {
    paddingHorizontal: 0,
  },
  welcome: {
    marginTop: 52,
    marginHorizontal: 16,
    marginBottom: 16,
  },
  welcomeSecondaryText: {
    width: 275,
  },
  close: {
    position: 'absolute',
    top: 0,
    right: 0,
  },
  section: {
    padding: 16,
    backgroundColor: AppColors.background4,
  },
  disclaimer: {
    paddingHorizontal: 16,
    marginBottom: 16,
    backgroundColor: AppColors.background,
  },
  coverImage: { position: 'absolute', top: 0, left: 0, right: 0, width: 375, height: 132 },
  detailLink: {
    flex: 1,
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    paddingTop: 16,
  },
  actionButton: {
    marginBottom: getBottomSafe() + 16,
  },
});

export default withViewModel(useOnboardingViewModel, OnboardingScreenView);

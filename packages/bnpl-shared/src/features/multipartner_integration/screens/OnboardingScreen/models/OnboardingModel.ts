import { postInitOnboardingProfile<PERSON>pi, postVerifyOnboardingProfileApi } from 'bnpl-shared/src/api/partner_lotte';
import { postSubmitOnboardingApi } from 'bnpl-shared/src/api/mini-bnpl';
import { MiniBNPLOnboardingStatusResp } from 'bnpl-shared/src/types';
export interface OnboardingModel {
  submitOnboarding: () => Promise<{ request_id: string } | MiniBNPLOnboardingStatusResp>;
  verifyOnboarding: (requestId: string) => Promise<any>;
}

export class LotteOnboardingModel implements OnboardingModel {
  constructor() {}

  async submitOnboarding() {
    return postInitOnboardingProfileApi();
  }

  async verifyOnboarding(requestId: string): Promise<any> {
    return await postVerifyOnboardingProfileApi(requestId);
  }
}

export class MiniBNPLOnboardingModel implements OnboardingModel {
  constructor() {}

  async submitOnboarding() {
    return postSubmitOnboardingApi();
  }

  async verifyOnboarding(_requestId: string): Promise<any> {
    return null;
  }
}

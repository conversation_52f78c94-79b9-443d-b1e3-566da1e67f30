import { PartnerCode } from 'bnpl-shared/src/constants';
import { images } from 'bnpl-shared/src/res';
import { LotteTerms, ZalopayPrivacy } from 'bnpl-shared/src/constants/PublicUrls';

export abstract class OnboardingScreenUIConfig {
  protected partnerCode: PartnerCode;
  protected partnerName: string;
  constructor(partnerCode: PartnerCode, partnerName: string) {
    this.partnerCode = partnerCode;
    this.partnerName = partnerName;
  }

  abstract getProviderLogo: () => number;
  abstract getLandingImageUrl: () => string;
  abstract getPrivacyUrl: () => string;
  abstract getPartnerUrl: () => string;
  getPartnerCode = () => this.partnerCode;
  getPartnerName = () => this.partnerName;
}

export class LotteUIConfig extends OnboardingScreenUIConfig {
  getProviderLogo = () => {
    return images.IconLotte;
  };

  getLandingImageUrl = () => {
    return 'https://simg.zalopay.com.vn/fs/bnpl/image/ab_landing_lotte.png';
  };

  getPrivacyUrl = () => {
    return ZalopayPrivacy;
  };

  getPartnerUrl = () => {
    return LotteTerms;
  };

  getPartnerName = () => this.partnerName || 'LOTTE Finance';
}

export class MiniBNPLUIConfig extends OnboardingScreenUIConfig {
  getProviderLogo = () => {
    return images.IconZlp;
  };

  getLandingImageUrl = () => {
    return 'https://simg.zalopay.com.vn/fs/bnpl/image/ab_landing_lotte.png';
  };

  getPrivacyUrl = () => {
    return ZalopayPrivacy;
  };

  getPartnerUrl = () => {
    return ZalopayPrivacy;
  };

  getPartnerName = () => this.partnerName || 'Zalopay';
}

import { <PERSON><PERSON><PERSON>, ScreenKey } from 'bnpl-shared/src/constants';
import { useNavigation } from 'bnpl-shared/src/shared/navigation';
import {
  LotteOnboardingModel,
  LotteUIConfig,
  MiniBNPLOnboardingModel,
  MiniBNPLUIConfig,
  OnboardingModel,
  OnboardingScreenUIConfig,
} from './models';
import { useUserInfo } from 'bnpl-shared/src/shared/ZaloPayModules/useUserInfo';
import { hideLoading, showLoading } from 'bnpl-shared/src/shared/ZaloPayModules';
import {
  AdsInventoryId,
  ConfigurableScreenProps,
  MiniBNPLOnboardingStatusResp,
  PredefinedCTA,
  RewardVoucherConfigs,
} from 'bnpl-shared/src/types';
import { invariant } from 'bnpl-shared/src/shared/utils/invariant';
import { useErrorHandler } from 'bnpl-shared/src/features/multipartner_integration/helpers/useErrorHandler';
import { useEffect } from 'react';
import { subscribe, unsubscribe } from 'bnpl-shared/src/hooks/useOnSwipeToBackIOS';
import {
  getMiniBNPLScreenKeyFromOnboardingNextStep,
  usePartnerData,
} from 'bnpl-shared/src/features/multipartner_integration/helpers';
import { useRemoteConfigs } from 'bnpl-shared/src/remote_configs/useRemoteConfigs';
import { closeMiniApp } from 'bnpl-shared/src/utils/closeMiniApp';

type NavigationParams = { partnerCode: PartnerCode; requestId?: string; partnerName: string };

type OnboardingScreenProps = {
  displayName: string;
  actions: {
    submitOnboarding: () => void;
    closeApp: () => void;
  };
  assetFrameInventoryId?: string;
} & ConfigurableScreenProps<NavigationParams, OnboardingScreenUIConfig>;

const getOnboardingModel = (partner: PartnerCode): OnboardingModel => {
  switch (partner) {
    default:
    case PartnerCode.LOTTE:
      return new LotteOnboardingModel();
    case PartnerCode.MINI_BNPL:
      return new MiniBNPLOnboardingModel();
  }
};

const getUIConfig = (partnerCode: PartnerCode, partnerName: string) => {
  switch (partnerCode) {
    default:
    case PartnerCode.LOTTE:
      return new LotteUIConfig(partnerCode, partnerName);
    case PartnerCode.MINI_BNPL:
      return new MiniBNPLUIConfig(partnerCode, partnerName);
  }
};

export const useOnboardingViewModel = (props: any): OnboardingScreenProps => {
  const { displayName = 'Bạn' } = useUserInfo();
  const { getPartnerDataByCode } = usePartnerData();
  const navigation = useNavigation<NavigationParams>(props);
  const handleError = useErrorHandler({ trackingLocation: 'landing' });
  const partnerCode = navigation.getParam('partnerCode');
  const paramRequestId = navigation.getParam('requestId');
  invariant(partnerCode, 'partnerCode is required');

  const partnerName = getPartnerDataByCode(partnerCode)?.partner_name || '';
  invariant(partnerName, 'partnerName is required');

  const uiConfig = getUIConfig(partnerCode, partnerName);
  const onboardingModel = getOnboardingModel(partnerCode);

  const { getConfigWithType } = useRemoteConfigs();
  const defaultAssetFrameInventoryId =
    partnerCode === PartnerCode.CIMB ? AdsInventoryId.ASSET_INVENTORY_ID_CIMB : AdsInventoryId.ASSET_INVENTORY_ID_LOTTE;
  const assetFrameInventoryId = partnerCode
    ? getConfigWithType<RewardVoucherConfigs>('asset_frame_config')?.[partnerCode.toLocaleLowerCase()] ||
      defaultAssetFrameInventoryId
    : defaultAssetFrameInventoryId;

  const isValidRequestId = (requestId: string) => {
    try {
      return requestId && parseInt(requestId, 10) !== 0;
    } catch (e) {
      return false;
    }
  };

  const submitOnboarding = async () => {
    try {
      showLoading();
      // start MiniBNPL Onboarding
      if (partnerCode === PartnerCode.MINI_BNPL) {
        const { onboarding_next_step } = (await onboardingModel.submitOnboarding()) as MiniBNPLOnboardingStatusResp;
        const screenKey = getMiniBNPLScreenKeyFromOnboardingNextStep(onboarding_next_step);
        return navigation.replace(screenKey, { partnerCode });
      }
      // end MiniBNPL Onboarding

      let requestIdToUse = paramRequestId;
      if (!requestIdToUse || !isValidRequestId(requestIdToUse)) {
        const { request_id } = (await onboardingModel.submitOnboarding()) as { request_id: string };
        requestIdToUse = request_id;
      }
      await onboardingModel.verifyOnboarding(requestIdToUse);
      navigation.replace(ScreenKey.MPSubmitDetailInfoScreen, { requestId: requestIdToUse, partnerCode });
    } catch (e: any) {
      handleError({
        error: e,
        ctaCallback: {
          [PredefinedCTA.RETRY]: () => submitOnboarding,
          [PredefinedCTA.RESTART]: () => {
            navigation.replace(ScreenKey.MPOnboardingScreen, { partnerCode });
          },
        },
      });
    } finally {
      hideLoading();
    }
  };

  const closeApp = async () => {
    closeMiniApp();
  };

  useEffect(() => {
    const subscriber = {
      fn: () => {
        closeApp();
      },
    };
    subscribe(ScreenKey.MPOnboardingScreen, subscriber);
    return () => {
      unsubscribe(ScreenKey.MPOnboardingScreen);
    };
  }, []);

  return {
    displayName,
    navigation,
    uiConfig,
    actions: {
      closeApp,
      submitOnboarding,
    },
    assetFrameInventoryId,
  };
};

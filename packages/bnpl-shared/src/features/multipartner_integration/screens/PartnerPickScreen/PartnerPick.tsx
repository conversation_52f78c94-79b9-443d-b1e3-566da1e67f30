import { usePartnerPickViewModel } from './usePartnerPickViewModel';
import React, { FC } from 'react';
import { Platform, TouchableOpacity, View } from 'react-native';
import { AppButton, AppImage, AppText } from 'bnpl-shared/src/shared/react-native-customized';
import { images } from 'bnpl-shared/src/res';
import { StyleSheet } from 'bnpl-shared/src/shared/styles/StyleSheet';
import { AppColors, PartnerCode } from 'bnpl-shared/src/constants';
import { Spacer } from 'bnpl-shared/src/components/Spacer';
import { RoutingInfo } from 'bnpl-shared/src/types';
import { windowWidth } from 'bnpl-shared/src/shared/styles/StyleUtils';
import { ImageSource } from 'bnpl-shared/src/shared/types';

const ActiveStatus = () => {
  return (
    <View style={[styles.partnerStatus, styles.activePartnerStatus]}>
      <AppText color={'#00A655'} size={12}>
        Đang hoạt động
      </AppText>
    </View>
  );
};

const InActiveStatus = () => {
  return (
    <View style={[styles.partnerStatus, styles.inactivePartnerStatus]}>
      <AppText color={AppColors.text2} size={12}>
        Chưa đăng ký
      </AppText>
    </View>
  );
};

export const PartnerPick: FC<ReturnType<typeof usePartnerPickViewModel>> = props => {
  const renderPartnerItem = (routingInfo: RoutingInfo) => {
    const actionHandler = () => props.actions.pickPartnerToContinue(routingInfo);

    const partnerImageMap = {
      [PartnerCode.CIMB]: images.IconLogoCIMB,
      [PartnerCode.LOTTE]: images.IconLotteSmall,
      [PartnerCode.MINI_BNPL]: images.IconZlp,
    } as const;

    const getPartnerLogo = (partnerCode: PartnerCode): ImageSource => {
      return partnerImageMap[partnerCode] || '/path/to/default-image.png';
    };

    return (
      <TouchableOpacity onPress={actionHandler} key={routingInfo.partner_code}>
        <View style={styles.partnerCard}>
          <View style={styles.partnerCardHeading}>
            <View style={styles.partnerCardInfo}>
              <AppImage source={getPartnerLogo(routingInfo.partner_code)} width={40} height={40} />
              <Spacer width={12} />
              <View>
                <AppText bold>{routingInfo.partner_name}</AppText>
              </View>
            </View>
          </View>
          <Spacer height={8} />
          <AppText color={AppColors.text2} size={14}>
            Trạng thái hoạt động
          </AppText>
          <Spacer height={8} />
          <View style={styles.partnerCardHeading}>
            {routingInfo.is_approved ? <ActiveStatus /> : <InActiveStatus />}
            <AppButton
              onPress={actionHandler}
              titleStyle={{ fontSize: 12 }}
              buttonStyle={{ height: 24, borderRadius: 24, paddingHorizontal: 8 }}
              boldText={false}
              variant="outlined"
              title={routingInfo.is_approved ? 'Chọn' : 'Đăng ký ngay'}
            />
          </View>
        </View>
      </TouchableOpacity>
    );
  };
  return (
    <View style={styles.root}>
      <AppImage
        style={styles.bgImage}
        width={windowWidth}
        height={700}
        resizeMode="stretch"
        source={images.BackgroundRepayScreen}
      />
      <View style={styles.container}>
        <AppText style={styles.title} color={AppColors.white} bold size={18}>
          Chọn đối tác cung cấp
        </AppText>
        {props.routingInfos.map(item => renderPartnerItem(item))}
      </View>
    </View>
  );
};

const styles = StyleSheet.create({
  root: {
    flex: 1,
    backgroundColor: AppColors.background2,
  },
  container: {
    flex: 1,
    paddingHorizontal: 16,
  },
  header: {
    paddingVertical: 12,
    paddingLeft: 12,
    paddingRight: 18,
    display: 'flex',
    flexDirection: 'row',
    justifyContent: 'space-between',
  },
  title: {
    paddingVertical: 12,
  },
  partnerCard: {
    paddingVertical: 14,
    paddingHorizontal: 16,
    backgroundColor: AppColors.background,
    borderRadius: 16,
    marginBottom: 16,
    height: 134,
    ...Platform.select({
      web: {
        boxShadow: '0px 2px 12px rgba(0, 31, 62, 0.05)',
      },
      ios: {
        shadowColor: '#000',
        shadowOffset: {
          width: 0,
          height: 8,
        },
        shadowOpacity: 0.04,
        shadowRadius: 8,
      },
      android: {
        elevation: 2,
      },
    }),
  },
  partnerCardHeading: {
    display: 'flex',
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  partnerCardInfo: {
    display: 'flex',
    flexDirection: 'row',
    justifyContent: 'flex-start',
    alignItems: 'center',
  },
  partnerStatus: {
    borderRadius: 16,
    width: 110,
    height: 24,
    alignItems: 'center',
    justifyContent: 'center',
  },
  activePartnerStatus: {
    backgroundColor: '#F2FFF8',
  },
  inactivePartnerStatus: {
    backgroundColor: AppColors.divider,
  },
  bgImage: { position: 'absolute', top: 0, left: 0, right: 0, bottom: 0 },
});

import { getTransactionHistory<PERSON><PERSON> } from 'bnpl-shared/src/api/getTransactionHistoryApi';
import { getTransHisByAccountIdApi } from 'bnpl-shared/src/api/getTransHisByAccountIdApi';
import AssetFrame from 'bnpl-shared/src/components/AssetFrame';
import AssetFrameMock from 'bnpl-shared/src/components/AssetFrame/AssetFrameMock';
import { Spacer } from 'bnpl-shared/src/components/Spacer';
import { AppColors, PartnerCode } from 'bnpl-shared/src/constants';
import { useRemoteConfigs } from 'bnpl-shared/src/remote_configs/useRemoteConfigs';
import { AppText } from 'bnpl-shared/src/shared/react-native-customized';
import { StyleSheet } from 'bnpl-shared/src/shared/styles/StyleSheet';
import { Colors } from 'bnpl-shared/src/shared/styles/StyleUtils';
import { AdsInventoryId, RewardVoucherConfigs, TransactionFilterType, TransactionStatus } from 'bnpl-shared/src/types';
import React, { memo, useCallback, useEffect, useState } from 'react';
import { Platform, TouchableOpacity, View } from 'react-native';

import { usePartnerData } from '../multipartner_integration/helpers';
import AdvertisingProductPageSlider from 'bnpl-shared/src/components/AdvertisingProductPageSlider';
import { Templates } from 'bnpl-shared/src/components/AdvertisingProductPageSlider/types';

enum Tab {
  Redemtion,
  Banner,
}

const TabConfig = {
  [Tab.Redemtion]: {
    label: '✨ Ước gì được nấy',
  },
  [Tab.Banner]: {
    label: 'Ưu đãi',
  },
};

const PromotionSection = ({ onItemPress }: { onItemPress: (url: string) => void }) => {
  const chosenPartner = usePartnerData().getChosenPartner();
  const [tab, setTab] = useState([Tab.Redemtion, Tab.Banner]);
  const [activeTab, setActiveTab] = useState<Tab>(Tab.Redemtion);
  const [ready, setReady] = useState(false);
  const { getConfigWithType } = useRemoteConfigs();
  const partnerCode = chosenPartner?.partner_code;

  const defaultAssetFrameInventoryId =
    partnerCode === PartnerCode.CIMB ? AdsInventoryId.ASSET_INVENTORY_ID_CIMB : AdsInventoryId.ASSET_INVENTORY_ID_LOTTE;
  const assetFrameInventoryId = partnerCode
    ? getConfigWithType<RewardVoucherConfigs>('asset_frame_config')?.[partnerCode.toLocaleLowerCase()] ||
      defaultAssetFrameInventoryId
    : defaultAssetFrameInventoryId;

  const AdBannerInventoryID =
    partnerCode === PartnerCode.CIMB ? AdsInventoryId.BANNER_SLIDER : AdsInventoryId.BANNER_SLIDER_LOTTE;

  const [allowRedem, setAllowRedem] = useState(false);


  const checkRedem = useCallback(async () => {
    const fetchTrans = partnerCode === PartnerCode.CIMB ? getTransactionHistoryApi : getTransHisByAccountIdApi;
    const payload = {
      limit: 5,
      ...(partnerCode === PartnerCode.LOTTE
        ? {
            account_id: chosenPartner?.account_id,
            trans_type: [TransactionFilterType.PAYMENT],
          }
        : null),
    };
    try {
      const { transactions } = await fetchTrans(payload);
      if (transactions.length) {
        const hasSucceeded = transactions.some(trans => trans.status === TransactionStatus.SUCCEEDED);
        setAllowRedem(hasSucceeded);
      }
    } catch (e) {
    } finally {
      setReady(true);
    }
  }, [chosenPartner, partnerCode]);

  const handleEmpty = (tab: Tab) => {
    setTab(prev => prev.filter(item => item !== tab));
  };

  useEffect(() => {
    if (chosenPartner) {
      (async () => {
        await checkRedem();
      })();
    }
  }, [checkRedem, chosenPartner]);

  useEffect(() => {
    setActiveTab(tab[0]);
  }, [tab]);

  if (!tab.length) {
    return null;
  }

  const showTab = Boolean(tab.length > 1);

  return (
    <>
      <View style={styles.root}>
        {showTab ? (
          <View style={styles.loyaltyTab}>
            {tab.map((item: Tab, idx: number) => (
              <TouchableOpacity
                key={idx}
                onPress={() => setActiveTab(item)}
                style={[styles.loyaltyTabItem, activeTab === item && styles.activeTabItem]}>
                <AppText style={styles.tabTitle} color={activeTab === item ? AppColors.primary : AppColors.text}>
                  {TabConfig[item].label}
                </AppText>
              </TouchableOpacity>
            ))}
          </View>
        ) : null}

        {activeTab === Tab.Redemtion ? (
          <View style={styles.assetFrameContent}>
            {ready ? (
              <View>
                {allowRedem ? (
                  <>
                    {Platform.OS !== 'web' && showTab ? <Spacer height={12} /> : null}
                    <AssetFrame
                      assetInventoryId={assetFrameInventoryId}
                      onItemPress={onItemPress}
                      onEmpty={() => handleEmpty(Tab.Redemtion)}
                    />
                  </>
                ) : (
                  <>
                    {showTab ? <Spacer height={12} /> : null}
                    <AssetFrameMock
                      assetInventoryId={assetFrameInventoryId}
                      onEmpty={() => handleEmpty(Tab.Redemtion)}
                    />
                  </>
                )}
              </View>
            ) : null}
          </View>
        ) : null}

        {activeTab === Tab.Banner ? (
          <View style={styles.bannerContent}>
            {showTab ? <Spacer height={12} /> : null}
            <AdvertisingProductPageSlider request={{
              inventory_id: AdBannerInventoryID,
              extra_infos: {
                position: "paylater_homepage",
              },
            }}
            hasSkeleton
            containerPadding={16} template={Templates.SLIDER21}
            onAdsBannerClick={(adsItem) => onItemPress(adsItem.redirect_url)} />
            {showTab ? <Spacer height={16} /> : null}
          </View>
        ) : null}
      </View>
    </>
  );
};

export default memo(PromotionSection);

const styles = StyleSheet.create({
  root: {
    borderTopLeftRadius: 16,
    borderTopRightRadius: 16,
    paddingTop: 16,
    marginBottom: 8,
    backgroundColor: AppColors.white,
  },
  loyaltyTab: {
    paddingHorizontal: 16,
    flexDirection: 'row',
  },
  loyaltyTabItem: {
    paddingHorizontal: 16,
    paddingVertical: 6,
    borderRadius: 99,
    borderWidth: 1,
    borderColor: Colors.border,
    backgroundColor: AppColors.background,
    marginRight: 4,
  },
  activeTabItem: { borderColor: Colors.primary, backgroundColor: AppColors.white },
  assetFrameContent: {
    minHeight: 152,
  },
  bannerContent: {
    minHeight: 130,
  },
  tabTitle: {
    lineHeight: 18,
  },
});

import { LayoutChangeEvent, View } from 'react-native';
import { AppImage, AppText } from 'bnpl-shared/src/shared/react-native-customized';
import { images } from 'bnpl-shared/src/res';
import { AppColors } from 'bnpl-shared/src/constants';
import { Spacer } from 'bnpl-shared/src/components/Spacer';
import { StyleUtils } from 'bnpl-shared/src/shared/styles/StyleUtils';
import React, { FC, memo, useContext, useEffect } from 'react';
import { StyleSheet } from 'bnpl-shared/src/shared/styles/StyleSheet';
import { useRenewOverDraft, useRenewWhitelist } from 'bnpl-shared/src/features/renew_overdraft';
import { PredefinedCTA, RenewAction, RenewStatusType, ResourceState, UtmCampaign } from 'bnpl-shared/src/types';
import { formatDate } from 'bnpl-shared/src/shared/utils/date';
import { useNavigation } from 'bnpl-shared/src/shared/navigation/useNavigation';
import { NavigationContext } from 'bnpl-shared/src/utils/NavigationContext';
import { useRenewActionDispatcher } from 'bnpl-shared/src/features/renew_overdraft/helpers';
import { useUtmHandler } from 'bnpl-shared/src/hooks/useUtmHandler';
import RenewOverdraftTracking from 'bnpl-shared/src/features/renew_overdraft/tracking';
import mapPredefinedCTAtoComponent from 'bnpl-shared/src/utils/mapPredefinedCTAtoComponent';
import Badge from 'bnpl-shared/src/components/Badge';

type BannerContentProps = {
  ctas?: PredefinedCTA[];
  description?: string;
  onActionPress: (cta: string) => void;
  title?: string;
};

export const RenewOverDraftBanner: FC<{ onLayout?: ((event: LayoutChangeEvent) => void) | undefined }> = ({
  onLayout,
}) => {
  const { utmCampaign, markUtmCampaignUsed } = useUtmHandler();
  const navigation = useNavigation({ navigation: useContext(NavigationContext) });
  const { isInRenewWhitelist } = useRenewWhitelist();
  const { getRenewInfo, getRenewResourceState } = useRenewOverDraft();
  const { dispatchActionByStatus, dispatchAction } = useRenewActionDispatcher({ navigation });
  const renewStatus = getRenewInfo()?.renewStatus;
  const renewResourceState = getRenewResourceState();

  useEffect(() => {
    if (isInRenewWhitelist() && renewResourceState === ResourceState.READY && renewStatus) {
      RenewOverdraftTracking.RenewBanner.load({
        renew_status: renewStatus,
        eligible_status: getRenewInfo()?.renewable || false,
        rejected_reasons: getRenewInfo()?.description || '',
        expired_account: getRenewInfo()?.isExpired || false,
      });

      if (utmCampaign === UtmCampaign.RenewOverdraft && getRenewInfo()?.renewable) {
        markUtmCampaignUsed();
        if (getRenewInfo()?.ctas?.length) {
          dispatchActionByStatus(renewStatus);
        }
      }
      if (utmCampaign === UtmCampaign.RenewOverdraftApproved) {
        markUtmCampaignUsed();
        dispatchAction(RenewAction.SHOW_SUCCESS_TOAST);
      }
      if (renewStatus === RenewStatusType.OTP_VERIFIED || renewStatus === RenewStatusType.WAITING_APPROVE) {
        dispatchAction(RenewAction.POLLING_STATUS);
      }
    }
  }, [renewStatus, renewResourceState]);

  if (
    !isInRenewWhitelist() ||
    renewResourceState !== ResourceState.READY ||
    !renewStatus ||
    getRenewInfo()?.description === ''
  ) {
    return null;
  }

  const renderBannerContentByStatus = (status: RenewStatusType) => {
    switch (status) {
      case RenewStatusType.WAITING_APPROVE:
      case RenewStatusType.OTP_VERIFIED:
        return (
          <WaitingApproveBannerContent
            description={getRenewInfo()?.description}
            ctas={getRenewInfo()?.ctas}
            onActionPress={(cta: string) => {
              RenewOverdraftTracking.RenewBanner.clickCTA({
                renew_status: status,
                cta,
                rejected_reasons: getRenewInfo()?.description || '',
                expired_account: getRenewInfo()?.isExpired || false,
              });
              dispatchActionByStatus(status);
            }}
          />
        );
      case RenewStatusType.TEMPORARY_REJECTED:
        return (
          <TemporaryRejectBannerContent
            description={getRenewInfo()?.description}
            ctas={getRenewInfo()?.ctas}
            onActionPress={(cta: string) => {
              RenewOverdraftTracking.RenewBanner.clickCTA({
                renew_status: status,
                cta,
                rejected_reasons: getRenewInfo()?.description || '',
                expired_account: getRenewInfo()?.isExpired || false,
              });
              dispatchActionByStatus(status);
            }}
          />
        );
      case RenewStatusType.PERMANENT_REJECTED:
        return (
          <PermanentRejectBannerContent
            ctas={getRenewInfo()?.ctas}
            description={getRenewInfo()?.description}
            expiredDate={getRenewInfo()?.expiredDate}
            onActionPress={(cta: string) => {
              RenewOverdraftTracking.RenewBanner.clickCTA({
                renew_status: status,
                cta,
                rejected_reasons: getRenewInfo()?.description || '',
                expired_account: getRenewInfo()?.isExpired || false,
              });
              dispatchActionByStatus(status);
            }}
          />
        );
      default:
        return (
          <InitBannerContent
            title={`TK Trả Sau ${getRenewInfo()?.isExpired ? 'đã hết hạn' : 'sắp hết hạn'}`}
            ctas={getRenewInfo()?.ctas}
            description={getRenewInfo()?.description}
            onActionPress={(cta: string) => {
              RenewOverdraftTracking.RenewBanner.clickCTA({
                renew_status: status,
                cta,
                rejected_reasons: getRenewInfo()?.description || '',
                expired_account: getRenewInfo()?.isExpired || false,
              });
              dispatchActionByStatus(status);
            }}
          />
        );
    }
  };

  return <View onLayout={onLayout}>{renderBannerContentByStatus(renewStatus)}</View>;
};

const PermanentRejectBannerContent: FC<BannerContentProps & { expiredDate?: string }> = ({
  expiredDate,
  description,
  ctas,
  onActionPress,
}) => {
  let readableExpiredDate = '';
  if (expiredDate) {
    const date = new Date(expiredDate);
    readableExpiredDate = `sau ngày ${formatDate(date, 'dd/MM/yyyy')} `;
  }

  return (
    <View testID="permanent-reject-banner" style={[styles.container, styles.warningContainer]}>
      <AppImage source={images.IconWarningRedTriangle} height={24} width={24} tintColor={AppColors.warning2} />
      <Spacer width={8} />
      <AppText testID={'reject-text'} style={StyleUtils.flexOne}>
        {description ||
          `Rất tiếc, Tài Khoản Trả Sau của bạn sẽ bị khóa mua sắm ${readableExpiredDate}vì không được tái ký. Liên hệ CIMB để biết thêm chi tiết.`}
      </AppText>
      {mapPredefinedCTAtoComponent({
        ctas,
        onCTAPress: onActionPress,
        buttonStyle: styles.button,
        titleStyle: styles.buttonTitle,
      })}
    </View>
  );
};

const TemporaryRejectBannerContent: FC<BannerContentProps> = ({ onActionPress, ctas, description }) => {
  return (
    <View testID="temporary-reject-banner" style={[styles.container, styles.temporaryRejectContainer]}>
      <AppImage source={images.IconReceipt} height={24} width={24} tintColor={AppColors.primary} />
      <Spacer width={8} />
      <View style={StyleUtils.flexOne}>
        <AppText color={AppColors.text2}>
          {description ||
            'Rất tiếc, tái ký hợp đồng không thành công. Vui lòng liên hệ CIMB để biết thêm chi tiết và thử lại.'}
        </AppText>
      </View>
      {mapPredefinedCTAtoComponent({
        ctas,
        onCTAPress: onActionPress,
        buttonStyle: styles.button,
        titleStyle: styles.buttonTitle,
      })}
    </View>
  );
};

const WaitingApproveBannerContent: FC<BannerContentProps> = ({ description, ctas, onActionPress }) => {
  return (
    <View testID="waiting-approve-banner" style={[styles.container, styles.warningContainer]}>
      <AppImage source={images.IconWarningRedTriangle} height={24} width={24} tintColor={AppColors.warning2} />
      <Spacer width={8} />
      <AppText style={StyleUtils.flexOne} color={AppColors.text2}>
        {description || 'CIMB đang xét duyệt hồ sơ. Bạn chờ trong vài phút nhé'}
      </AppText>
      {mapPredefinedCTAtoComponent({
        ctas,
        onCTAPress: onActionPress,
        buttonStyle: styles.button,
        titleStyle: styles.buttonTitle,
      })}
    </View>
  );
};

const InitBannerContent: FC<BannerContentProps> = ({ title, onActionPress, ctas, description }) => {
  return (
    <View testID="init-banner" style={styles.container}>
      <AppImage source={images.IconReceipt} height={24} width={24} tintColor={AppColors.primary} />
      <Spacer width={8} />
      <View style={StyleUtils.flexOne}>
        <AppText>{title}</AppText>
        <AppText color={AppColors.text2}>
          {description || 'Vui lòng tái ký để tiếp tục sử dụng Tài khoản trả sau'}
        </AppText>
      </View>
      {mapPredefinedCTAtoComponent({
        ctas,
        onCTAPress: onActionPress,
        buttonStyle: styles.button,
        titleStyle: styles.buttonTitle,
      })}
      <Badge title="Gia hạn, nhận ngay 10K" variant="label" style={styles.badge} />
    </View>
  );
};

export default memo(RenewOverDraftBanner);

const styles = StyleSheet.create({
  container: {
    flexDirection: 'row',
    backgroundColor: AppColors.background4,
    borderRadius: 8,
    paddingHorizontal: 16,
    paddingVertical: 10,
    alignItems: 'center',
    width: "100%",
    marginTop: 8,
  },
  warningContainer: {
    backgroundColor: AppColors.warning,
  },
  expiredContainer: {
    backgroundColor: AppColors.red[2],
  },
  temporaryRejectContainer: {
    backgroundColor: '#FEF3F5',
  },
  button: {
    flexDirection: 'row',
    justifyContent: 'center',
    alignItems: 'center',
    width: 70,
    paddingHorizontal: 12,
    paddingVertical: 8,
    height: 36,
    borderRadius: 8,
    marginStart: 8,
    backgroundColor: AppColors.background,
    borderColor: AppColors.primary,
    borderWidth: 1,
  },
  buttonTitle: {
    fontSize: 12,
    color: AppColors.primary,
  },
  badge: {
    position: 'absolute',
    right: -2,
    top: -6,
    alignItems: 'flex-end',
},
});

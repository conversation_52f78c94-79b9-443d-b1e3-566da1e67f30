import React, { FC, useEffect, useState } from 'react';
import { StyleSheet } from 'bnpl-shared/src/shared/styles/StyleSheet';
import { ScrollView, TouchableWithoutFeedback, View, Platform } from 'react-native';
import { AppColors } from 'bnpl-shared/src/constants';
import { TwoSideView } from 'bnpl-shared/src/shared/TwoSideView/TwoSideView';
import { AppButton, AppText } from 'bnpl-shared/src/shared/react-native-customized';
import { LinkButton } from 'bnpl-shared/src/shared/LinkButton';
import { Spacer } from 'bnpl-shared/src/components/Spacer';
import { useRenewOverDraft } from 'bnpl-shared/src/features/renew_overdraft';
import RenewOverdraftTracking from 'bnpl-shared/src/features/renew_overdraft/tracking';
import { getSafeView } from 'bnpl-shared/src/utils';
import { LinkType, openCommonLink } from 'bnpl-shared/src/utils/openCommonLink';
import { useMultiPartnerLockStatus } from 'bnpl-shared/src/features/multipartner_integration/helpers';
import RewardBanner from 'bnpl-shared/src/features/reward_banner';
import { RewardBannerKey } from 'bnpl-shared/src/types';
import { PartnerCode } from 'bnpl-shared/src/constants';

export const RenewOverDraftInfoModal: FC<{
  onActionPress?: (action: 'term' | 'privacy' | 'main') => void;
  navigation?: any;
}> = ({ onActionPress, navigation }) => {
  const [userInfo, setUserInfo] = useState<{ key: string; value: string }[]>([]);
  const [disclaimerMarginBottom, setDisclaimerMarginBottom] = useState(0);
  const { getRenewInfo } = useRenewOverDraft();
  const { isLocked } = useMultiPartnerLockStatus();
  const renewAppendixInfo = getRenewInfo()?.appendixInfo;

  useEffect(() => {
    if (renewAppendixInfo) {
      setUserInfo([
        { key: 'Số điện thoại', value: renewAppendixInfo?.phone },
        { key: 'CMND/CCCD', value: renewAppendixInfo?.id_number },
        { key: 'Ngày cấp', value: renewAppendixInfo?.id_issued_date },
        { key: 'Ngày hết hạn', value: renewAppendixInfo?.id_expired_date },
        { key: 'Nơi cấp', value: renewAppendixInfo?.id_issued_location },
        { key: 'Ngày sinh', value: renewAppendixInfo?.birthday },
        { key: 'Họ tên', value: renewAppendixInfo?.full_name },
        { key: 'Giới tính', value: renewAppendixInfo?.gender },
        { key: 'Quốc tịch', value: renewAppendixInfo?.nationality },
        { key: 'Hình chân dung', value: renewAppendixInfo.face_info },
        { key: 'CMND cũ ', value: renewAppendixInfo?.old_id_number },
        { key: 'Số TKTT', value: renewAppendixInfo?.account_number },
        { key: 'Địa chỉ thường trú', value: renewAppendixInfo?.certify_address },
      ]);
    }
  }, [renewAppendixInfo]);

  const handleOnPrivacyPress = () => {
    RenewOverdraftTracking.InfoModal.ctaClick({ cta: 'privacy_policy' });
    onActionPress?.('privacy');
    openCommonLink(LinkType.ZALOPAY_PRIVACY, navigation);
    RenewOverdraftTracking.InfoModal.diplayDocument({
      type: 'privacy_policy',
    });
  };

  useEffect(() => {
    if (renewAppendixInfo) {
      RenewOverdraftTracking.InfoModal.show({
        account_status: isLocked ? 'locked' : 'active',
      });
    }
  }, [renewAppendixInfo, isLocked]);

  return (
    <>
      <View style={styles.root}>
        <ScrollView
          testID={'renew-info-modal'}
          nestedScrollEnabled={true}
          scrollEnabled={true}
          showsVerticalScrollIndicator={false}>
          <TouchableWithoutFeedback>
            <View>
              <View style={styles.infoSection}>
                {userInfo.map((item, index) => (
                  <TwoSideView
                    key={index}
                    style={styles.row}
                    left={<AppText style={styles.left}>{item.key}</AppText>}
                    right={<AppText style={styles.right}>{item.value || '--'}</AppText>}
                  />
                ))}
              </View>
              <View
                style={[
                  styles.disclaimer,
                  { marginBottom: Platform.OS === 'ios' ? disclaimerMarginBottom : disclaimerMarginBottom + 20 },
                ]}>
                <AppText size={12}>
                  Bằng cách nhấn “Tiếp tục”, tôi đồng ý với các{' '}
                  <LinkButton size={12} height={14} onPress={handleOnPrivacyPress}>
                    Chính sách bảo vệ quyền riêng tư của Zalopay
                  </LinkButton>{' '}
                  và cho phép Zalopay chia sẻ thông tin của tôi mà Zalopay có, kết quả xác thực Căn cước công dân của
                  tôi cho CIMB để sử dụng dịch vụ, sản phẩm của CIMB
                </AppText>
              </View>
            </View>
          </TouchableWithoutFeedback>
        </ScrollView>
        <View
          style={styles.actionSection}
          onLayout={event => setDisclaimerMarginBottom(Number(event?.nativeEvent?.layout?.height) || 100)}>
          <RewardBanner partner={PartnerCode.CIMB.toLocaleLowerCase()} bannerKey={RewardBannerKey.RenewApplication} />
          <Spacer height={16} />
          <View style={styles.wrapper}>
            <AppButton
              testID={'confirm-button'}
              title="Tiếp tục"
              onPress={() => {
                RenewOverdraftTracking.InfoModal.clickContinue();
                onActionPress?.('main');
              }}
            />
          </View>
        </View>
      </View>
    </>
  );
};

const styles = StyleSheet.create({
  root: {
    backgroundColor: AppColors.background2,
    paddingBottom: getSafeView().bottom,
    flex: 1,
  },
  left: {
    width: 100,
    flex: 1,
  },
  right: {
    width: 200,
    textAlign: 'right',
  },
  bodyStyle: {
    height: 450,
  },
  infoSection: {
    marginVertical: 20,
    marginHorizontal: 16,
    paddingHorizontal: 16,
    paddingTop: 16,
    paddingBottom: 8,
    backgroundColor: AppColors.background,
    borderRadius: 8,
  },
  actionSection: {
    position: 'absolute',
    width: '100%',
    left: 0,
    bottom: 0,
    paddingVertical: 16,
    backgroundColor: AppColors.background,
  },
  row: { paddingBottom: 16, flexDirection: 'row', alignItems: 'flex-start' },
  updateInfoLabel: {
    flex: 1,
    paddingBottom: 16,
    paddingStart: 16,
    flexDirection: 'row',
    alignItems: 'center',
  },
  wrapper: {
    paddingHorizontal: 16,
  },
  disclaimer: {
    paddingHorizontal: 16,
  },
});

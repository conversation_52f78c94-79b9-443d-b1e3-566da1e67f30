import React, { FC, useEffect, useRef } from 'react';
import { AppStateStatus, Dimensions, LayoutChangeEvent, View } from 'react-native';
import { AppButton, AppImage, AppText } from 'bnpl-shared/src/shared/react-native-customized';
import { images } from 'bnpl-shared/src/res';
import { AppColors, INVALID_ACCOUNT_ID } from 'bnpl-shared/src/constants';
import { Spacer } from 'bnpl-shared/src/components/Spacer';
import { StyleSheet } from 'bnpl-shared/src/shared/styles/StyleSheet';
import { StyleUtils } from 'bnpl-shared/src/shared/styles/StyleUtils';
import {
  AuthChallengeType,
  ExperimentName,
  GetNfcDataResponse,
  NfcStatus,
  PredefinedCTA,
  UMSource,
  UMStatus,
  UtmCampaign,
} from 'bnpl-shared/src/types';
import { hideLoading, showLoading, usePromptAuthChallengeFlow } from 'bnpl-shared/src/shared/ZaloPayModules';
import { setAppToast, ToastType } from 'bnpl-shared/src/redux/appToastReducer';
import { useDispatch } from 'react-redux';
import { InfoModalService, ModalType } from 'bnpl-shared/src/services';
import { NfcDataModalUI } from 'bnpl-shared/src/features/update_nfc/components/NfcDataModalUI';
import { store, useAppSelector } from 'bnpl-shared/src/redux/store';
import { getNfcDataApi, getNfcStatusApi, postSubmitNfcApi } from 'bnpl-shared/src/api/update_nfc';
import { usePartnerData } from 'bnpl-shared/src/features/multipartner_integration/helpers';
import { setNfcStatus, setNfcStatusCTA } from 'bnpl-shared/src/redux/updateNfcReducer';
import { useUtmHandler } from 'bnpl-shared/src/hooks/useUtmHandler';
import { LinkType, openCommonLink } from 'bnpl-shared/src/utils/openCommonLink';
import UpdateNfcTracking from 'bnpl-shared/src/features/update_nfc/tracking';
import { useABTesting } from 'bnpl-shared/src/hooks/useABTesting';
import { useErrorHandler } from 'bnpl-shared/src/features/multipartner_integration/helpers/useErrorHandler';
import mapPredefinedCTAtoComponent from 'bnpl-shared/src/utils/mapPredefinedCTAtoComponent';
import { FSAppState } from 'bnpl-shared/src/shared/react-native-customized/FSAppState';
import {useAuthChallengeSource} from "bnpl-shared/src/hooks/useAuthChallengeSource";

export const UpdateNfcBanner: FC<{
  onLayout?: ((event: LayoutChangeEvent) => void) | undefined;
  navigation?: any;
}> = ({ onLayout, navigation }) => {
  const dispatch = useDispatch();
  const { getChosenPartner } = usePartnerData();
  const authSource = useAuthChallengeSource();
  const chosenPartner = getChosenPartner();
  const { isInWhiteList } = useABTesting();
  const { utmCampaign, markUtmCampaignUsed } = useUtmHandler();
  const handleError = useErrorHandler({ trackingLocation: 'update-nfc-banner' });
  const { nfc_status, nfc_status_cta } = useAppSelector(state => state.updateNfc);
  const { promptAuthChallengeFlow } = usePromptAuthChallengeFlow();
  const intervalRef = useRef<any>();

  const showAppToast = (type: ToastType, message: string) => {
    dispatch(setAppToast({ message, type, duration: 3000 }));
  };

  //this cover usecase when user start update-nfc flow from ZPI then navigate to ZPA to update-nfc, then back to ZPI
  //when user back to ZPI, we need to fetch nfc status again
  useEffect(() => {
    const handleAppStateChange = async (state: AppStateStatus) => {
      if (state === 'active') {
        fetchUpdateNfcStatus();
      }
    };
    FSAppState.addEventListener('change', handleAppStateChange);
    return () => FSAppState.removeEventListener('change', handleAppStateChange);
  }, []);

  const startIntervalCheckSubmitStatus = () => {
    intervalRef.current = setInterval(() => {
      fetchUpdateNfcStatus();
    }, 5000);
  };

  const fetchNfcData = async (): Promise<GetNfcDataResponse | undefined> => {
    try {
      return await getNfcDataApi();
    } catch (error: any) {
      handleError({ error });
      UpdateNfcTracking.loadNfcDataModal({ error });
      return undefined;
    }
  };

  const fetchUpdateNfcStatus = () => {
    (async () => {
      try {
        const resp = await getNfcStatusApi();
        store.dispatch(setNfcStatus(resp.status));
        if (resp.cta) {
          store.dispatch(setNfcStatusCTA(resp.cta));
        }
      } catch (e: any) {
        console.log(e);
      }
    })();
  };

  //handle deeplink utm_campaign=update_nfc
  useEffect(() => {
    if (nfc_status === NfcStatus.MISSING_DATA || nfc_status === NfcStatus.SYNC_TO_PARTNER) {
      if (utmCampaign === UtmCampaign.UpdateNfc) {
        (async () => {
          await handleActionButton();
          markUtmCampaignUsed();
        })();
      }
    }
    if (nfc_status === NfcStatus.WAITING_APPROVAL) {
      startIntervalCheckSubmitStatus();
    } else if (nfc_status === NfcStatus.REJECTED || nfc_status === NfcStatus.VALID) {
      if (intervalRef.current && nfc_status === NfcStatus.VALID) {
        showAppToast(ToastType.SUCCESS, 'Cập nhật NFC thành công');
      }
      clearInterval(intervalRef.current);
    }
    return () => {
      clearInterval(intervalRef.current);
    };
  }, [nfc_status, utmCampaign]);

  const handleSubmitActionNfc = async (action: 'main' | 'privacy') => {
    switch (action) {
      case 'main':
        if (chosenPartner?.account_id && chosenPartner?.account_id !== INVALID_ACCOUNT_ID) {
          try {
            showLoading();
            const resp = await postSubmitNfcApi(chosenPartner?.account_id);
            if (resp.status) {
              dispatch(setNfcStatus(resp.status));
            }
          } catch (error: any) {
            handleError({ error });
          } finally {
            hideLoading();
          }
        }
        break;
      case 'privacy':
        openCommonLink(LinkType.ZALOPAY_PRIVACY, navigation);
        break;
    }
  };

  const showConfirmModal = () => {
    (async () => {
      const resp = await fetchNfcData();
      if (resp) {
        const userInfo = [
          { key: 'Họ tên', value: resp.name || '--' },
          { key: 'Ngày sinh', value: resp.dob || '--' },
          { key: 'Giới tính', value: resp.gender === 'MALE' ? 'Nam' : 'Nữ' },
          { key: 'Căn cước công dân', value: resp.id_number || '--' },
          { key: 'Ngày cấp', value: resp.id_issue_date || '--' },
          { key: 'Hình chân dung', value: 'Hình ảnh trong CCCD' },
          { key: 'CMND cũ', value: resp.old_id_number || '--' },
          { key: 'Quốc tịch', value: resp.nationality || '--' },
          { key: 'Ngày hết hạn', value: resp.id_expired_date || '--' },
          { key: 'Nơi cấp', value: resp.id_issued_location || '--' },
          { key: 'Địa chỉ thường trú', value: resp.permanent_residence || '--' },
          { key: 'Ngày cập nhật', value: resp.update_at || '--' },
        ];
        InfoModalService.showModal({
          screen: <NfcDataModalUI userInfo={userInfo} onActionPress={handleSubmitActionNfc} />,
          type: ModalType.BOTTOM_SHEET,
          bottomSheetProps: {
            title: 'Cập nhật thông tin',
            scrollable: false,
            containerStyle: {
              height: Dimensions.get('screen').height * 0.8,
              position: 'relative',
            },
            requestClose: () => {
              UpdateNfcTracking.clickCloseCTANfcDataModal();
            },
          },
        });
      }
    })();
  };

  const handleActionButton = async () => {
    UpdateNfcTracking.clickCTAUpdateNfcBanner({ status: nfc_status });
    switch (nfc_status) {
      case NfcStatus.MISSING_DATA:
        try {
          UpdateNfcTracking.redirectToNfc();
          const result = await promptAuthChallengeFlow(Number(authSource), AuthChallengeType.NFC);
          UpdateNfcTracking.callbackFromNfc({ result });
          if (result.status === UMStatus.Success) {
            fetchUpdateNfcStatus();
            showConfirmModal();
          }
        } catch (e: any) {
          if (e !== 'failed') {
            showAppToast(ToastType.ERROR, 'Có lỗi xảy ra, vui lòng thử lại sau.');
          }
        }
        break;
      case NfcStatus.SYNC_TO_PARTNER:
        showConfirmModal();
        break;
    }
  };

  if (
    nfc_status === NfcStatus.ACCOUNT_NOT_FOUND ||
    nfc_status === NfcStatus.VALID ||
    !isInWhiteList(ExperimentName.UPDATE_NFC)
  ) {
    return null;
  }

  if (nfc_status === NfcStatus.WAITING_APPROVAL) {
    UpdateNfcTracking.loadWaitingUpdateNfcBanner();
    return (
      <View onLayout={onLayout} testID="update-nfc-waiting-banner" style={styles.waitingContainer}>
        <AppText style={StyleUtils.flexOne} color={AppColors.text2}>
          Ngân hàng CIMB đang cập nhật thông tin NFC của bạn. Bạn chờ ít phút nhé.
        </AppText>
      </View>
    );
  }

  if (nfc_status === NfcStatus.REJECTED && nfc_status_cta) {
    return (
      <View onLayout={onLayout} testID="update-nfc-waiting-banner" style={styles.rejectContainer}>
        <AppText style={StyleUtils.flexOne} color={AppColors.text2}>
          {nfc_status_cta.description}
        </AppText>
        {mapPredefinedCTAtoComponent({
          buttonStyle: styles.buttonSecondary,
          titleStyle: styles.buttonTitleSecondary,
          ctas: nfc_status_cta.ctas,
          onCTAPress: cta => {
            switch (cta) {
              case PredefinedCTA.RETRY:
                showConfirmModal();
                break;
              default:
                return;
            }
          },
        })}
      </View>
    );
  }

  UpdateNfcTracking.loadUpdateNfcBanner();

  return (
    <View onLayout={onLayout} testID="update-nfc-banner" style={styles.container}>
      <AppImage source={images.IconReceipt} height={24} width={24} tintColor={AppColors.primary} />
      <Spacer width={8} />
      <AppText style={StyleUtils.flexOne} color={AppColors.text2}>
        Hoàn tất cập nhật NFC để tiếp tục sử dụng
      </AppText>
      <AppButton
        titleStyle={styles.buttonTitle}
        buttonStyle={[styles.button]}
        title="Cập nhật ngay"
        variant={'contained'}
        onPress={handleActionButton}
      />
    </View>
  );
};

const WIDTH = 310;

const styles = StyleSheet.create({
  container: {
    flexDirection: 'row',
    backgroundColor: AppColors.background4,
    borderRadius: 8,
    paddingHorizontal: 16,
    paddingVertical: 10,
    alignItems: 'center',
    width: WIDTH,
  },
  waitingContainer: {
    flexDirection: 'row',
    backgroundColor: AppColors.warning,
    borderRadius: 8,
    paddingHorizontal: 16,
    paddingVertical: 10,
    alignItems: 'center',
    width: WIDTH,
  },
  rejectContainer: {
    flexDirection: 'row',
    backgroundColor: '#FEF3F5',
    borderRadius: 8,
    paddingHorizontal: 16,
    paddingVertical: 10,
    alignItems: 'center',
    width: WIDTH,
  },
  button: {
    flexDirection: 'row',
    justifyContent: 'center',
    alignItems: 'center',
    width: 100,
    height: 36,
    marginStart: 8,
    borderRadius: 8,
  },
  buttonSecondary: {
    flexDirection: 'row',
    justifyContent: 'center',
    alignItems: 'center',
    width: 100,
    height: 36,
    marginStart: 8,
    borderRadius: 8,
    backgroundColor: AppColors.background,
    borderColor: AppColors.primary,
    borderWidth: 1,
  },
  buttonTitle: {
    fontSize: 12,
    color: AppColors.white,
  },
  buttonTitleSecondary: {
    fontSize: 12,
    color: AppColors.primary,
  },
});

import React, { useEffect, useRef } from 'react';
import { View, TouchableOpacity, Animated } from 'react-native';
import { Spacer } from 'bnpl-shared/src/components/Spacer';
import { AppColors } from 'bnpl-shared/src/constants';
import { images } from 'bnpl-shared/src/res';
import { AppImage, AppText } from 'bnpl-shared/src/shared/react-native-customized';
import { calculateScaleDimension, StyleSheet } from 'bnpl-shared/src/shared/styles/StyleSheet';
import { NfcStatus } from "bnpl-shared/src/types";
import { StyleUtils } from "bnpl-shared/src/shared/styles/StyleUtils";
import UpdateNfcTracking from "../update_nfc/tracking";

export const EXPANDING_HEIGHT = calculateScaleDimension(114);
export const COLLAPSED_HEIGHT = calculateScaleDimension(44);

export default function NFCFloatingBanner({ onToggleBanner, expanding, ctaLabel, onCTAClick, nfcStatus, customDescription }: NFCFloatingBannerProps) {
  const animationHeight = useRef(new Animated.Value(expanding ? EXPANDING_HEIGHT : COLLAPSED_HEIGHT)).current;
  const animationRotateArrow = useRef(new Animated.Value(expanding ? 0: 1)).current;
  const display = MapDisplayByNFCStatus[nfcStatus];

  useEffect(() => {
    UpdateNfcTracking.loadUpdateNfcFloatingBanner({ expanding, code: nfcStatus });
  }, [expanding, nfcStatus]);

  function handleToggle() {
    UpdateNfcTracking.toggleUpdateNfcFloatingBanner({ expanding, code: nfcStatus });
    Animated.timing(animationHeight, {
      toValue: expanding ? COLLAPSED_HEIGHT : EXPANDING_HEIGHT,
      duration: 200,
      useNativeDriver: false,
    }).start();
    Animated.timing(animationRotateArrow, {
      toValue: expanding ? 1 : 0,
      duration: 200,
      useNativeDriver: false,
    }).start();
    onToggleBanner();
  }

  const NFCImageSize = expanding ? 64 : 60;
  const description = customDescription || display?.description || 'Ngừng thanh toán qua Tài khoản trả sau nếu chưa xác thực sinh trắc học.';

  return (
    <TouchableOpacity activeOpacity={1} onPress={onCTAClick} testID="button-repayment">
      <Animated.View style={[styles.root, StyleUtils.shadow, { height: animationHeight }]}>
        {expanding && (<AppImage source={images.BackgroundUpdateNFCBanner} width={343} height={114} />)}
        <View style={styles.content}>
          <View style={styles.description}>
            <View style={styles.titleWrapper}>
              <AppText size={16} height={20} bold color={AppColors.dark[500]}>{display?.title || 'Cập nhật sinh trắc học'}</AppText>
              {!expanding && (
                <>
                  <Spacer width={4} />
                  <AppImage source={images.IconArrowNextOutlineActive} width={16} height={16} />
                </>
              )}
            </View>
            <Spacer height={expanding ? 4 : 12} />
            <AppText style={{ width: '100%' }} numberOfLines={3} size={14} height={18} color={AppColors.dark[500]}>
              {description}
            </AppText>
          </View>
          <View style={[styles.ctaWrapper, { marginTop: expanding ? 0 : -12 }]}>
            <AppImage source={images.ImageUpdateNFC} width={NFCImageSize} height={NFCImageSize} />
            <Spacer height={2} />
            {ctaLabel && (
              <View style={styles.cta}>
                <AppText size={12} height={16} color={AppColors.red[6]}>{ctaLabel}</AppText>
              </View>
            )}
          </View>
          <TouchableOpacity onPress={handleToggle}>
            <Animated.View style={{
              transform: [{
                rotate: animationRotateArrow.interpolate({
                  inputRange: [0, 1],
                  outputRange: ['90deg', '-90deg'],
                })
              }]
            }}>
              <AppImage source={images.IconArrowRight} width={16} height={16} />
            </Animated.View>
          </TouchableOpacity>
        </View>
      </Animated.View>
    </TouchableOpacity>
  );
};

interface NFCFloatingBannerProps {
  onToggleBanner: () => void;
  expanding: boolean;
  ctaLabel: string;
  onCTAClick: () => void;
  nfcStatus: NfcStatus;
  customDescription?: string;
}

const MapDisplayByNFCStatus: Partial<Record<NfcStatus, { title: string; description: string }>> = {
  [NfcStatus.MISSING_DATA]: {
    title: 'Cập nhật sinh trắc học',
    description: 'Ngừng thanh toán qua Tài khoản trả sau nếu chưa xác thực sinh trắc học.',
  },
  [NfcStatus.SYNC_TO_PARTNER]: {
    title: 'Cập nhật sinh trắc học',
    description: 'Ngừng thanh toán qua Tài khoản trả sau nếu chưa xác thực sinh trắc học.',
  },
  [NfcStatus.WAITING_APPROVAL]: {
    title: 'Đang cập nhật sinh trắc học',
    description: 'Ngân hàng CIMB đang cập nhật thông tin. Bạn chờ ít phút nhé.',
  },
}

const styles = StyleSheet.create({
  root: {
    position: 'relative',
    justifyContent: 'space-around',
    width: 343,
    borderRadius: 8,
    borderColor: AppColors.red[7],
    backgroundColor: AppColors.red[5],
    overflow: 'hidden',
  },
  content: {
    position: 'absolute',
    top: 0,
    left: 0,
    width: '100%',
    flexDirection: 'row',
    justifyContent: 'space-between',
    paddingTop: 12,
    paddingHorizontal: 12,
  },
  titleWrapper: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  remaining: {
    backgroundColor: AppColors.background,
    borderRadius: 8,
    padding: 4,
    width: 67,
    height: 60,
    justifyContent: 'center',
    alignItems: 'center',
  },
  ctaWrapper: {
    flexDirection: 'column',
    alignItems: 'center',
    flexShrink: 0,
  },
  cta: {
    borderRadius: 6,
    backgroundColor: AppColors.background,
    paddingHorizontal: 12,
    height: 24,
    justifyContent: 'center',
    alignItems: 'center',
    borderWidth: 1,
    borderColor: AppColors.red[6],
  },
  action: {
    flexDirection: 'row',
    justifyContent: 'flex-start',
    alignItems: 'center',
  },
  description: {
    flexDirection: 'column',
    alignItems: 'flex-start',
    flexShrink: 1,
  },
});

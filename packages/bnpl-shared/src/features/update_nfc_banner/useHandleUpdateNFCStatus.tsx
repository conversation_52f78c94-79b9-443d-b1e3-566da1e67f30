import React, { useEffect, useRef } from 'react';
import { AppStateStatus, Dimensions } from 'react-native';
import { useAppSelector } from "bnpl-shared/src/redux/store";
import { setNfcStatus, setNfcStatusCTA } from "bnpl-shared/src/redux/updateNfcReducer";
import { getNfcDataApi, getNfcStatusApi, postSubmitNfcApi } from 'bnpl-shared/src/api/update_nfc';
import { useDispatch } from "react-redux";
import {
  AuthChallengeType,
  GetNfcDataResponse,
  NfcStatus,
  UtmCampaign,
  UMStatus,
  PredefinedCTA,
} from 'bnpl-shared/src/types';
import { hideLoading, showLoading, usePromptAuthChallengeFlow } from 'bnpl-shared/src/shared/ZaloPayModules';
import { setAppToast, ToastType } from 'bnpl-shared/src/redux/appToastReducer';
import { InfoModalService, ModalType } from 'bnpl-shared/src/services';
import { NfcDataModalUI } from 'bnpl-shared/src/features/update_nfc/components/NfcDataModalUI';
import { usePartnerData } from 'bnpl-shared/src/features/multipartner_integration/helpers';
import { useUtmHandler } from 'bnpl-shared/src/hooks/useUtmHandler';
import { LinkType, openCommonLink } from 'bnpl-shared/src/utils/openCommonLink';
import UpdateNfcTracking from 'bnpl-shared/src/features/update_nfc/tracking';
import { useErrorHandler } from 'bnpl-shared/src/features/multipartner_integration/helpers/useErrorHandler';
import { FSAppState } from 'bnpl-shared/src/shared/react-native-customized/FSAppState';
import { useAuthChallengeSource } from "bnpl-shared/src/hooks/useAuthChallengeSource";
import { INVALID_ACCOUNT_ID } from "bnpl-shared/src/constants";
export default function useHandleNFCStatus(navigation: any) {
  const dispatch = useDispatch();
  const { getChosenPartner } = usePartnerData();
  const authSource = useAuthChallengeSource();
  const chosenPartner = getChosenPartner();
  const { utmCampaign, markUtmCampaignUsed } = useUtmHandler();
  const handleError = useErrorHandler({ trackingLocation: 'update-nfc-banner' });
  const { nfc_status, nfc_status_cta, is_update_nfc_banner_expanding } = useAppSelector(state => state.updateNfc);
  const { promptAuthChallengeFlow } = usePromptAuthChallengeFlow();
  const intervalRef = useRef<any>();

  const showAppToast = (type: ToastType, message: string) => {
    dispatch(setAppToast({ message, type, duration: 3000 }));
  };

  //this cover usecase when user start update-nfc flow from ZPI then navigate to ZPA to update-nfc, then back to ZPI
  //when user back to ZPI, we need to fetch nfc status again
  useEffect(() => {
    fetchUpdateNfcStatus();
    const handleAppStateChange = async (state: AppStateStatus) => {
      if (state === 'active') {
        fetchUpdateNfcStatus();
      }
    };
    FSAppState.addEventListener('change', handleAppStateChange);
    return () => FSAppState.removeEventListener('change', handleAppStateChange);
  }, []);

  const startIntervalCheckSubmitStatus = () => {
    intervalRef.current = setInterval(() => {
      fetchUpdateNfcStatus();
    }, 5000);
  };

  const fetchNfcData = async (): Promise<GetNfcDataResponse | undefined> => {
    try {
      return await getNfcDataApi();
    } catch (error: any) {
      handleError({ error });
      UpdateNfcTracking.loadNfcDataModal({ error });
      return undefined;
    }
  };

  const fetchUpdateNfcStatus = () => {
    (async () => {
      try {
        const resp = await getNfcStatusApi();
        if(resp){
          dispatch(setNfcStatus(resp.status));
          if (resp.cta) {
            dispatch(setNfcStatusCTA(resp.cta));
          }
        }
      } catch (e: any) {
        console.log(e);
      }
    })();
  };

  //handle deeplink utm_campaign=update_nfc
  useEffect(() => {
    if (nfc_status === NfcStatus.MISSING_DATA || nfc_status === NfcStatus.SYNC_TO_PARTNER) {
      if (utmCampaign === UtmCampaign.UpdateNfc) {
        (async () => {
          await handleAction();
          markUtmCampaignUsed();
        })();
      }
    }
    if (nfc_status === NfcStatus.WAITING_APPROVAL) {
      startIntervalCheckSubmitStatus();
    } else if (nfc_status === NfcStatus.REJECTED || nfc_status === NfcStatus.VALID) {
      if (intervalRef.current && nfc_status === NfcStatus.VALID) {
        showAppToast(ToastType.SUCCESS, 'Cập nhật NFC thành công');
      }
      clearInterval(intervalRef.current);
    }
    return () => {
      clearInterval(intervalRef.current);
    };
  }, [nfc_status, utmCampaign]);

  const handleSubmitActionNfc = async (action: 'main' | 'privacy') => {
    switch (action) {
      case 'main':
        if (chosenPartner?.account_id && chosenPartner?.account_id !== INVALID_ACCOUNT_ID) {
          try {
            showLoading();
            const resp = await postSubmitNfcApi(chosenPartner?.account_id);
            if (resp.status) {
              dispatch(setNfcStatus(resp.status));
            }
          } catch (error: any) {
            handleError({ error });
          } finally {
            hideLoading();
          }
        }
        break;
      case 'privacy':
        openCommonLink(LinkType.ZALOPAY_PRIVACY, navigation);
        break;
    }
  };

  const showConfirmModal = () => {
    (async () => {
      const resp = await fetchNfcData();
      if (resp) {
        const userInfo = [
          { key: 'Họ tên', value: resp.name || '--' },
          { key: 'Ngày sinh', value: resp.dob || '--' },
          { key: 'Giới tính', value: resp.gender === 'MALE' ? 'Nam' : 'Nữ' },
          { key: 'Căn cước công dân', value: resp.id_number || '--' },
          { key: 'Ngày cấp', value: resp.id_issue_date || '--' },
          { key: 'Hình chân dung', value: 'Hình ảnh trong CCCD' },
          { key: 'CMND cũ', value: resp.old_id_number || '--' },
          { key: 'Quốc tịch', value: resp.nationality || '--' },
          { key: 'Ngày hết hạn', value: resp.id_expired_date || '--' },
          { key: 'Nơi cấp', value: resp.id_issued_location || '--' },
          { key: 'Địa chỉ thường trú', value: resp.permanent_residence || '--' },
          { key: 'Ngày cập nhật', value: resp.update_at || '--' },
        ];
        InfoModalService.showModal({
          screen: <NfcDataModalUI userInfo={userInfo} onActionPress={handleSubmitActionNfc} />,
          type: ModalType.BOTTOM_SHEET,
          bottomSheetProps: {
            title: 'Cập nhật thông tin',
            scrollable: false,
            containerStyle: {
              height: Dimensions.get('screen').height * 0.8,
              position: 'relative',
            },
            requestClose: () => {
              UpdateNfcTracking.clickCloseCTANfcDataModal();
            },
          },
        });
      }
    })();
  };

  const handleAction = async () => {
    UpdateNfcTracking.clickUpdateNfcFloatingBanner({ expanding: is_update_nfc_banner_expanding, code: nfc_status });
    switch (nfc_status) {
      case NfcStatus.MISSING_DATA:
        try {
          UpdateNfcTracking.redirectToNfc();
          const result = await promptAuthChallengeFlow(Number(authSource), AuthChallengeType.NFC);
          UpdateNfcTracking.callbackFromNfc({ result });
          if (result.status === UMStatus.Success) {
            fetchUpdateNfcStatus();
            showConfirmModal();
          }
        } catch (e: any) {
          if (e !== 'failed') {
            showAppToast(ToastType.ERROR, 'Có lỗi xảy ra, vui lòng thử lại sau.');
          }
        }
        break;
      case NfcStatus.SYNC_TO_PARTNER:
        showConfirmModal();
        break;
      case NfcStatus.REJECTED:
        // scenario này chỉ handle với trường hợp retry
        if (nfc_status_cta?.ctas?.includes(PredefinedCTA.RETRY)) {
          showConfirmModal();
        }
        break;
      default:
        break;
    }
  };
  const getCTALabel = () => {
    switch (nfc_status) {
      case NfcStatus.MISSING_DATA:
      case NfcStatus.SYNC_TO_PARTNER:
        return 'Cập nhật ngay';
      case NfcStatus.REJECTED:
        return nfc_status_cta?.ctas?.includes(PredefinedCTA.RETRY) ? 'Thử lại' : '';
      default:
        return '';
    }
  }
  return {
    handleAction,
    getCTALabel,
  }
}

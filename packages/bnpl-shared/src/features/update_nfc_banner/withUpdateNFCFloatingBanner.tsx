import React from 'react';
import { View  } from 'react-native';
import { StyleSheet } from 'bnpl-shared/src/shared/styles/StyleSheet';
import UpdateNFCFloatingBanner, { COLLAPSED_HEIGHT, EXPANDING_HEIGHT } from "./UpdateNFCFloatingBanner";
import { useAppSelector } from "bnpl-shared/src/redux/store";
import { setIsShowNfcBannerExpanding } from "bnpl-shared/src/redux/updateNfcReducer";
import { useDispatch } from "react-redux";
import {
  ABTestingGroup,
  ExperimentName,
  NfcStatus,
} from 'bnpl-shared/src/types';
import useHandleUpdateNFCStatus from "./useHandleUpdateNFCStatus";

export const withUpdateNFCFloatingBanner = (
  WrappedComponent: React.ComponentType<any>,
) => {
  const displayName = WrappedComponent.displayName || WrappedComponent.name || 'Component';

  const ComponentWithBanner = (props: any) => {
    const abTesting = useAppSelector(state => state.abTesting);
    const { nfc_status, nfc_status_cta, is_update_nfc_banner_expanding } = useAppSelector(state => state.updateNfc);
    const dispatch = useDispatch();
    const { handleAction, getCTALabel } = useHandleUpdateNFCStatus(props.navigation);
    const isInWhiteList =
      abTesting[ExperimentName.UPDATE_NFC]?.toLowerCase() !== ABTestingGroup.Control_Group.toLowerCase();
    const bannerVisible = isInWhiteList && SHOULD_RENDER_STATUS.includes(nfc_status);

    if (!bannerVisible) {
      return <WrappedComponent {...props} />;
    }
    return (
      <View style={styles.wrapper}>
        <div style={{ height: '100%', overflowY: "auto", paddingBottom: is_update_nfc_banner_expanding ? EXPANDING_HEIGHT : COLLAPSED_HEIGHT }}>
          <WrappedComponent {...props} />
        </div>
        <View style={styles.bannerWrapper}>
          <UpdateNFCFloatingBanner
            nfcStatus={nfc_status}
            onToggleBanner={() => {
              dispatch(setIsShowNfcBannerExpanding(!is_update_nfc_banner_expanding));
            }}
            expanding={is_update_nfc_banner_expanding}
            ctaLabel={getCTALabel()}
            onCTAClick={handleAction}
            customDescription={(nfc_status === NfcStatus.REJECTED && nfc_status_cta?.description) || ''}
          />
        </View>
      </View>
    );
  };

  ComponentWithBanner.displayName = `withNFCFloatingBanner(${displayName})`;

  return ComponentWithBanner;
};

export const SHOULD_RENDER_STATUS = [
  NfcStatus.MISSING_DATA,
  NfcStatus.SYNC_TO_PARTNER,
  NfcStatus.WAITING_APPROVAL,
  NfcStatus.REJECTED,
];

const styles = StyleSheet.create({
  wrapper: {
    height: '100%',
    overflow: 'hidden',
  },
  bannerWrapper: {
    position: 'absolute',
    left: 0,
    right: 0,
    bottom: 8,
    alignItems: 'center',
    justifyContent: 'center',
  },
});

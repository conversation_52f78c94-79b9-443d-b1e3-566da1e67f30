// import { useAuthentication as _useAuthentication } from "auth_challenge/AuthenticationContext";
import { UMAuthResult, startAuthChallenge } from '../lib/ZalopaySDK/startAuthChallenge';
import { PLATFORM, UMStatus } from '../constants';
import { getAppInfo } from '../lib/ZalopaySDK';
import { AuthChallengeType } from '../types';

export interface IAuthChallenge {
  source: number;
  authType: AuthChallengeType;
  params?: Object;
  options?: Object;
  skipResult?: boolean;
  numberOfRetries?: number;
}

export const useAuthentication = () => {

  const launchAuthChallenge = async (payload: IAuthChallenge): Promise<UMAuthResult> => {
    const { platform } = (await getAppInfo()).data;
    if (platform === PLATFORM.ZPA) {
      const result = await startAuthChallenge({ payload });

      return result?.data
        ? result.data
        : {
            status: UMStatus.Failure,
            result: '',
          };
    } else {
      return new Promise((resolve, _reject) => {
        if (!window?.UM_AUTH_CHALLENGE?.openAuthChallenge) {
          const error = {
            errorCode: "CALL_FUNC_OPEN_AUTH_CHALLENGE_FAILED",
            errorMessage: "openAuthChallenge not ready'",
          };
          throw error;
        }

        window?.UM_AUTH_CHALLENGE?.openAuthChallenge({
          ...payload,
          onCompleted: (result: any) => {
            resolve({
              status: result.status,
              result: result.resultData,
            } as UMAuthResult);
          },
        });
      });
    }
  };
  return { launchAuthChallenge };
};

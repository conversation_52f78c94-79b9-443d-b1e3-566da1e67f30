import { useCIMBStatementController } from 'bnpl-shared/src/utils/statement_controller/useCIMBStatementController';
import { useCallback } from 'react';
import { PartnerCode } from 'bnpl-shared/src/constants';
import { CIMBRuleCheck } from 'bnpl-shared/src/screens/RepaymentScreen/CIMBRuleCheck';
import { useLotteStatementController } from 'bnpl-shared/src/utils/statement_controller/useLotteStatementController';
import { LotteRuleCheck } from 'bnpl-shared/src/features/multipartner_integration/screens/RepayScreen/models/LotteRuleCheck';
import { useUserBalanceController } from 'bnpl-shared/src/utils/useUserBalanceController';

/**
 * Stm = Statement
 */
export enum UIRules {
  ShowAmountInput,
  ShowStmStatus,
  ShowSelectableInput,
  ShowStmSummary,
  EnableRepayButton,
  ShowEmptyState,
  ShowTotalOutstanding,
  ShowRepayMethodSelector,
  ShowHomeRepayWidget,
  ShowErrorState,
  ShowLoadingState,
  ShowTotalAmount,
}

export const useStatementRuleCheck = (partnerCode: PartnerCode) => {
  const { balanceConditions } = useUserBalanceController();
  const { stmConditions: stmConditionCIMB } = useCIMBStatementController();
  const { stmConditions: stmConditionsLotte } = useLotteStatementController();
  const checkUIRule = useCallback(
    (rule: UIRules): boolean => {
      if (partnerCode === PartnerCode.CIMB) {
        return CIMBRuleCheck(stmConditionCIMB, balanceConditions, rule);
      } else if (partnerCode === PartnerCode.LOTTE) {
        return LotteRuleCheck(stmConditionsLotte, balanceConditions, rule);
      } else if (partnerCode === PartnerCode.MINI_BNPL) {
        return true;
      }
      return false;
    },
    [stmConditionCIMB, stmConditionsLotte],
  );
  return { checkUIRule };
};

import {
  AccountInfo,
  RepayMethod,
  RoutingInfo,
  SourceOfFund,
  TransactionStatus,
  TransactionType,
} from 'bnpl-shared/src/types';
import { TransactionHistoryDetail } from 'bnpl-shared/src/types/TransactionHistoryTypes';
import { AccountStatus, MPOnboardingStatus, PartnerCode, RepayMethodId } from 'bnpl-shared/src/constants';

export const SUCCESS_RESPONSE = { ok: true, status: 200, json: async () => 'success' };

export const createSuccessMockResp = (body: any) => {
  const resp = {
    ok: true,
    status: 200,
    json: async () => body,
    clone: () => resp,
  };
  return resp;
};

export const createFailMockResp = (body: any, statusCode: number) => {
  const resp = {
    ok: false,
    status: statusCode,
    json: async () => body,
    clone: () => resp,
  };
  return resp;
};

export const mockFetch = (param: any) => {
  if (param instanceof Error) {
    return (global.fetch as jest.Mock).mockRejectedValueOnce(param);
  }
  (global.fetch as jest.Mock).mockResolvedValueOnce(param);
};

export const _TransactionDetails: TransactionHistoryDetail[] = [
  {
    id: '0',
    type: TransactionType.PAYMENT,
    trans_status: TransactionStatus.PROCESSING,
    amount: 100000,
    description: 'Gờ CaFee D16 319 LTK',
    status_description: 'Giao dịch đang được xử lý. Mong bạn thông cảm, chờ trong giây lát và không thử lại.',
    updated_at: '2022-05-27T15:18:25+07:00',
    zp_trans_id: '220809000002206',
  },
  {
    id: '999',
    type: TransactionType.PAYMENT,
    trans_status: TransactionStatus.FAILED,
    amount: 100000,
    description: 'Gờ CaFee D16 319 LTK',
    updated_at: '2022-05-27T15:18:25+07:00',
    zp_trans_id: '220809000002206',
    status_description: 'Hệ thống đang gián đoạn. Mong bạn thông cảm và thử lại',
  },
  {
    id: '1',
    type: TransactionType.PAYMENT,
    trans_status: TransactionStatus.SUCCEEDED,
    amount: 200000,
    description: 'Gờ CaFee D16 319 LTK',
    updated_at: '2022-05-27T15:18:25+07:00',
    zp_trans_id: '220809000002206',
  },
  {
    id: '2',
    type: TransactionType.REPAYMENT,
    trans_status: TransactionStatus.SUCCEEDED,
    amount: 69000,
    description: 'Thanh toán dư nợ',
    updated_at: '2022-05-27T15:18:25+07:00',
    zp_trans_id: '220809000002206',
    stages: [
      { message: 'Zalopay trừ tiền thành công', status: TransactionStatus.SUCCEEDED },
      { message: 'CIMB nhận tiền  thành công', status: TransactionStatus.SUCCEEDED },
    ],
  },
  {
    id: '3',
    type: TransactionType.REPAYMENT,
    trans_status: TransactionStatus.PROCESSING,
    zp_trans_id: '220809000002206',
    amount: 69000,
    description: 'Thanh toán dư nợ',
    updated_at: '2022-05-27T15:18:25+07:00',
    stages: [
      { message: 'Zalopay trừ tiền thành công', status: TransactionStatus.SUCCEEDED },
      { message: 'CIMB đang xử lý', status: TransactionStatus.PROCESSING },
    ],
    status_description:
      'Đã hoàn tất nghĩa vụ thanh toán dư nợ. Thời gian xử lý tối đa 1 ngày làm việc. Các khoản phí, lãi phát sinh (nếu có) do chưa xử lý kịp thời sẽ được miễn trừ.',
  },
  {
    id: '4',
    type: TransactionType.REPAYMENT,
    trans_status: TransactionStatus.FAILED,
    zp_trans_id: '220809000002206',
    amount: 69000,
    description: 'Thanh toán dư nợ',
    updated_at: '2022-05-27T15:18:25+07:00',
    status_description: 'Hệ thống đang gặp gián đoạn. Mong bạn thông cảm và thử lại.',
    stages: [
      { message: 'Zalopay trừ tiền thành công', status: TransactionStatus.SUCCEEDED },
      { message: 'CIMB nhận tiền  thất bại', status: TransactionStatus.FAILED },
    ],
  },
  {
    id: '123',
    type: TransactionType.REFUND_FOR_PAYMENT,
    trans_status: TransactionStatus.SUCCEEDED,
    amount: 200000,
    description: 'Hoàn hạn mức khả dụng',
    zp_trans_id: '220809000002206',
    updated_at: '2022-05-27T15:18:25+07:00',
  },
  {
    id: '1234',
    type: TransactionType.REFUND_FOR_REPAYMENT,
    trans_status: TransactionStatus.SUCCEEDED,
    amount: 200000,
    description: 'Điều chỉnh cấn trừ',
    zp_trans_id: '220809000002206',
    updated_at: '2022-05-27T15:18:25+07:00',
  },
];

export const _TransactionHistory = {
  next_cursor: '1234',
  transactions: [
    {
      id: '2',
      type: TransactionType.REPAYMENT,
      status: TransactionStatus.SUCCEEDED,
      amount: 69000,
      description: 'Thanh toán dư nợ',
      created_at: '2022-05-27T15:18:25+07:00',
      updated_at: '2022-05-27T15:18:25+07:00',
    },
    {
      id: '4',
      type: TransactionType.REPAYMENT,
      status: TransactionStatus.FAILED,
      amount: 69000,
      description: 'Thanh toán dư nợ',
      created_at: '2022-05-27T15:18:25+07:00',
      updated_at: '2022-05-27T15:18:25+07:00',
    },
    {
      id: '3',
      type: TransactionType.REPAYMENT,
      status: TransactionStatus.PROCESSING,
      amount: 69000,
      description: 'Thanh toán dư nợ',
      created_at: '2022-05-27T15:18:25+07:00',
      updated_at: '2022-05-27T15:18:25+07:00',
    },
    {
      id: '0',
      type: TransactionType.PAYMENT,
      status: TransactionStatus.PROCESSING,
      amount: 100000,
      description: 'Gờ CaFee D16 319 LTK',
      created_at: '2022-05-27T15:18:25+07:00',
      updated_at: '2022-05-27T15:18:25+07:00',
    },
    {
      id: '999',
      type: TransactionType.PAYMENT,
      status: TransactionStatus.FAILED,
      amount: 100000,
      description: 'Gờ CaFee D16 319 LTK',
      created_at: '2022-05-27T15:18:25+07:00',
      updated_at: '2022-05-27T15:18:25+07:00',
    },
    {
      id: '1',
      type: TransactionType.PAYMENT,
      status: TransactionStatus.SUCCEEDED,
      amount: 200000,
      description: 'Gờ CaFee D16 319 LTK',
      created_at: '2022-05-27T15:18:25+07:00',
      updated_at: '2022-05-27T15:18:25+07:00',
    },
    {
      id: '123',
      type: TransactionType.REFUND_FOR_PAYMENT,
      status: TransactionStatus.SUCCEEDED,
      amount: 200000,
      description: 'Hoàn hạn mức khả dụng',
      created_at: '2022-05-27T15:18:25+07:00',
      updated_at: '2022-05-27T15:18:25+07:00',
    },
    {
      id: '1234',
      type: TransactionType.REFUND_FOR_REPAYMENT,
      status: TransactionStatus.SUCCEEDED,
      amount: 200000,
      description: 'Điều chỉnh cấn trừ',
      created_at: '2022-05-27T15:18:25+07:00',
      updated_at: '2022-05-27T15:18:25+07:00',
    },
  ],
};

export const _FakeRepayMethods: RepayMethod[] = [
  {
    id: RepayMethodId.ZaloPay,
    title: 'Ví Zalopay',
    icon: 'https://simg.zalopay.com.vn/fs/bnpl/image/IconZaloPay.png',
    description: [
      {
        text: 'Trực tiếp trên ứng dụng Zalopay',
        icon: 'check',
      },
      {
        text: 'Không có hoàn tiền',
        icon: 'cross',
      },
    ],
  },
  {
    id: RepayMethodId.VietQR,
    title: 'VietQR',
    icon: 'https://simg.zalopay.com.vn/fs/bnpl/image/IconVietQR.png',
    description: [
      {
        text: 'Trực tiếp trên ứng dụng ngân hàng',
        icon: 'check',
      },
      {
        text: 'Hoàn tiền 1k/giao dịch',
        icon: 'check',
      },
    ],
  },
];

export const FakeCIMBAccountInfo = {
  id: '123',
  zalopay_id: '123',
  partner_code: 'CIMB',
  status: AccountStatus.ACTIVE,
  total_limit: '100000',
  created_at: '1',
  statement_date: '1',
  statement_grace_full_date: '1',
  benefits: [],
};

export const FakeLotteAccountInfo = {
  id: '123',
  zalopay_id: '123',
  partner_code: 'LFVN',
  status: AccountStatus.ACTIVE,
  total_limit: '100000',
  created_at: '1',
  statement_date: '1',
  statement_grace_full_date: '1',
  onboarding_next_step: '',
  benefits: [],
};

export const FakePartnerCIMB: RoutingInfo = {
  account_id: '1234',
  request_id: '123',
  descriptions: ['Hạn mức đến 10 triệu', 'Thanh toán tối thiểu', 'Chọn ngày đến hạn'],
  fee: 'Phí 20.000đ/tháng',
  has_submit_to_partner: false,
  is_approved: false,
  is_created: false,
  is_eligible_for_onboarding: false,
  partner_code: PartnerCode.CIMB,
  partner_name: 'CIMB',
  process_bar: { total_step: 3, current_step: 3 },
  status: MPOnboardingStatus.APPROVED,
  onboarding_next_step: '',
};

export const FakePartnerLotte: RoutingInfo = {
  account_id: '1234',
  request_id: '123',
  descriptions: ['Hạn mức đến 10 triệu', 'Thanh toán tối thiểu', 'Chọn ngày đến hạn'],
  fee: 'Phí 20.000đ/tháng',
  has_submit_to_partner: false,
  is_approved: false,
  is_created: false,
  is_eligible_for_onboarding: false,
  partner_code: PartnerCode.LOTTE,
  partner_name: 'LOTTE',
  process_bar: { total_step: 3, current_step: 3 },
  status: MPOnboardingStatus.APPROVED,
  onboarding_next_step: '',
};

export const FakeListSourceOfFund: SourceOfFund[] = [
  {
    type: 'wallet',
    id: '*********',
    display_image: 'https://i.imgur.com/e6OsUBi.png',
    display_name: 'Ví Zalopay',
    is_default: true,
    is_enabled: false,
  },
  {
    type: 'atm',
    id: '*********',
    display_image: 'https://i.imgur.com/e6OsUBi.png',
    display_name: 'Vietcombank****1234',
    is_default: false,
    is_enabled: true,
  },
  {
    type: 'tktl',
    id: '*********',
    display_image: 'https://i.imgur.com/e6OsUBi.png',
    display_name: 'Tài Khoản Tích Lũy',
    is_default: false,
    is_enabled: false,
  },
];

export const fakeAccountInfo: AccountInfo = {
  id: '123',
  zalopay_id: '123',
  partner_code: PartnerCode.CIMB,
  status: AccountStatus.ACTIVE,
  total_limit: '100000',
  created_at: '1',
  statement_date: '1',
  statement_grace_full_date: '1',
  benefits: [],
};

export const fakeBindingInfo = {
  request_id: '169919166789189632',
  basic_profile: {
    full_name: 'Lê thị hòa',
    phone_number: '**********',
    id_number: '************',
    full_name_suggestion: 'Lê thị hòa',
    um_phone_number: '**********',
  },
  detail_profile: {
    birthday: '04/10/1999',
    id_issued_date: '04/10/2021',
    id_issued_location: 'Cục trưởng cục cảnh sát ĐKQL cư trú và DLQL về dân cư',
    permanent_address: '51/B Khu Phố 2, Thị Trấn Bình Đại, Huyện Bình Đại, Tỉnh Bến Tre',
    temp_residence_address: '51/B Khu Phố 2, Thị Trấn Bình Đại, Huyện Bình Đại, Tỉnh Bến Tre',
    gender: 'MALE',
    education: 'EDU003',
    occupation: '34',
    job_title: '07',
    employment_status: 'EPM001',
    source_of_fund: '3',
    monthly_income: '********',
    living_city: '075',
    loan_purpose: '3008',
    opening_casa_purpose: 'OPENING_CASA_PURPOSE',
  },
  binding_step: {
    current_step: {
      int_value: 3,
      string_value: 'bod2-succeeded',
    },
    all_steps: [
      {
        int_value: -2,
        string_value: 'bod-resubmit',
      },
      {
        int_value: 0,
        string_value: 'bod1-succeeded',
      },
      {
        int_value: 1,
        string_value: 'bod1-rejected',
      },
      {
        int_value: 2,
        string_value: 'bod2-processing',
      },
      {
        int_value: 3,
        string_value: 'bod2-succeeded',
      },
      {
        int_value: 4,
        string_value: 'bod2-rejected',
      },
      {
        int_value: 5,
        string_value: 'otp-verified',
      },
      {
        int_value: 6,
        string_value: 'waiting-approve',
      },
      {
        int_value: 7,
        string_value: 'approved',
      },
      {
        int_value: 8,
        string_value: 'rejected',
      },
    ],
  },
};

export const rejectFaceChallengeResp = {
  code: 257,
  reason: 'ONBOARDING_PROFILE_NOT_ELIGIBLE',
  message: 'Tài khoản chưa đủ điều kiện tham gia đăng ký.',
  metadata: {
    notice: {
      title: 'Xác thực gương mặt',
      desc: 'Vì sự an toàn và bảo mật của bạn, hãy thực hiện xác minh khuôn mặt để chứng thực danh tính chủ sở hữu tài khoản. Đừng lo, việc này chỉ kéo dài khoảng 10 giây.',
      ctas: [
        {
          type: 'REDIRECT_FACE_AUTHENTICATE_PROCESS',
          metadata: null,
        },
      ],
    },
  },
};

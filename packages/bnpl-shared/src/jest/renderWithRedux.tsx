import { renderHook } from '@testing-library/react-hooks';
import { render } from '@testing-library/react-native';
import React from 'react';
import { Provider } from 'react-redux';
import { store } from '../redux/store';
import { NavigationContext } from 'bnpl-shared/src/utils/NavigationContext';
import { FAKE_NAVIGATION } from './constants';
import { NavigationProvider } from '../components/NavigationWrapper/context';
import { NavigationWrapper } from '../components/NavigationWrapper';

export const renderWithRedux = (children: any) => {
  return render(<Provider store={store}>{children}</Provider>);
};

export const renderWithReduxAndNavigationContext = (children: any) => {
  return render(
    <Provider store={store}>
      <NavigationContext.Provider value={FAKE_NAVIGATION}>{children}</NavigationContext.Provider>
    </Provider>,
  );
};

export const renderReduxWithNavigationWrapper = (children: any) => {
  return render(
    <Provider store={store}>
       <NavigationWrapper>
        <NavigationContext.Provider value={FAKE_NAVIGATION}>
          {children}
        </NavigationContext.Provider>
      </NavigationWrapper>
    </Provider>
  );
};

export const renderHookWithRedux = (callback: any) => {
  return renderHook(callback, { wrapper: ({ children }) => <Provider store={store}>{children}</Provider> }) as any;
};

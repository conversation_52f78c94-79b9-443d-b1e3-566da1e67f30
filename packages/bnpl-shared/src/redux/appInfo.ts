import { createSlice, PayloadAction } from '@reduxjs/toolkit';
import { AppInfo } from 'bnpl-shared/src/lib/ZalopaySDK/type';

const slice = createSlice({
  name: 'appInfo',
  initialState: {
    appVersion: '',
    platform: '',
  },
  reducers: {
    setAppInfo(state, action: PayloadAction<AppInfo>) {
      state.appVersion = action.payload.appVersion;
      state.platform = action.payload.platform;
    },
  },
});

export const appInfoReducer = slice.reducer;
export const { setAppInfo } = slice.actions;

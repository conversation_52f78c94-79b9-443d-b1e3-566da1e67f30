import { createSlice, PayloadAction } from '@reduxjs/toolkit';
import { ResourceState, StateResource, MiniBnplTotalOutstandingState } from '../types';

type State = {
  data: StateResource<MiniBnplTotalOutstandingState | null>;
};

const slice = createSlice({
  name: 'miniBnplTotalOutstanding',
  initialState: {
    data: { state: ResourceState.INIT },
  } as State,
  reducers: {
    setMiniBnplTotalOutstanding(state, action: PayloadAction<StateResource<MiniBnplTotalOutstandingState | null>>) {
      state.data = action.payload;
    },
  },
});

export const miniBnplTotalOutstandingReducer = slice.reducer;
export const { setMiniBnplTotalOutstanding } = slice.actions;

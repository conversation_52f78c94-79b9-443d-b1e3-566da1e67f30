import { configureStore } from '@reduxjs/toolkit';
import { TypedUseSelectorHook, useSelector } from 'react-redux';
import { personalExtraDataReducer } from '../screens/onboarding-flow/PersonalExtraDataScreen';
import { utmReducer } from './utmReducer';
import { bindingReducer } from './bindingReducer';
import { userBalanceReducer } from './userBalanceReducer';
import { userStatementReducer } from './userStatementReducer';
import { reloadableComponentsReducer } from './reloadableComponentsReducer';
import { globalReducer } from 'bnpl-shared/src/redux/globalReducer';
import { remoteConfigsReducer } from 'bnpl-shared/src/redux/remoteConfigsReducer';
import { abTestingReducer } from 'bnpl-shared/src/redux/abTestingReducer';
import { autoRepaymentReducer } from 'bnpl-shared/src/redux/autoRepaymentReducer';
import { appToastReducer } from 'bnpl-shared/src/redux/appToastReducer';
import { featureRenewOverDraftReducer } from 'bnpl-shared/src/redux/featureRenewOverDraftReducer';
import { routingReducer } from 'bnpl-shared/src/redux/routingReducer';
import { multiPartnerReducer } from 'bnpl-shared/src/redux/multipartnerReducer';
import { accountInfoReducer } from 'bnpl-shared/src/redux/accountInfoReducer';
import { servicesReducer } from 'bnpl-shared/src/redux/servicesReducer';
import { lotteStatementReducer } from 'bnpl-shared/src/redux/lotteStatementReducer';
import { updateNfcReducer } from 'bnpl-shared/src/redux/updateNfcReducer';
import { debtObligationReducer } from 'bnpl-shared/src/redux/debtObligationReducer';
import { surveyModalReducer } from 'bnpl-shared/src/redux/surveyModalReducer';
import { lotteOnboardingDataReducer } from 'bnpl-shared/src/redux/lotteOnboardingDataReducer';
import { miniBnplTotalOutstandingReducer } from "./miniBnplTotalOutstandingReducer";
import { appInfoReducer } from './appInfo';

export const store = configureStore({
  reducer: {
    appInfo: appInfoReducer,
    binding: bindingReducer,
    utm: utmReducer,
    userBalance: userBalanceReducer,
    userStatement: userStatementReducer,
    lotteStatement: lotteStatementReducer,
    personalExtraData: personalExtraDataReducer,
    reloadableComponents: reloadableComponentsReducer,
    globalReducer: globalReducer,
    autoRepaymentReducer: autoRepaymentReducer,
    abTesting: abTestingReducer,
    remoteConfigs: remoteConfigsReducer,
    appToast: appToastReducer,
    featureRenewOverDraft: featureRenewOverDraftReducer,
    routingHistory: routingReducer,
    multiPartner: multiPartnerReducer,
    accountInfo: accountInfoReducer,
    services: servicesReducer,
    updateNfc: updateNfcReducer,
    debtObligation: debtObligationReducer,
    surveyModal: surveyModalReducer,
    lotteOnboardingData: lotteOnboardingDataReducer,
    miniBnplTotalOutstanding: miniBnplTotalOutstandingReducer,
  },
});

export type RootState = ReturnType<typeof store.getState>;
export const useAppSelector: TypedUseSelectorHook<RootState> = useSelector;

//DO NOT EDIT
//this file should not be edited manually, instead run script sync_image_res to update new image in res/images folder
export const local_images: any = {
  BackgroundAccountScreen: require('./BackgroundAccountScreen.png'),
  BackgroundGradientPrimaryTop: require('./BackgroundGradientPrimaryTop.png'),
  BackgroundRepayScreen: require('./BackgroundRepayScreen.png'),
  IconAccountInfo: require('./IconAccountInfo.png'),
  IconActionUnlock: require('./IconActionUnlock.png'),
  IconAppLogo: require('./IconAppLogo.png'),
  IconArrowNext: require('./IconArrowNext.png'),
  IconArrowNextLine: require('./IconArrowNextLine.png'),
  IconArrowRight: require('./IconArrowRight.png'),
  IconAutoRepayment: require('./IconAutoRepayment.png'),
  IconBagCircle: require('./IconBagCircle.png'),
  IconBankRegisterProcessing: require('./IconBankRegisterProcessing.png'),
  IconBankRegisterSuccess: require('./IconBankRegisterSuccess.png'),
  IconBottomHistoryActive: require('./IconBottomHistoryActive.png'),
  IconBottomHistoryInactive: require('./IconBottomHistoryInactive.png'),
  IconBottomHomeActive: require('./IconBottomHomeActive.png'),
  IconBottomHomeInactive: require('./IconBottomHomeInactive.png'),
  IconBottomProfileActive: require('./IconBottomProfileActive.png'),
  IconBottomProfileInactive: require('./IconBottomProfileInactive.png'),
  IconBottomRepayActive: require('./IconBottomRepayActive.png'),
  IconBottomRepayInactive: require('./IconBottomRepayInactive.png'),
  IconCalendar: require('./IconCalendar.png'),
  IconCalendarCircle: require('./IconCalendarCircle.png'),
  IconChatCirclePink: require('./IconChatCirclePink.png'),
  IconCheck: require('./IconCheck.png'),
  IconCheckCircle: require('./IconCheckCircle.png'),
  IconCheckCircleBlue: require('./IconCheckCircleBlue.png'),
  IconCheckCircleBlueLight: require('./IconCheckCircleBlueLight.png'),
  IconClockCircle: require('./IconClockCircle.png'),
  IconClose: require('./IconClose.png'),
  IconCopy: require('./IconCopy.png'),
  IconDollarCircle: require('./IconDollarCircle.png'),
  IconDollarYellowCircle: require('./IconDollarYellowCircle.png'),
  IconDotted: require('./IconDotted.png'),
  IconDummyAvatar1: require('./IconDummyAvatar1.png'),
  IconDummyAvatar2: require('./IconDummyAvatar2.png'),
  IconDummyAvatar3: require('./IconDummyAvatar3.png'),
  IconDummyAvatar4: require('./IconDummyAvatar4.png'),
  IconFaceId: require('./IconFaceId.png'),
  IconFaqCircleBlue: require('./IconFaqCircleBlue.png'),
  IconGuideCircleOrange: require('./IconGuideCircleOrange.png'),
  IconGuideStep1: require('./IconGuideStep1.png'),
  IconGuideStep2: require('./IconGuideStep2.png'),
  IconGuideStep3: require('./IconGuideStep3.png'),
  IconHeadPhone: require('./IconHeadPhone.png'),
  IconInfoBlueCircle: require('./IconInfoBlueCircle.png'),
  IconInfoFeeService: require('./IconInfoFeeService.png'),
  IconInfoFeeUse: require('./IconInfoFeeUse.png'),
  IconInfoICircleNoFill: require('./IconInfoICircleNoFill.png'),
  IconInfoPartner: require('./IconInfoPartner.png'),
  IconLock: require('./IconLock.png'),
  IconLockLine: require('./IconLockLine.png'),
  IconLogoCIMB: require('./IconLogoCIMB.png'),
  IconLogoCIMBFull: require('./IconLogoCIMBFull.png'),
  IconLogoCIMBRound: require('./IconLogoCIMBRound.png'),
  IconLotte: require('./IconLotte.png'),
  IconLotteSmall: require('./IconLotteSmall.png'),
  IconManageSOF: require('./IconManageSOF.png'),
  IconMoneyBag: require('./IconMoneyBag.png'),
  IconMoneyFillGold: require('./IconMoneyFillGold.png'),
  IconNotification: require('./IconNotification.png'),
  IconNotificationLineGreen: require('./IconNotificationLineGreen.png'),
  IconPrioritySOF: require('./IconPrioritySOF.png'),
  IconQuestion: require('./IconQuestion.png'),
  IconReceipt: require('./IconReceipt.png'),
  IconReminderSetting: require('./IconReminderSetting.png'),
  IconReminderSettingDevice: require('./IconReminderSettingDevice.png'),
  IconRepaymentPaid: require('./IconRepaymentPaid.png'),
  IconRepaymentRefunded: require('./IconRepaymentRefunded.png'),
  IconSearch: require('./IconSearch.png'),
  IconSettingBell: require('./IconSettingBell.png'),
  IconSettingCalendar: require('./IconSettingCalendar.png'),
  IconSignPadReset: require('./IconSignPadReset.png'),
  IconStar: require('./IconStar.png'),
  IconStepProgress1: require('./IconStepProgress1.png'),
  IconStepProgress2: require('./IconStepProgress2.png'),
  IconStepProgress3: require('./IconStepProgress3.png'),
  IconSubject1: require('./IconSubject1.png'),
  IconSubject2: require('./IconSubject2.png'),
  IconSubject3: require('./IconSubject3.png'),
  IconSubject4: require('./IconSubject4.png'),
  IconSubject5: require('./IconSubject5.png'),
  IconSubject6: require('./IconSubject6.png'),
  IconSubject7: require('./IconSubject7.png'),
  IconSubject8: require('./IconSubject8.png'),
  IconSubject9: require('./IconSubject9.png'),
  IconSubjectRenew: require('./IconSubjectRenew.png'),
  IconSupport: require('./IconSupport.png'),
  IconThunder: require('./IconThunder.png'),
  IconTip: require('./IconTip.png'),
  IconTnc1: require('./IconTnc1.png'),
  IconTnc2: require('./IconTnc2.png'),
  IconTnc3: require('./IconTnc3.png'),
  IconTransFeePaidLate: require('./IconTransFeePaidLate.png'),
  IconTransFeePaidLateCircle: require('./IconTransFeePaidLateCircle.png'),
  IconTransFeePaidTransfer: require('./IconTransFeePaidTransfer.png'),
  IconTransFeeUse: require('./IconTransFeeUse.png'),
  IconTransFeeUseCircle: require('./IconTransFeeUseCircle.png'),
  IconTransPurchase: require('./IconTransPurchase.png'),
  IconTransRefund: require('./IconTransRefund.png'),
  IconTransRepayment: require('./IconTransRepayment.png'),
  IconUtilityAutoRepay: require('./IconUtilityAutoRepay.png'),
  IconUtilityDefaultSOF: require('./IconUtilityDefaultSOF.png'),
  IconUtilityListSOF: require('./IconUtilityListSOF.png'),
  IconUtilityLock: require('./IconUtilityLock.png'),
  IconUtilityNoti: require('./IconUtilityNoti.png'),
  IconUtilityPrioritySOF: require('./IconUtilityPrioritySOF.png'),
  IconUtilityReminder: require('./IconUtilityReminder.png'),
  IconUtilityStatement: require('./IconUtilityStatement.png'),
  IconUtilityTerminate: require('./IconUtilityTerminate.png'),
  IconUtilityUnlock: require('./IconUtilityUnlock.png'),
  IconWaringHexagon: require('./IconWaringHexagon.png'),
  IconWarningCircle: require('./IconWarningCircle.png'),
  IconWarningFill: require('./IconWarningFill.png'),
  IconWarningFillGradient: require('./IconWarningFillGradient.png'),
  IconWarningRedTriangle: require('./IconWarningRedTriangle.png'),
  IconXCircle: require('./IconXCircle.png'),
  IconZalo: require('./IconZalo.png'),
  IconZlp: require('./IconZlp.png'),
  IconZlpSupport: require('./IconZlpSupport.png'),
  ImageFaceChallengeScreen: require('./ImageFaceChallengeScreen.png'),
  ImageVerifyIssueNFC: require('./ImageVerifyIssueNFC.png'),
};

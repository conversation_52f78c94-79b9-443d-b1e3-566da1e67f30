import { AccountScreenView } from './AccountScreenView';
import { FAKE_NAVIGATION } from 'bnpl-shared/src/jest/constants';
import React from 'react';
import { renderReduxWithNavigationWrapper } from 'bnpl-shared/src/jest/renderWithRedux';
import { PartnerCode } from 'bnpl-shared/src/constants';
import { getUserInfo } from 'bnpl-shared/src/shared/ZaloPayModules';
import { Gender } from 'bnpl-shared/src/types';
import { waitFor } from '@testing-library/react-native';

describe(AccountScreenView.name, function () {
  it('render as expected', async () => {
    (getUserInfo as jest.Mock).mockResolvedValue({
      displayName: 'Phong',
      zalopay_id: '123',
      gender: Gender.MALE,
      profile_level: 2,
      birth_day: '123',
      phone: '**********',
      email: '123',
      zalo_id: '123',
      avatar: '123',
    });
    const { queryByTestId } = renderReduxWithNavigationWrapper(
      <AccountScreenView
        partnerCode={PartnerCode.CIMB}
        actions={{
          handleUtilityAction: jest.fn(),
        }}
        contract={{
          visible: false,
          info: { url: 'url', password: 'password' },
          setVisible: jest.fn(),
          useFileViewer: false,
        }}
        navigation={FAKE_NAVIGATION}
      />,
    );
    await waitFor(() => expect(queryByTestId('user-info-section')).toBeTruthy());
    expect(queryByTestId('utilities-section')).toBeTruthy();
  });
});

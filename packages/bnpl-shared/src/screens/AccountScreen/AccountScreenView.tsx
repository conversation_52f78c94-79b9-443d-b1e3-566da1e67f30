import React, { FC, useEffect } from 'react';
import { ScrollView, View } from 'react-native';
import { useAccountViewModel } from './useAccountViewModel';
import { StyleSheet } from 'bnpl-shared/src/shared/styles/StyleSheet';
import { AppColors } from 'bnpl-shared/src/constants';
import { Spacer } from 'bnpl-shared/src/components/Spacer';
import {
  AccountInfoSection,
  MoreInfoSection,
  UserInfoSection,
  AccountUtilitySection,
} from 'bnpl-shared/src/screens/AccountScreen/components';
import { StyleUtils } from 'bnpl-shared/src/shared/styles/StyleUtils';
import { UtilitySectionTracking } from 'bnpl-shared/src/screens/HomeScreen/components/UtilitySection/tracking';
import ContractModal from 'bnpl-shared/src/screens/HomeScreen/components/UtilitySection/ContractModal';

export const AccountScreenView: FC<ReturnType<typeof useAccountViewModel>> = props => {
  useEffect(() => {
    UtilitySectionTracking.trackAccountScreenShow();
  }, []);

  return (
    <View style={styles.root}>
      <ScrollView
        testID={'utilities-section'}
        showsVerticalScrollIndicator={false}
        bounces={false}
        style={StyleUtils.flexOne}>
        <UserInfoSection />
        <View style={styles.scroll}>
          <MoreInfoSection onPress={props.actions.handleUtilityAction} />
          <Spacer height={20} />
          <AccountUtilitySection
            onPress={props.actions.handleUtilityAction}
            title={'Tiện ích khác'}
            utilities={props.uiConfig?.getOtherUtilities()}
          />
          <Spacer height={20} />
          <AccountInfoSection
            partnerCode={props.partnerCode}
            onInfoItemPress={props.actions.handleUtilityAction}
            infoItems={props.uiConfig?.getAccountInfoItems()}
            feeDescription={props.uiConfig?.getFeeDescription()}
          />
          <Spacer height={20} />
          <AccountUtilitySection
            onPress={props.actions.handleUtilityAction}
            title={'Quản lý tài khoản'}
            utilities={props.uiConfig?.getAccountUtilities()}
          />
        </View>
      </ScrollView>
      <ContractModal
        onRequestClose={() => props.contract.setVisible(false)}
        contractUrl={props.contract.info?.url || ''}
        password={props.contract.info?.password}
        visible={props.contract.visible}
        useFileViewer={props.contract.useFileViewer}
      />
    </View>
  );
};

const styles = StyleSheet.create({
  root: {
    flex: 1,
    backgroundColor: AppColors.background2,
  },
  scroll: {
    flex: 1,
    backgroundColor: AppColors.background2,
    borderTopLeftRadius: 16,
    borderTopRightRadius: 16,
    marginTop: -48,
    padding: 16,
  },
  card: {
    justifyContent: 'center',
    alignContent: 'center',
    padding: 12,
    backgroundColor: AppColors.background,
    borderRadius: 8,
  },
  item: {
    width: 95,
    height: 58,
    justifyContent: 'center',
    alignItems: 'center',
  },
  divider: {
    marginHorizontal: 8,
    width: 1,
    height: 58,
    backgroundColor: AppColors.divider,
  },
  feature: {
    flexDirection: 'row',
    alignContent: 'center',
    padding: 12,
    backgroundColor: AppColors.background,
    borderRadius: 8,
  },
  generalSection: {
    padding: 12,
    backgroundColor: AppColors.background,
    borderRadius: 8,
  },
  hozDivider: {
    width: '100%',
    height: 1,
    backgroundColor: AppColors.divider,
  },
});

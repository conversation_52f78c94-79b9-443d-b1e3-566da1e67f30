import React, { FC } from 'react';
import { AppText } from 'bnpl-shared/src/shared/react-native-customized';
import { StyleSheet } from 'bnpl-shared/src/shared/styles/StyleSheet';
import { AppColors, PartnerCode, Utility } from 'bnpl-shared/src/constants';
import { TwoSideView } from 'bnpl-shared/src/shared/TwoSideView/TwoSideView';
import { Section } from 'bnpl-shared/src/components';
import { LinkButton } from 'bnpl-shared/src/shared/LinkButton';
import { View } from 'react-native';
import { Skeleton } from 'bnpl-shared/src/shared/animated-wrappers/Skeleton';
import { Spacer } from 'bnpl-shared/src/components/Spacer';
import { random } from 'lodash';

export enum AccountInfoType {
  Contract,
  ProductInfo,
  StatementDate,
  DueDate,
  Fee,
}

export type AccountInfoItem = {
  type: AccountInfoType;
  value?: string;
};

export const AccountInfoSection: FC<{
  partnerCode: PartnerCode;
  infoItems?: AccountInfoItem[];
  onInfoItemPress: (utility: Utility) => void;
  feeDescription?: string;
}> = ({ infoItems, onInfoItemPress, partnerCode, feeDescription }) => {
  const renderInfoItem = (item: AccountInfoItem) => {
    switch (item.type) {
      case AccountInfoType.Contract:
        return (
          <TwoSideView
            testID={'item-contract'}
            key={'Hợp đồng'}
            style={styles.item}
            left={<AppText>Hợp đồng</AppText>}
            right={
              <LinkButton testID={'link-button-contract'} onPress={() => onInfoItemPress(Utility.ViewContract)}>
                Xem chi tiết
              </LinkButton>
            }
          />
        );
      case AccountInfoType.ProductInfo:
        return (
          <TwoSideView
            testID={'item-product-info'}
            key={'Thông tin sản phẩm'}
            style={styles.item}
            left={<AppText>Thông tin sản phẩm</AppText>}
            right={
              <LinkButton testID={'link-button-product'} onPress={() => onInfoItemPress(Utility.ProductInfo)}>
                Xem chi tiết
              </LinkButton>
            }
          />
        );
      case AccountInfoType.StatementDate:
        const renderStmDateDesc = () => {
          if (partnerCode === PartnerCode.CIMB) {
            return null;
          }

          const content = item.value?.includes('11')
            ? 'Bao gồm các giao dịch phát sinh từ 18:00:00 ngày 11 tháng trước tới 17:59:59 của ngày 11 tháng sao kê hiện tại. Thời gian chốt sao kê có thể chậm tối đa 3 tiếng.'
            : 'Bao gồm các giao dịch phát sinh từ 18:00:00 ngày 1 tháng trước tới 17:59:59 của ngày 1 tháng sao kê hiện tại. Thời gian chốt sao kê có thể chậm tối đa 3 tiếng.';
          return (
            <AppText size={12} style={styles.stmDateDesc}>
              {content}
            </AppText>
          );
        };
        return (
          <View key={'Ngày phát sinh sao kê'}>
            <TwoSideView
              testID={'item-statement-date'}
              style={styles.item}
              left={<AppText>Ngày phát sinh sao kê</AppText>}
              right={<AppText>{item.value}</AppText>}
            />
            {renderStmDateDesc()}
          </View>
        );
      case AccountInfoType.DueDate:
        return (
          <TwoSideView
            testID={'item-due-date'}
            key={'Đến hạn thanh toán'}
            style={styles.item}
            left={<AppText>Đến hạn thanh toán</AppText>}
            right={<AppText>{item.value}</AppText>}
          />
        );
      case AccountInfoType.Fee:
        return (
          <View key={'Phí dịch vụ'}>
            <TwoSideView
              testID={'item-fee'}
              style={styles.item}
              left={<AppText>Phí dịch vụ</AppText>}
              right={<AppText>{item.value}</AppText>}
            />
            {feeDescription && (
              <View style={styles.warningBanner}>
                <AppText>{feeDescription}</AppText>
              </View>
            )}
          </View>
        );
    }
  };

  if (!infoItems) {
    const renderLoadingItem = (index: number) => {
      return (
        <TwoSideView
          key={index}
          style={styles.item}
          right={<Skeleton style={styles.loading} width={random(100, 150, false)} height={18} />}
          left={<Skeleton style={styles.loading} width={random(100, 150, false)} height={18} />}
        />
      );
    };
    return (
      <View testID={'loading'}>
        <Skeleton style={styles.loading} width={150} height={20} />
        <Spacer height={16} />
        <View style={styles.root}>{[0, 1, 2, 3].map(value => renderLoadingItem(value))}</View>
      </View>
    );
  }

  if (!infoItems?.length) {
    return null;
  }

  return (
    <Section.AccountScreen title="Thông tin chung">
      {infoItems.map(value => renderInfoItem(value))}
    </Section.AccountScreen>
  );
};

const styles = StyleSheet.create({
  root: {
    backgroundColor: AppColors.background,
    borderRadius: 8,
  },
  item: {
    height: 48,
    paddingHorizontal: 16,
    paddingVertical: 12,
  },
  warningBanner: {
    backgroundColor: AppColors.warning,
    paddingHorizontal: 16,
    paddingVertical: 12,
    marginHorizontal: 16,
    marginBottom: 16,
    borderRadius: 8,
  },
  loading: {
    borderRadius: 4,
  },
  stmDateDesc: {
    color: AppColors.text2,
    paddingHorizontal: 16,
  },
});

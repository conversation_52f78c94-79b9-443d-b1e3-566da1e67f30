import { AccountScreenUIConfig, CIMBAccountScreenUIConfig, LotteAccountScreenUIConfig } from './AccountScreenUIConfig';
import {
  FakeCIMBAccountInfo,
  FakeLotteAccountInfo,
  FakePartnerCIMB,
  FakePartnerLotte,
} from 'bnpl-shared/src/jest/fakeData';
import { MPOnboardingStatus, Utility } from 'bnpl-shared/src/constants';
import { AccountInfoType } from 'bnpl-shared/src/screens/AccountScreen/components';

describe(AccountScreenUIConfig.name, () => {
  describe(CIMBAccountScreenUIConfig.name, () => {
    it('when account is not approved yet', () => {
      const uiConfig = new CIMBAccountScreenUIConfig({
        accountInfo: FakeCIMBAccountInfo,
        partnerData: {
          ...FakePartnerCIMB,
          status: MPOnboardingStatus.WAITING_APPROVE,
        },
      });
      expect(uiConfig.getAccountInfoItems().flatMap(value => value.type)).toEqual([
        AccountInfoType.ProductInfo,
        AccountInfoType.StatementDate,
        AccountInfoType.DueDate,
      ]);
      expect(uiConfig.getOtherUtilities().length).toEqual(0);
      expect(uiConfig.getAccountUtilities().length).toEqual(0);
    });
    it('when account is approved', () => {
      const uiConfig = new CIMBAccountScreenUIConfig({
        accountInfo: FakeCIMBAccountInfo,
        partnerData: {
          ...FakePartnerCIMB,
          status: MPOnboardingStatus.APPROVED,
        },
        isLocked: false,
      });
      expect(uiConfig.getAccountInfoItems().flatMap(value => value.type)).toEqual([
        AccountInfoType.Contract,
        AccountInfoType.ProductInfo,
        AccountInfoType.StatementDate,
        AccountInfoType.DueDate,
        AccountInfoType.Fee,
      ]);
      expect(uiConfig.getOtherUtilities()).toEqual([
        Utility.SetDefaultSourceOfFund,
        Utility.ManageListSOF,
        Utility.Notification,
      ]);
      expect(uiConfig.getAccountUtilities()).toEqual([Utility.LockAccount, Utility.TerminateAccount]);
    });

    it('when account is approved & payment is locked', () => {
      const uiConfig = new CIMBAccountScreenUIConfig({
        accountInfo: FakeCIMBAccountInfo,
        partnerData: {
          ...FakePartnerCIMB,
          status: MPOnboardingStatus.APPROVED,
        },
        isLocked: true,
      });
      expect(uiConfig.getAccountInfoItems().flatMap(value => value.type)).toEqual([
        AccountInfoType.Contract,
        AccountInfoType.ProductInfo,
        AccountInfoType.StatementDate,
        AccountInfoType.DueDate,
        AccountInfoType.Fee,
      ]);
      expect(uiConfig.getOtherUtilities()).toEqual([Utility.Notification]);
      expect(uiConfig.getAccountUtilities()).toEqual([Utility.UnlockAccount, Utility.TerminateAccount]);
    });
  });
  describe(LotteAccountScreenUIConfig.name, () => {
    it('when account is not approved yet', () => {
      const uiConfig = new LotteAccountScreenUIConfig({
        accountInfo: FakeLotteAccountInfo,
        partnerData: {
          ...FakePartnerLotte,
          status: MPOnboardingStatus.WAITING_APPROVE,
        },
      });
      expect(uiConfig.getAccountInfoItems().flatMap(value => value.type)).toEqual([
        AccountInfoType.ProductInfo,
        AccountInfoType.StatementDate,
        AccountInfoType.DueDate,
      ]);
      expect(uiConfig.getOtherUtilities().length).toEqual(0);
      expect(uiConfig.getAccountUtilities().length).toEqual(0);
    });
    it('when account is approved', () => {
      const uiConfig = new LotteAccountScreenUIConfig({
        accountInfo: FakeLotteAccountInfo,
        partnerData: {
          ...FakePartnerLotte,
          status: MPOnboardingStatus.APPROVED,
        },
      });
      expect(uiConfig.getAccountInfoItems().flatMap(value => value.type)).toEqual([
        AccountInfoType.Contract,
        AccountInfoType.ProductInfo,
        AccountInfoType.StatementDate,
        AccountInfoType.DueDate,
        AccountInfoType.Fee,
      ]);
      expect(uiConfig.getOtherUtilities()).toEqual([Utility.Notification]);
      expect(uiConfig.getAccountUtilities()).toEqual([Utility.LockAccount]);
    });
  });
});

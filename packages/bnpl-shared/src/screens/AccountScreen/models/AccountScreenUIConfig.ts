import { MPOnboardingStatus, PartnerCode, Utility } from 'bnpl-shared/src/constants';
import { AccountInfoItem, AccountInfoType } from '../components';
import { ABTestingGroup, AccountInfo, ExperimentName, PartnerData } from 'bnpl-shared/src/types';
import { store } from 'bnpl-shared/src/redux/store';
import { checkFreeTrialAvailable } from 'bnpl-shared/src/utils/checkFreeTrialAvailable';
import { differenceInMinutes } from 'date-fns';

export type AccountUIConfigParams = { accountInfo?: AccountInfo; partnerData: PartnerData; isLocked?: boolean };

export abstract class AccountScreenUIConfig {
  protected accountInfo?: AccountInfo;
  protected partnerData: PartnerData;
  protected isLocked?: boolean = false;

  constructor(params: AccountUIConfigParams) {
    this.accountInfo = params.accountInfo;
    this.partnerData = params.partnerData;
    this.isLocked = params.isLocked;
  }

  abstract getOtherUtilities(): Utility[];
  abstract getAccountUtilities(): Utility[];
  abstract getAccountInfoItems(): AccountInfoItem[];
  abstract getFeeDescription(): string;
}

export class CIMBAccountScreenUIConfig extends AccountScreenUIConfig {
  getAccountInfoItems(): AccountInfoItem[] {
    if (this.accountInfo && this.partnerData.status === MPOnboardingStatus.APPROVED) {
      return [
        {
          type: AccountInfoType.Contract,
        },
        {
          type: AccountInfoType.ProductInfo,
        },
        {
          type: AccountInfoType.StatementDate,
          value: this.accountInfo.statement_date,
        },
        {
          type: AccountInfoType.DueDate,
          value: this.accountInfo.statement_grace_full_date,
        },
        {
          type: AccountInfoType.Fee,
          value: this.partnerData.fee,
        },
      ];
    }
    return [
      {
        type: AccountInfoType.ProductInfo,
      },
      {
        type: AccountInfoType.StatementDate,
        value: 'Ngày 28 hằng tháng',
      },
      {
        type: AccountInfoType.DueDate,
        value: 'Ngày 4 hằng tháng',
      },
    ];
  }

  getAccountUtilities(): Utility[] {
    if (this.accountInfo && this.partnerData.status === MPOnboardingStatus.APPROVED) {
      return [this.isLocked ? Utility.UnlockAccount : Utility.LockAccount, Utility.TerminateAccount];
    }
    return [];
  }

  getOtherUtilities(): Utility[] {
    if (this.accountInfo && this.partnerData.status === MPOnboardingStatus.APPROVED) {
      return this.isLocked
        ? [Utility.Notification]
        : [Utility.SetDefaultSourceOfFund, Utility.ManageListSOF, Utility.Notification];
    }
    return [];
  }

  getFeeDescription(): string {
    return 'Phát sinh khi có giao dịch trong kỳ sao kê hoặc còn dư nợ trước ngày phát sinh sao kê';
  }
}

export class LotteAccountScreenUIConfig extends AccountScreenUIConfig {
  getAccountInfoItems(): AccountInfoItem[] {
    if (this.accountInfo && this.partnerData.status === MPOnboardingStatus.APPROVED) {
      return [
        {
          type: AccountInfoType.Contract,
        },
        {
          type: AccountInfoType.ProductInfo,
        },
        {
          type: AccountInfoType.StatementDate,
          value: this.accountInfo.statement_date,
        },
        {
          type: AccountInfoType.DueDate,
          value: this.accountInfo.statement_grace_full_date,
        },
        {
          type: AccountInfoType.Fee,
          value: generateLotteFreeTrialData(this.partnerData.fee).fee,
        },
      ];
    }
    return [
      {
        type: AccountInfoType.ProductInfo,
      },
      {
        type: AccountInfoType.StatementDate,
        value: '-',
      },
      {
        type: AccountInfoType.DueDate,
        value: '-',
      },
    ];
  }

  getAccountUtilities(): Utility[] {
    if (this.accountInfo && this.partnerData.status === MPOnboardingStatus.APPROVED) {
      return [this.isLocked ? Utility.UnlockAccount : Utility.LockAccount];
    }
    return [];
  }

  getOtherUtilities(): Utility[] {
    if (this.accountInfo && this.partnerData.status === MPOnboardingStatus.APPROVED) {
      return [Utility.Notification];
    }
    return [];
  }

  getFeeDescription(): string {
    const { feeDescription } = generateLotteFreeTrialData(this.partnerData.fee);
    return feeDescription;
  }
}

function generateLotteFreeTrialData(defaultFee: string) {
  const isInFreeTrialWhiteList = store.getState().abTesting[ExperimentName.LOTTE_FREE_TRIAL]?.toLowerCase() === ABTestingGroup.Variation_1.toLowerCase();
  const accountInfo = store.getState().accountInfo[PartnerCode.LOTTE];
  const freeTrialResult = checkFreeTrialAvailable(accountInfo);
  if (isInFreeTrialWhiteList && freeTrialResult.available) {
    const { benefit } = freeTrialResult;
    const remainingDays = Math.ceil(differenceInMinutes(new Date(benefit.endTime), new Date()) / (24 * 60));
    if (remainingDays > 0) {
      return {
        fee: 'Miễn phí',
        feeDescription: `Bạn có ${remainingDays} ngày miễn phí dịch vụ.`,
      };
    }
  }
  return {
    fee: defaultFee,
    feeDescription: 'Phát sinh khi có giao dịch trong kỳ sao kê hoặc còn dư nợ trước ngày phát sinh sao kê',
  }
}

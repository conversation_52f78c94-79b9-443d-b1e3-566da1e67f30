import { PartnerCode } from 'bnpl-shared/src/constants';
import {
  AccountScreenUIConfig,
  AccountUIConfigParams,
  CIMBAccountScreenUIConfig,
  LotteAccountScreenUIConfig,
} from './AccountScreenUIConfig';

export * from './AccountScreenUIConfig';

export const AccountScreenUIConfigFactory = {
  get(params: AccountUIConfigParams): AccountScreenUIConfig {
    switch (params.partnerData.partner_code) {
      case PartnerCode.CIMB:
        return new CIMBAccountScreenUIConfig(params);
      case PartnerCode.LOTTE:
        return new LotteAccountScreenUIConfig(params);
      case PartnerCode.MINI_BNPL:
        return new LotteAccountScreenUIConfig(params);
    }
  },
};

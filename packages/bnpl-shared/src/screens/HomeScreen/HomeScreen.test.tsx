import HomeScreen from 'bnpl-shared/src/screens/HomeScreen/HomeScreen';
import { renderWithRedux, renderReduxWithNavigationWrapper } from 'bnpl-shared/src/jest/renderWithRedux';
import React from 'react';
import { store } from 'bnpl-shared/src/redux/store';
import {
  setBindingStatus,
  setOnboardingId,
  setOnboardingStatus,
  setRiskInfo,
} from 'bnpl-shared/src/redux/bindingReducer';
import {
  ABTestingGroup,
  BindingStatus,
  ExperimentName,
  OnboardingStatus,
  ResourceState,
  RiskAction,
} from 'bnpl-shared/src/types';
import { FAKE_NAVIGATION } from 'bnpl-shared/src/jest/constants';
import { waitFor } from '@testing-library/react-native';
// import { getSummaryStatementApiBuilder } from 'bnpl-shared/src/api/__mocks__/getSummaryStatementApi';
// import { ScreenKey } from 'bnpl-shared/src/constants';
import { setSummaryUserBalance } from 'bnpl-shared/src/redux/userBalanceReducer';
import { getCurrentDate } from 'bnpl-shared/src/utils';
import { asMock } from 'bnpl-shared/src/jest/utils';
import { getTransactionHistoryApi } from 'bnpl-shared/src/api/getTransactionHistoryApi';
import { getTransactionHistoryApiBuilder } from 'bnpl-shared/src/api/__mocks__/getTransactionHistory';
import * as spyRemoteConfigHook from 'bnpl-shared/src/remote_configs/useRemoteConfigs';
import { setUtmCampaignAction } from 'bnpl-shared/src/redux/utmReducer';
import { setAbTestingResult } from 'bnpl-shared/src/redux/abTestingReducer';
import * as spyABTestHook from 'bnpl-shared/src/hooks/useABTesting';
import { ToastType } from 'bnpl-shared/src/redux/appToastReducer';
import { setAutoRepayment } from 'bnpl-shared/src/redux/autoRepaymentReducer';
import InfoModal from 'bnpl-shared/src/components/InfoModal';
import { infoModalRef, InfoModalService } from 'bnpl-shared/src/services';
import {getTransHisByAccountIdApi} from "bnpl-shared/src/api/getTransHisByAccountIdApi";
import {setChosenPartner, setPartnerData} from "bnpl-shared/src/redux/multipartnerReducer";
import {PartnerCode} from "bnpl-shared/src/constants";
import {RoutingInfoMapper} from "bnpl-shared/src/features/multipartner_integration/helpers";
import {FakePartnerCIMB} from "bnpl-shared/src/jest/fakeData";

const mockGetConfig = jest.fn();

jest.spyOn(spyRemoteConfigHook, 'useRemoteConfigs').mockReturnValue({
  fetchConfigs: jest.fn(),
  getConfig: mockGetConfig,
  getConfigWithType: jest.fn(),
});

jest.spyOn(spyABTestHook, 'useABTesting').mockReturnValue({
  fetchABTestResult: jest.fn(),
  isInWhiteList: jest.fn().mockReturnValue(true),
  isInWhiteListRealtime: jest.fn().mockReturnValue(true),
  getABTestResultByKey: jest.fn(),
});

const Subject = () => {
  return (
    <>
      <HomeScreen navigation={FAKE_NAVIGATION} />
      <InfoModal ref={infoModalRef} />
    </>
  );
};

jest.mock('bnpl-shared/src/features/renew_overdraft/helpers', () => {
  const originalModule = jest.requireActual('bnpl-shared/src/features/renew_overdraft/helpers');
  return {
    __esModule: true,
    ...originalModule,
    useRenewOverDraft: () => ({
      launchRenewSurvey: jest.fn(),
      getRenewInfo: jest.fn().mockReturnValue(false),
      getRenewResourceState: jest.fn(),
      fetchRenewStatus: jest.fn(),
    }),
  };
});

describe(HomeScreen.name, () => {
  afterEach(() => {
    (FAKE_NAVIGATION.getParam as jest.Mock).mockReset();
    InfoModalService.hideModal();
  });

  // it('handle repayment widget action when statement is available', async () => {
  //   store.dispatch(setOutstandingBalance(200000));
  //   (getSummaryStatementApi as jest.Mock).mockResolvedValue(getSummaryStatementApiBuilder.build('before_due'));
  //   const { getByTestId, queryByTestId } = renderWithRedux(<HomeScreen navigation={FAKE_NAVIGATION} />);
  //   await waitFor(() => expect(queryByTestId('button-repayment-available')).toBeTruthy(), { timeout: 5000 });
  //   fireEvent.press(getByTestId('button-repayment-available'));
  //   await waitFor(() => expect(FAKE_NAVIGATION.navigate).toHaveBeenCalledWith(ScreenKey.Repayment));
  // });

  it('renders WAITING_APPROVE => REJECTED', () => {
    store.dispatch(setOnboardingId('onboardingId'));
    store.dispatch(setOnboardingStatus(OnboardingStatus.WAITING_APPROVE));
    const { queryByTestId } = renderReduxWithNavigationWrapper(<HomeScreen navigation={FAKE_NAVIGATION} />);
    expect(queryByTestId('home-screen-ui')).toBeTruthy();
    store.dispatch(setOnboardingStatus(OnboardingStatus.REJECTED));
    expect(queryByTestId('rejected-toast')).toBeTruthy();
  });
  it('renders  OnboardingStatus.WAITING_APPROVE', () => {
    store.dispatch(setOnboardingStatus(OnboardingStatus.WAITING_APPROVE));
    const { queryByTestId } = renderReduxWithNavigationWrapper(<HomeScreen navigation={FAKE_NAVIGATION} />);
    expect(queryByTestId('home-screen-ui')).toBeTruthy();
    expect(queryByTestId('rejected-toast')).toBeNull();
    expect(queryByTestId('approve-toast')).toBeNull();
  });
  it('renders WAITING_APPROVE => APPROVE', async () => {
    store.dispatch(setOnboardingId('onboardingId'));
    store.dispatch(setOnboardingStatus(OnboardingStatus.WAITING_APPROVE));
    const { queryByTestId, queryByText } = renderReduxWithNavigationWrapper(<Subject />);
    expect(queryByTestId('home-screen-ui')).toBeTruthy();
    store.dispatch(setOnboardingStatus(OnboardingStatus.APPROVE));
    expect(queryByTestId('approve-toast')).toBeTruthy();
    await waitFor(() => {
      expect(
        queryByText('Chúc mừng bạn đã nhận được hạn mức trả sau. Sử dụng để chi tiêu bất kỳ lúc nào bạn nhé'),
      ).toBeTruthy();
    });
    // fireEvent.press(getByTestId('main-action-button'));
    // await waitFor(() => expect(queryByTestId('services-modal')).toBeTruthy());
  });

  it('show risk info modal as expected', () => {
    store.dispatch(setRiskInfo({ is_risk: true, action: RiskAction.FACE_CHALLENGE, action_url: 'test_url' }));
    const { getByTestId } = renderReduxWithNavigationWrapper(<HomeScreen navigation={FAKE_NAVIGATION} />);
    expect(getByTestId('liveness-modal')).toBeTruthy();
  });

  it('show update delay time tooltip as expected', async () => {
    store.dispatch(
        setPartnerData({
          partnerCode: PartnerCode.CIMB,
          data: new RoutingInfoMapper(FakePartnerCIMB).toPartnerData(),
        }),
    );
    store.dispatch(setChosenPartner(PartnerCode.CIMB));
    mockGetConfig.mockImplementation(arg => {
      if (arg === 'available_limit_delay_in_minute') {
        return 5;
      }
    });
    (getCurrentDate as jest.Mock).mockReturnValue(new Date('2022-05-27T15:19:00+07:00'));
    asMock(getTransHisByAccountIdApi).mockResolvedValueOnce(getTransactionHistoryApiBuilder.build('success').data);
    store.dispatch(
      setSummaryUserBalance({
        state: ResourceState.READY,
        data: {
          total_limit: 5000000,
          available_balance: 4000000,
          over_repayment_balance: 250000,
          outstanding_balance: 0,
          user_service_fee: 0,
        },
      }),
    );
    const { queryByText, getByTestId } = renderReduxWithNavigationWrapper(<HomeScreen navigation={FAKE_NAVIGATION} />);
    //trigger onLayout props callback to setup tooltip anchor view
    await waitFor(() =>
      getByTestId('available-balance-container').props.onLayout({ nativeEvent: { layout: { width: 100 } } }),
    );
    await waitFor(() => expect(queryByText('Cập nhật sau 5 phút')).toBeTruthy());
  });

  describe('deeplink auto repay binding', () => {
    it('show binding modal when user have not enable feature', async () => {
      store.dispatch(setBindingStatus(BindingStatus.open));
      store.dispatch(setAutoRepayment({ state: ResourceState.READY, data: false }));
      store.dispatch(setUtmCampaignAction('auto_repay_binding'));
      store.dispatch(
        setAbTestingResult({ experiment_key: ExperimentName.AUTO_REPAYMENT, result: ABTestingGroup.Variation_1 }),
      );
      const { queryByTestId } = renderReduxWithNavigationWrapper(
        <>
          <HomeScreen navigation={FAKE_NAVIGATION} />
          <InfoModal ref={infoModalRef} />
        </>,
      );
      await waitFor(() => expect(queryByTestId('auto-repay-binding')).toBeTruthy());
    });

    it('show toast when user already have enabled feature', async () => {
      store.dispatch(setBindingStatus(BindingStatus.open));
      store.dispatch(setAutoRepayment({ state: ResourceState.READY, data: true }));
      store.dispatch(setUtmCampaignAction('auto_repay_binding'));
      store.dispatch(
        setAbTestingResult({ experiment_key: ExperimentName.AUTO_REPAYMENT, result: ABTestingGroup.Variation_1 }),
      );
      renderReduxWithNavigationWrapper(<HomeScreen navigation={FAKE_NAVIGATION} />);
      expect(store.getState().appToast.toast).toEqual({
        message: 'Đã bật tự động thanh toán',
        type: ToastType.SUCCESS,
        duration: 5000,
      });
    });
  });
});

import { Spacer } from 'bnpl-shared/src/components/Spacer';
import { StyleSheet } from 'bnpl-shared/src/shared/styles/StyleSheet';
import { Colors } from 'bnpl-shared/src/shared/styles/StyleUtils';
import { getAppInfo, launchDeepLink, launchInAppWebView } from 'bnpl-shared/src/shared/ZaloPayModules';
import React, { FC, useContext, useMemo, useRef, useState } from 'react';
import { NativeScrollEvent, NativeSyntheticEvent, Platform, ScrollView, View } from 'react-native';
import { BalanceSummarySection } from './components/BalanceSummarySection';
import { FooterSection } from './components/FooterSection';
import { HelpSection } from './components/HelpSection';
import { RecentTransactionSection } from './components/RecentTransactionSection';
import { AppColors, PartnerCode } from 'bnpl-shared/src/constants';
import { UtilitySection } from './components/UtilitySection';
import { ServicesSection } from 'bnpl-shared/src/screens/HomeScreen/components/ServicesSection';
import { useHomePreviewController } from 'bnpl-shared/src/utils/useHomePreviewController';
import { AutoRepaymentEntryPoint } from 'bnpl-shared/src/features/auto_repayment';
import { AdsInventoryId, TransactionType, ZlpPlatform } from 'bnpl-shared/src/types';
import { useRemoteConfigs } from 'bnpl-shared/src/remote_configs/useRemoteConfigs';
import { throttle, toNumber } from 'lodash';
import { OverRepaymentBalanceModal } from 'bnpl-shared/src/screens/HomeScreen/components/OverRepaymentBalanceModal';
import { FeeBanner } from 'bnpl-shared/src/components/FeeBanner';
import { HeaderSlider } from 'bnpl-shared/src/screens/HomeScreen/components/HeaderSlider';
import { getCurrentDate } from 'bnpl-shared/src/utils';
import { useNavigation } from 'bnpl-shared/src/shared/nav';
import { NavigationContext } from 'bnpl-shared/src/utils/NavigationContext';
import { isZLPDeeplink } from 'bnpl-shared/src/shared/utils/isZLPDeeplink';
import { AppRefreshControl, RepaymentWidget } from 'bnpl-shared/src/components';
import PromotionSection from 'bnpl-shared/src/features/promotion_section';
import { ExternalComponentIntegration } from 'bnpl-shared/src/screens/HomeScreen/components/ExternalComponentIntegration';
import { CIMBInfoBanner } from 'bnpl-shared/src/components/CIMBInfoBanner';

type HomeScreenUiProps = {
  onPullToRefresh?: () => Promise<void>;
  onScroll?: (e: NativeSyntheticEvent<NativeScrollEvent>) => void;
  onAction?: (actionName: string) => void;
  autoRepaymentEnable?: boolean;
};

export const HomeScreenUI: FC<HomeScreenUiProps> = ({
  onPullToRefresh,
  onScroll,
  onAction,
  autoRepaymentEnable = false,
}) => {
  const navigation = useNavigation({ navigation: useContext(NavigationContext) });
  const { isPreviewMode } = useHomePreviewController(PartnerCode.CIMB);
  const { getConfig } = useRemoteConfigs();
  const currentScrollPercent = useRef<number>(0);
  const [obTooltipVisisble, setObTooltipVisisble] = useState(0); // 0 || > 1 : not show, 1: show,
  const [overRepaymentModalVisisble, setOverRepaymentModalVisisble] = useState(false);
  const [refreshing, setRefreshing] = useState(false);
  const handlePullToRefresh = async () => {
    if (refreshing) {
      return;
    }

    try {
      setRefreshing(true);
      await onPullToRefresh?.();
    } finally {
      setRefreshing(false);
    }
  };

  const handleScroll = useMemo(() => {
    return throttle(
      (height: number, offset: number) => {
        currentScrollPercent.current = (offset / height) * 100;
      },
      300,
      { leading: true },
    );
  }, []);

  const handleBannerPress = async (url: string) => {
    const appInfo = await getAppInfo();
    if (appInfo.platform === ZlpPlatform.ZPI) {
      window.zlpSdk?.Navigator.navigateTo?.({ url });
    } else {
      if (isZLPDeeplink(url)) {
        launchDeepLink(url);
      } else {
        launchInAppWebView(url);
      }
    }
  };

  return (
    <View testID="home-screen-ui" style={styles.root}>
      <ScrollView
        style={styles.container}
        onScroll={e => {
          onScroll?.(e);
          const height = e.nativeEvent.contentSize.height - e.nativeEvent.layoutMeasurement.height;
          const offset = e.nativeEvent.contentOffset.y;
          handleScroll(height, offset);
        }}
        showsVerticalScrollIndicator={false}
        contentContainerStyle={styles.contentContainer}
        scrollEventThrottle={120}
        refreshControl={<AppRefreshControl refreshing={refreshing} onRefresh={handlePullToRefresh} />}>
        <>
          <HeaderSlider inventoryId={AdsInventoryId.HEADER_SLIDER} onBannerPress={handleBannerPress} />
          <View style={{ ...styles.sectionWrapper, marginBottom: 0, marginHorizontal: 16, marginTop: 160 }}>
            <BalanceSummarySection
              isPreviewMode={isPreviewMode}
              onTooltipPress={() => setObTooltipVisisble(0)}
              onRequestToogleOverRepaymentModal={() => setOverRepaymentModalVisisble(true)}
              showOutStandingBalanceTooltip={obTooltipVisisble !== 0}
            />

            <RepaymentWidget partnerCode={PartnerCode.CIMB} onAction={onAction} isPreviewMode={isPreviewMode} />

            {autoRepaymentEnable && !isPreviewMode && (
              <AutoRepaymentEntryPoint
                navigation={navigation}
                entryPoint={isPreviewMode ? 'wait_approve_home' : 'home'}
                containerStyle={styles.autoRepayController}
              />
            )}

            <FeeBanner style={{ marginBottom: 16 }} />
            <CIMBInfoBanner style={{ marginBottom: 16 }} />

          </View>

          {!isPreviewMode && Platform.OS === 'web' ? <ExternalComponentIntegration /> : null}

          <PromotionSection onItemPress={handleBannerPress} />

          <View style={styles.body}>
            {!isPreviewMode && (
              <RecentTransactionSection
                onDataReady={transactions => {
                  const availableLimitDelayInMinute = toNumber(getConfig('available_limit_delay_in_minute') || 0);
                  if (transactions.length && availableLimitDelayInMinute > 0) {
                    let hasRecentRepayTransaction =
                      transactions.find(t => {
                        const updateTimeIsBelowDelayLimit =
                          getCurrentDate().getTime() - new Date(t.updated_at).getTime() <
                          availableLimitDelayInMinute * 60 * 1000;
                        return t.type === TransactionType.REPAYMENT && updateTimeIsBelowDelayLimit;
                      }) !== undefined;
                    if (hasRecentRepayTransaction && currentScrollPercent.current < 20) {
                      setObTooltipVisisble(prevState => prevState + 1);
                    }
                  }
                }}
              />
            )}
            <Spacer height={8} />
            <ServicesSection />
            <Spacer height={8} />

            <UtilitySection />
            <Spacer height={8} />

            <HelpSection />
            <FooterSection partnerCode={PartnerCode.CIMB} />
          </View>
        </>
      </ScrollView>
      <OverRepaymentBalanceModal
        onRequestOpenHistory={() => {
          setOverRepaymentModalVisisble(false);
          onAction?.('navigate-transaction-history');
        }}
        onRequestClose={() => setOverRepaymentModalVisisble(false)}
        visible={overRepaymentModalVisisble}
      />
    </View>
  );
};

const styles = StyleSheet.create({
  pattern: {
    height: 97,
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
  },
  body: {
    backgroundColor: Colors.background2,
    overflow: 'hidden',
    paddingHorizontal: 16,
  },
  root: {
    flex: 1,
    backgroundColor: Colors.background2,
  },
  container: {},
  contentContainer: {
    paddingBottom: 0,
  },
  moneyIcon: {
    width: 16,
    height: 16,
    marginRight: 4,
  },
  sectionWidget: {
    marginBottom: 16,
  },
  sectionWrapper: {
    margin: 16,
  },
  sectionGutter: {
    marginBottom: 16,
  },
  autoRepayController: {
    backgroundColor: AppColors.background,
    borderRadius: 8,
    marginBottom: 16,
  },
});

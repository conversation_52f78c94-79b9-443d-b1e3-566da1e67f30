import React, { memo, ReactNode, useContext } from 'react';
import { View, TouchableOpacity, ViewStyle } from 'react-native';
import { AppImage, AppText } from 'bnpl-shared/src/shared/react-native-customized';
import { StyleSheet } from 'bnpl-shared/src/shared/styles/StyleSheet';
import { TwoSideView } from 'bnpl-shared/src/shared/TwoSideView/TwoSideView';
import { SectionContainer } from '../SectionContainer';
import { size } from 'bnpl-shared/src/shared/styles/StyleUtils';
import { Divider } from 'bnpl-shared/src/components/Divider';
import { NavigationContext } from 'bnpl-shared/src/utils/NavigationContext';
import { AppColors, PartnerCode, ScreenKey } from 'bnpl-shared/src/constants';
import { publishTracking, Event } from 'bnpl-shared/src/utils/tracking';
import { ChevronIcon } from 'bnpl-shared/src/shared/ChevronIcon';
import { GeneralInfoIc24, GeneralQuestionIc24, GeneralSupportIc24 } from '@zpi/looknfeel-icons';

export const HelpSection = memo(() => {
  const navigation = useContext(NavigationContext);
  const onPressFaq = () => {
    publishTracking?.({ source: 'faq', event: Event.CTA_CLICK });
    navigation.navigate(ScreenKey.Faq, { partnerCode: PartnerCode.CIMB });
  };

  const onPressInfo = () => {
    publishTracking?.({ source: 'product_info', event: Event.CTA_CLICK });
    navigation.navigate(ScreenKey.ProductInfo, { partnerCode: PartnerCode.CIMB });
  };

  const onPressSupport = () => {
    publishTracking?.({ source: 'user_manual', event: Event.CTA_CLICK });
    navigation.navigate(ScreenKey.UserGuide);
  };

  return (
    <SectionContainer title="Bạn cần hỗ trợ?" titleGutter={false}>
      <HelpItem testId="info-help" onPress={onPressInfo} icon={<GeneralInfoIc24 color={AppColors.dark200} />} title="Thông tin sản phẩm" />
      <Divider />
      <HelpItem testId="faq-help" onPress={onPressFaq} icon={<GeneralQuestionIc24 color={AppColors.dark200} />} title="Câu hỏi thường gặp" />
      <Divider />
      <HelpItem
        testId="support-help"
        onPress={onPressSupport}
        icon={<GeneralSupportIc24 color={AppColors.dark200} />}
        title="Hướng dẫn sử dụng"
        style={styles.helpItemLast}
      />
    </SectionContainer>
  );
});

const styles = StyleSheet.create({
  helpItemLast: {
    paddingBottom: 0,
  },
});

const HelpItem = ({
  icon,
  title,
  style,
  onPress,
  testId,
}: {
  icon?: ReactNode;
  title?: string;
  style?: ViewStyle;
  onPress?: () => void;
  testId?: string;
}) => {
  return (
    <TouchableOpacity testID={testId} activeOpacity={0.8} style={[helpItemStyles.root, style]} onPress={onPress}>
      <TwoSideView
        left={
          <View style={helpItemStyles.left}>
            <View style={helpItemStyles.icon}>
              {icon}
            </View>
            <AppText size={14}>{title}</AppText>
          </View>
        }
        right={<ChevronIcon tintColor={AppColors.dark200} direction={'right'} />}
      />
    </TouchableOpacity>
  );
};

const helpItemStyles = StyleSheet.create({
  root: {
    paddingVertical: 16,
  },
  left: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  icon: {
    marginRight: 12,
  },
});

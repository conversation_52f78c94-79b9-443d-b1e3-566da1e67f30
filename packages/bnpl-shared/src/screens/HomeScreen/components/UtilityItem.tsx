import React, { FC, ReactNode } from 'react';
import { TouchableOpacity, View } from 'react-native';
import { StyleSheet } from 'bnpl-shared/src/shared/styles/StyleSheet';
import { AppImage, AppText } from 'bnpl-shared/src/shared/react-native-customized';
import { size } from 'bnpl-shared/src/shared/styles/StyleUtils';
import { throttle } from 'lodash';
import { ImageSource } from 'bnpl-shared/src/shared/types';

export const UtilityItem: FC<{
  icon?: ImageSource | ReactNode;
  children: string;
  onPress?: () => void;
  testId?: string;
  disable?: boolean;
}> = ({ icon, children, onPress, testId, disable = false }) => {
  const debouncedPress = throttle(
    () => {
      if (!disable) {
        onPress?.();
      }
    },
    1000,
    { leading: true, trailing: false },
  );

  return (
    <TouchableOpacity
      disabled={disable}
      testID={testId}
      activeOpacity={0.5}
      style={styles.utility}
      onPress={debouncedPress}>
      {icon && (
        React.isValidElement(icon) 
          ? <View style={[styles.icon, { opacity: disable ? 0.2 : 1 }]}>{icon}</View>
          : <AppImage source={icon as ImageSource} {...size(32)} style={[styles.icon, { opacity: disable ? 0.2 : 1 }]} />
      )}
      <View style={styles.textContainer}>
        <AppText size={13} numberOfLines={2} style={[styles.text, { opacity: disable ? 0.2 : 1 }]}>
          {children}
        </AppText>
      </View>
    </TouchableOpacity>
  );
};

const styles = StyleSheet.create({
  utility: {
    width: '100%',
    alignSelf: 'center',
    alignItems: 'center',
    marginBottom: 12,
  },
  textContainer: {
    paddingBottom: 40,
    width: '100%',
  },
  icon: {
    marginBottom: 4,
  },
  text: {
    textAlign: 'center',
    position: 'absolute',
    top: 8,
    width: '100%',
  },
});

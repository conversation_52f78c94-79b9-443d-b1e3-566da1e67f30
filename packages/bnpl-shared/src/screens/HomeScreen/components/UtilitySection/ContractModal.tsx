import React from 'react';
import { windowHeight } from 'bnpl-shared/src/shared/styles/StyleUtils';
import { StyleSheet } from 'bnpl-shared/src/shared/styles/StyleSheet';
import { WebView } from 'react-native-webview';
import { ScrollView, View } from 'react-native';
import { AppModal } from 'bnpl-shared/src/local/AppModal';
import { BottomSheetLayout } from 'bnpl-shared/src/shared/BottomSheet/BottomSheetLayout';
import { FileViewer } from 'bnpl-shared/src/components/FileViewer';
import { useNavigationContext } from 'bnpl-shared/src/components/NavigationWrapper';

type Props = {
  contractUrl: string;
  password?: string;
  visible: boolean;
  onRequestClose?: () => void;
  useFileViewer?: boolean;
};

const ContractModal = ({ contractUrl, visible, onRequestClose, useFileViewer }: Props) => {
  const { state: navigationState } = useNavigationContext();
  return (
    <AppModal transparent visible={visible} onRequestClose={onRequestClose}>
      <BottomSheetLayout
        title={'Xem hợp đồng'}
        onRequestClose={onRequestClose}
        content={
          <View style={[styles.bodyStyle, navigationState?.fullScreenData?.fullScreen && styles.bodyFullScreen]}>
            {!useFileViewer ? (
              <WebView source={{ uri: contractUrl }} />
            ) : (
              <ScrollView showsVerticalScrollIndicator={false}>
                <FileViewer url={contractUrl} type={'pdf'} />
              </ScrollView>
            )}
          </View>
        }
      />
    </AppModal>
  );
};

const styles = StyleSheet.create({
  bodyStyle: {
    height: windowHeight * 0.75,
  },
  bodyFullScreen: {
    height: windowHeight * 0.65,
  },
});

export default ContractModal;

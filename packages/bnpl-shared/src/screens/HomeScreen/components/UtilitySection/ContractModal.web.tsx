import React from 'react';
import { StyleSheet } from 'bnpl-shared/src/shared/styles/StyleSheet';
import { PdfFileWebViewer } from 'bnpl-shared/src/components/FileViewer/PdfFileWebViewer';
import { ScrollView, } from 'react-native';
import { BottomSheetLayout } from 'bnpl-shared/src/shared/BottomSheet/BottomSheetLayout';
import { AppModal } from 'bnpl-shared/src/local/AppModal';
import { FileViewer } from 'bnpl-shared/src/components/FileViewer';
import { windowHeight } from 'bnpl-shared/src/shared/styles/StyleUtils';
import { useNavigationContext } from 'bnpl-shared/src/components/NavigationWrapper';

type Props = {
  contractUrl: string;
  password?: any;
  visible: boolean;
  onRequestClose?: () => void;
  useFileViewer?: boolean;
};

const ContractModal = ({ useFileViewer, contractUrl, password, onRequestClose, visible }: Props) => {
  const { state: navigationState } = useNavigationContext();
  return (
    <AppModal transparent visible={visible} onRequestClose={onRequestClose}>
      <BottomSheetLayout
        title={'Xem hợp đồng'}
        onRequestClose={onRequestClose}
        content={
          <ScrollView style={[styles.bodyStyle, navigationState?.fullScreenData?.fullScreen && styles.bodyFullScreen]}>
            {useFileViewer ? (
              <FileViewer url={contractUrl} type={'pdf'} />
            ) : (
              <PdfFileWebViewer options={{ password }} fileUrl={contractUrl} />
            )}
          </ScrollView>
        }
      />
    </AppModal>
  );
};

const styles = StyleSheet.create({
  bodyStyle: {
    height: windowHeight * 0.75,
  },
  bodyFullScreen: {
    height: windowHeight * 0.65,
  },
});

export default ContractModal;

import React from 'react';
import { UtilitySection } from './index';
import { renderReduxWithNavigationWrapper } from 'bnpl-shared/src/jest/renderWithRedux';
import { store } from 'bnpl-shared/src/redux/store';
import { setBindingStatus } from 'bnpl-shared/src/redux/bindingReducer';
import { fireEvent } from '@testing-library/react-native';
import { BindingStatus } from 'bnpl-shared/src/types';
import { FAKE_NAVIGATION } from 'bnpl-shared/src/jest/constants';
import { PartnerCode, ScreenKey } from 'bnpl-shared/src/constants';
import { NavigationContext } from 'bnpl-shared/src/utils/NavigationContext';
import { launchSofsDeeplink } from 'bnpl-shared/src/utils/launchSofsDeeplink';
import { setChosenPartner, setPartnerData } from 'bnpl-shared/src/redux/multipartnerReducer';
import { RoutingInfoMapper } from 'bnpl-shared/src/features/multipartner_integration/helpers';
import { FakePartnerCIMB } from 'bnpl-shared/src/jest/fakeData';

const mockLaunchSOFPriorityFlow = jest.fn();
const mockShouldLaunchSOFPriorityFlow = jest.fn();

jest.mock('bnpl-shared/src/utils/launchSofsDeeplink', () => ({
  launchSofsDeeplink: jest.fn(),
}));

jest.mock('bnpl-shared/src/features/config_utilities', () => ({
  useUtilityPrioritySOF: () => ({
    launchSOFPriorityFlow: mockLaunchSOFPriorityFlow,
    shouldLaunchSOFPriorityFlow: mockShouldLaunchSOFPriorityFlow,
  }),
}));

const mockHandleViewContract = jest.fn();

jest.mock('bnpl-shared/src/utils/view_contract', () => ({
  useViewContract: () => ({
    handleViewContract: mockHandleViewContract,
  }),
}));

describe(UtilitySection.name, () => {
  beforeAll(() => {
    store.dispatch(
      setPartnerData({
        partnerCode: PartnerCode.CIMB,
        data: new RoutingInfoMapper(FakePartnerCIMB).toPartnerData(),
      }),
    );
    store.dispatch(setChosenPartner(PartnerCode.CIMB));
  });

  it('render utility section', () => {
    const { queryByText } = renderReduxWithNavigationWrapper(<UtilitySection />);
    expect(queryByText('Tiện ích')).toBeFalsy();
  });

  it('not show notification, priority-sof, manage-sof only onboarding status is not approved', () => {
    store.dispatch(setBindingStatus(BindingStatus.processing));
    const { queryByTestId } = renderReduxWithNavigationWrapper(<UtilitySection />);
    expect(queryByTestId('priority-sof-icon')).toBeFalsy();
    expect(queryByTestId('manage-sof-icon')).toBeFalsy();
    expect(queryByTestId('notification-icon')).toBeFalsy();
  });

  it('not show priority-sof, manage-sof if binding status is locked', () => {
    store.dispatch(setBindingStatus(BindingStatus.locked));
    const { queryByTestId } = renderReduxWithNavigationWrapper(<UtilitySection />);
    expect(queryByTestId('priority-sof-icon')).toBeFalsy();
    expect(queryByTestId('manage-sof-icon')).toBeFalsy();
    expect(queryByTestId('notification-icon')).toBeTruthy();
  });

  it('verify contract info', () => {
    store.dispatch(setBindingStatus(BindingStatus.open));
    const { getByTestId } = renderReduxWithNavigationWrapper(<UtilitySection />);
    fireEvent.press(getByTestId('contract-info'));
    expect(mockHandleViewContract).toHaveBeenCalled();
  });

  describe('verify notification', () => {
    it('render only when onboarding status is approved', () => {
      store.dispatch(setBindingStatus(BindingStatus.open));
      const { queryByTestId } = renderReduxWithNavigationWrapper(<UtilitySection />);
      expect(queryByTestId('notification-icon')).toBeTruthy();
    });

    it('press navigate as expected', () => {
      store.dispatch(setBindingStatus(BindingStatus.open));
      const { getByTestId } = renderReduxWithNavigationWrapper(
        <UtilitySection />
      );
      fireEvent.press(getByTestId('notification-icon'));
      expect(FAKE_NAVIGATION.navigate).toBeCalledWith(ScreenKey.Notification);
    });
  });

  describe('verify priority sof', () => {
    it('render only when onboarding status is approved', () => {
      store.dispatch(setBindingStatus(BindingStatus.open));
      const { queryByTestId } = renderReduxWithNavigationWrapper(<UtilitySection />);
      expect(queryByTestId('priority-sof-icon')).toBeTruthy();
    });

    it('press show modal suggest priority sof as expected', () => {
      store.dispatch(setBindingStatus(BindingStatus.open));
      const { getByTestId } = renderReduxWithNavigationWrapper(<UtilitySection />);
      fireEvent.press(getByTestId('priority-sof-icon'));
      expect(mockLaunchSOFPriorityFlow).toHaveBeenCalled();
    });

    it('show suggest priority sof modal when utility section is mounted', () => {
      store.dispatch(setBindingStatus(BindingStatus.open));
      mockShouldLaunchSOFPriorityFlow.mockResolvedValue(true);
      renderReduxWithNavigationWrapper(<UtilitySection />);
      expect(mockLaunchSOFPriorityFlow).toHaveBeenCalled();
    });
  });

  describe('verify manage sof', () => {
    it('render only when onboarding status is approved', () => {
      store.dispatch(setBindingStatus(BindingStatus.open));
      const { queryByTestId } = renderReduxWithNavigationWrapper(<UtilitySection />);
      expect(queryByTestId('manage-sof-icon')).toBeTruthy();
    });

    it('press navigate to manage sof as expected', () => {
      store.dispatch(setBindingStatus(BindingStatus.open));
      const { getByTestId } = renderReduxWithNavigationWrapper(<UtilitySection />);
      fireEvent.press(getByTestId('manage-sof-icon'));
      expect(launchSofsDeeplink).toHaveBeenCalled();
    });
  });
});

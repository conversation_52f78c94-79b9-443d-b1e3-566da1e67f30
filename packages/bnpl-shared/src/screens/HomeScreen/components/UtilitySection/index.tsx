import React, { useContext, useEffect, useState } from 'react';
import { Row } from 'bnpl-shared/src/components/Row';
import { AppText } from 'bnpl-shared/src/shared/react-native-customized';
import { StyleSheet } from 'bnpl-shared/src/shared/styles/StyleSheet';
import { Colors, StyleUtils } from 'bnpl-shared/src/shared/styles/StyleUtils';
import { useAppSelector } from 'bnpl-shared/src/redux/store';
import { Platform, View } from 'react-native';
import { SectionContainer } from '../SectionContainer';
import { UtilityItem } from '../UtilityItem';
import { BindingStatus } from 'bnpl-shared/src/types';
import { images } from 'bnpl-shared/src/res';
import { useNavigation } from 'bnpl-shared/src/shared/navigation/useNavigation';
import { NavigationContext } from 'bnpl-shared/src/utils/NavigationContext';
import { AppColors, PartnerCode, ScreenKey } from 'bnpl-shared/src/constants';
import { launchSofsDeeplink } from 'bnpl-shared/src/utils/launchSofsDeeplink';
import { useUtilityPrioritySOF } from 'bnpl-shared/src/features/config_utilities/';
import { UtilitySectionTracking } from 'bnpl-shared/src/screens/HomeScreen/components/UtilitySection/tracking';
import { useViewContract } from 'bnpl-shared/src/utils/view_contract';
import ContractModal from 'bnpl-shared/src/screens/HomeScreen/components/UtilitySection/ContractModal';
import { usePartnerData } from 'bnpl-shared/src/features/multipartner_integration/helpers';
import { ServiceFinanceInstallmentSecondary } from '@zpi/looknfeel-icons';
import { launchDeeplink } from 'bnpl-shared/src/lib/ZalopaySDK/launchDeeplink';

export const UtilitySection = () => {
  const { handleViewContract, contractInfo, contractVisible, setContractVisible } = useViewContract(PartnerCode.CIMB);
  const { launchSOFPriorityFlow, shouldLaunchSOFPriorityFlow } = useUtilityPrioritySOF();
  const bindingStatus = useAppSelector(state => state.binding.bindingStatus);
  const navigation = useNavigation({ navigation: useContext(NavigationContext) });
  const [disablePrioritySOFBtn, setDisablePrioritySOFBtn] = useState(false);
  const { getChosenPartner } = usePartnerData();
  const chosenPartner = getChosenPartner();

  const shouldShowUtilityItem = (itemKey: string) => {
    switch (itemKey) {
      case 'manage-sof':
      case 'priority-sof':
        return bindingStatus && BindingStatus.open === bindingStatus;
    }
  };

  const lauchInstallmentApp= () => {
    UtilitySectionTracking.trackItemClick('installment');
    const params  = "?utm_source=pay_later"
    launchDeeplink({ zpi: `/spa/v2/installment${params}`, zpa: `zalopay://launch/app/2263${params}` });
  }

  useEffect(() => {
    (async () => {
      const shouldLaunchSOFPriority = await shouldLaunchSOFPriorityFlow();
      if (shouldShowUtilityItem('priority-sof') && shouldLaunchSOFPriority) {
        await launchSOFPriorityFlow();
      }
    })();
  }, []);

  const viewContract = async () => {
    if (chosenPartner?.request_id) {
      UtilitySectionTracking.trackItemClick('contract');
      await handleViewContract(chosenPartner?.request_id);
    }
  };

  if (!bindingStatus || ![BindingStatus.open, BindingStatus.locked].includes(bindingStatus)) {
    return null;
  }

  return (
    <View style={styles.root}>
      <SectionContainer
        style={styles.container}
        title={
          <AppText bold size={16} style={styles.title}>
            Tiện ích
          </AppText>
        }>
        <Row cols={4}>
          <UtilityItem testId="contract-info" icon={images.IconAccountInfo} onPress={viewContract}>
            {'Thông tin\nhợp đồng'}
          </UtilityItem>
          <UtilityItem
            testId="notification-icon"
            icon={images.IconNotificationLineGreen}
            onPress={() => {
              UtilitySectionTracking.trackItemClick('noti');
              navigation.navigate(ScreenKey.Notification);
            }}>
            {'Thông báo'}
          </UtilityItem>
          {shouldShowUtilityItem('priority-sof') && (
            <UtilityItem
              disable={disablePrioritySOFBtn}
              testId="priority-sof-icon"
              icon={images.IconPrioritySOF}
              onPress={async () => {
                UtilitySectionTracking.trackItemClick('prioritize_sof');
                setDisablePrioritySOFBtn(true);
                await launchSOFPriorityFlow({ dequeue: true });
                setDisablePrioritySOFBtn(false);
              }}>
              {'Ưu tiên\nnguồn tiền'}
            </UtilityItem>
          )}
          {shouldShowUtilityItem('manage-sof') && (
            <UtilityItem
              testId="manage-sof-icon"
              icon={images.IconManageSOF}
              onPress={() => {
                UtilitySectionTracking.trackItemClick('esof_hub');
                launchSofsDeeplink();
              }}>
              {'Quản lý\nnguồn tiền'}
            </UtilityItem>
          )}
          <UtilityItem
              testId="installment-icon"
              icon={<View style={styles.installmentIcon}><ServiceFinanceInstallmentSecondary width={24} height={24} /></View>}
              onPress={lauchInstallmentApp}>
              Trả góp
            </UtilityItem>
        </Row>
      </SectionContainer>
      <ContractModal
        useFileViewer={Platform.OS === 'android'}
        onRequestClose={() => setContractVisible(false)}
        contractUrl={contractInfo?.url || ''}
        visible={contractVisible}
      />
    </View>
  );
};

const styles = StyleSheet.create({
  root: {
    backgroundColor: Colors.background,
    marginHorizontal: -16,
    paddingHorizontal: 16,
  },
  title: {
    marginBottom: 16,
  },
  container: {
    marginBottom: 8,
  },
  modalRoot: {
    ...StyleUtils.shadow,
    alignSelf: 'center',
    backgroundColor: AppColors.background,
    borderRadius: 8,
    width: 300,
    height: 330,
    padding: 16,
    alignItems: 'center',
  },
  modalButton: {
    width: '100%',
  },
  modalText: {
    textAlign: 'center',
  },
  closeButton: {
    position: 'absolute',
    top: 16,
    right: 16,
  },
  installmentIcon: {
    backgroundColor: AppColors.blue[25],
    borderRadius: 100,
    padding: 4,
  }
});

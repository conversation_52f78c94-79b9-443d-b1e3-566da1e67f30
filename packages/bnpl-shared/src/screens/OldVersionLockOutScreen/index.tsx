import React, { Fragment, useEffect } from 'react';
import { Linking, Platform, View } from 'react-native';
import { NavigationHeader } from '../../shared/nav/NavigationHeader';
import { AppImage, AppText } from '../../shared/react-native-customized';
import { Colors, windowHeight } from '../../shared/styles/StyleUtils';
import { StyleSheet } from '../../shared/styles/StyleSheet';
import { size } from '../../shared/styleUtils';
import { closeApp } from '../../shared/ZaloPayModules';
import { Spacer } from '../../components/Spacer';
import BottomButton from '../../shared/BottomButton';
import { trackLockOutScreenLoad } from 'bnpl-shared/src/screens/LockOutScreen/tracking';
import { getNativeVersion } from 'bnpl-shared/src/shared/utils/versionUtils';
import { images } from 'bnpl-shared/src/res';
import { ScreenKey } from 'bnpl-shared/src/constants';

export const OldVersionLockOutScreen = () => {
  const handleUpdateApp = () => {
    if (Platform.OS === 'android') {
      return Linking.openURL('https://play.google.com/store/apps/details?id=vn.com.vng.zalopay');
    }
    if (Platform.OS === 'ios') {
      return Linking.openURL('https://apps.apple.com/vn/app/zalopay-thanh-to%C3%A1n-trong-2s/id1112407590');
    }
  };

  useEffect(() => {
    trackLockOutScreenLoad({ stop_reason: 'update_app_version', native_app_version: getNativeVersion() });
  }, []);

  return (
    <Fragment>
      <NavigationHeader tag={ScreenKey.OldVersionLockOutScreen} onPressBack={closeApp} title="Tài Khoản Trả Sau" />
      <View style={styles.root}>
        <AppImage source={images.ImageLockout} style={styles.image} {...size(180)} />
        <AppText bold style={styles.title}>
          Cần cập nhật phiên bản Zalopay
        </AppText>
        <AppText color={Colors.text2} height={22} style={styles.content}>
          Để sử dụng dịch vụ, bạn cần cập nhật phiên bản Zalopay mới nhất từ kho ứng dụng.
        </AppText>
        <Spacer height={24} />
        <BottomButton title="Cập nhật ngay" onPress={handleUpdateApp} style={styles.button} />
      </View>
    </Fragment>
  );
};

//#region
const styles = StyleSheet.create({
  root: {
    flex: 1,
    alignItems: 'center',
    backgroundColor: Colors.background,
    paddingHorizontal: 24,
    paddingTop: windowHeight * 0.2,
  },
  image: {},
  title: {
    marginTop: 24,
  },
  content: {
    textAlign: 'center',
    marginTop: 8,
    marginBottom: 24,
  },
  button: {
    width: 280,
    marginBottom: 8,
  },
  secondaryActionTitleStyle: {
    fontWeight: 'normal',
  },
});
//#endregion

import { render, waitFor } from '@testing-library/react-native';
import React from 'react';
import { FAKE_NAVIGATION } from 'bnpl-shared/src/jest/constants';
import { TransactionDetailScreen } from 'bnpl-shared/src/screens/TransactionDetail/TransactionDetailScreen';
import { TransactionType } from 'bnpl-shared/src/types';
import { getFeeDetailApi } from 'bnpl-shared/src/api/getFeeDetailApi';
import { getFeeDetailApiBuilder } from 'bnpl-shared/src/api/__mocks__/getFeeDetailApi';
import { getTransactionDetailApi } from 'bnpl-shared/src/api/getTransactionDetailApi';
import { getTransactionDetailApiBuilder } from 'bnpl-shared/src/api/__mocks__/getTransactionDetailApi';
import { renderReduxWithNavigationWrapper } from 'bnpl-shared/src/jest/renderWithRedux';

jest.mock('bnpl-shared/src/api/getTransactionDetailApi', () => ({
  getTransactionDetailApi: jest.fn(),
}));

jest.mock('bnpl-shared/src/api/getFeeDetailApi', () => ({
  getFeeDetailApi: jest.fn(),
}));

describe(TransactionDetailScreen.name, () => {
  beforeEach(() => {
    (getTransactionDetailApi as jest.Mock).mockClear();
    (getFeeDetailApi as jest.Mock).mockClear();
  });

  afterEach(() => {
    (FAKE_NAVIGATION.getParam as jest.Mock).mockReset();
  });

  it('renders transaction detail as expected', async () => {
    (getTransactionDetailApi as jest.Mock).mockResolvedValue(getTransactionDetailApiBuilder.build());
    (FAKE_NAVIGATION.getParam as jest.Mock).mockImplementation(param => {
      if (param === 'transId') {
        return '1';
      }
      if (param === 'type') {
        return TransactionType.PAYMENT;
      }
    });
    const { queryByTestId } = renderReduxWithNavigationWrapper(<TransactionDetailScreen navigation={FAKE_NAVIGATION} />);
    await waitFor(() => expect(queryByTestId('trans-desc')?.children[0]).toEqual('Mua hàng'));
    await waitFor(() => expect(queryByTestId('trans-amount')?.children[0]).toEqual('-200.000đ'));
    await waitFor(() => expect(queryByTestId('trans-status-desc')?.children[0]).toEqual('status_desc_payment'));
    await waitFor(() => expect(queryByTestId('zlp-trans-id')?.children[0]).toEqual('zp_test_id'));
  });

  it('renders fee detail as expected', async () => {
    (getFeeDetailApi as jest.Mock).mockResolvedValue(getFeeDetailApiBuilder.build('fee_service'));
    (FAKE_NAVIGATION.getParam as jest.Mock).mockImplementation(param => {
      if (param === 'transId') {
        return '2';
      }
      if (param === 'type') {
        return TransactionType.FEE_SERVICE;
      }
    });
    const { queryByTestId } = renderReduxWithNavigationWrapper(<TransactionDetailScreen navigation={FAKE_NAVIGATION} />);
    await waitFor(() =>
      expect(queryByTestId('trans-desc')?.children[0]).toEqual(
        'Phí sử dụng phát sinh khi bạn không thanh toán hết dư nợ cuối kỳ của sao kê.',
      ),
    );
    await waitFor(() => expect(queryByTestId('trans-amount')?.children[0]).toEqual('-200.000đ'));
    await waitFor(() => expect(queryByTestId('trans-status-desc')?.children[0]).toBeFalsy());
    await waitFor(() => expect(queryByTestId('zlp-trans-id')?.children[0]).toEqual('2'));
  });

  // it('handle pull refresh as expected', async () => {
  //   (getTransactionDetailApi as jest.Mock).mockResolvedValue(getTransactionDetailApiBuilder.build('repayment'));
  //   (FAKE_NAVIGATION.getParam as jest.Mock).mockImplementation(param => {
  //     if (param === 'transId') {
  //       return '1';
  //     }
  //     if (param === 'type') {
  //       return TransactionType.REPAYMENT;
  //     }
  //   });
  //   const { getByTestId, queryByTestId } = render(<TransactionDetailScreen navigation={FAKE_NAVIGATION} />);
  //   await waitFor(() => expect(queryByTestId('scroll-container')).toBeTruthy());
  //   getByTestId('scroll-container')?.props?.refreshControl?.props?.onRefresh();
  //   await waitFor(() => expect(getTransactionDetailApi).toHaveBeenCalledTimes(2));
  // });
});

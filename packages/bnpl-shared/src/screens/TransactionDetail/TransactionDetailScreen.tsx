import React, { FC, useEffect, useRef, useState } from 'react';
import { NavigationScreenProp } from 'react-navigation';
import { Flex, AppRefreshControl } from 'bnpl-shared/src/components';
import { StyleSheet } from 'bnpl-shared/src/shared/styles/StyleSheet';
import { Colors, windowWidth } from 'bnpl-shared/src/shared/styles/StyleUtils';
import { TransactionMetadataCard } from 'bnpl-shared/src/screens/TransactionDetail/TransactionMetadataCard';
import { TransactionStatusCard } from 'bnpl-shared/src/screens/TransactionDetail/TransactionStatusCard';
import { TransactionTimeCard } from 'bnpl-shared/src/screens/TransactionDetail/TransactionTimeCard';
import { useNavigation } from 'bnpl-shared/src/shared/nav';
import { closeApp, hideLoading, showLoading } from 'bnpl-shared/src/shared/ZaloPayModules';
import { trackTransactionDetailScreenEvent } from 'bnpl-shared/src/utils/tracking';
import { AppColors, ScreenKey } from 'bnpl-shared/src/constants';
import { TransactionStatus, TransactionType } from 'bnpl-shared/src/types';
import { useTransactionDetail } from 'bnpl-shared/src/screens/TransactionDetail/useTransactionDetail';
import { InfoModalService, ModalType } from 'bnpl-shared/src/services';
import { View } from 'react-native';
import { AppButton, AppImage, AppText } from 'bnpl-shared/src/shared/react-native-customized';
import { images } from 'bnpl-shared/src/res';
import { Spacer } from 'bnpl-shared/src/components/Spacer';
import BottomButton from 'bnpl-shared/src/shared/BottomButton';
import { useNavigationContext } from 'bnpl-shared/src/components/NavigationWrapper';

const FINAL_STATUS = [TransactionStatus.SUCCEEDED, TransactionStatus.FAILED];

export const TransactionDetailScreen: FC<{
  navigation: NavigationScreenProp<{}, { transId: string; type: TransactionType; showCloseButton: boolean }>;
}> = props => {
  const navigation = useNavigation(props);
  const transId: string = navigation.getParam('transId');
  const showCloseButton: boolean = navigation.getParam('showCloseButton') || false;
  const transType: TransactionType = navigation.getParam('type');
  const [refreshing, setRefreshing] = useState(false);
  const isFee = transType === TransactionType.FEE_SERVICE || transType === TransactionType.FEE_LATE;
  const { resources, fetchResources } = useTransactionDetail({ id: transId, type: transType });
  const intervalIdRef = useRef<any>();
  const { state: navigationState } = useNavigationContext();

  useEffect(() => {
    if (resources) {
      try {
        if (!FINAL_STATUS.includes(resources.status) && !intervalIdRef.current) {
          intervalIdRef.current = setInterval(fetchResources, 1000);
          return () => {
            clearInterval(intervalIdRef.current);
          };
        } else if (intervalIdRef.current) {
          clearInterval(intervalIdRef.current);
        }
        trackTransactionDetailScreenEvent('000', {
          tnx_type: resources.type,
          status: resources.status,
        });
      } catch (e: any) {}
    }
  }, [resources]);

  const handleOnRefresh = async () => {
    setRefreshing(true);
    await fetchResources();
    setRefreshing(false);
  };

  useEffect(() => {
    if (transId) {
      (async () => {
        try {
          showLoading();
          await fetchResources();
        } catch (e: any) {
          InfoModalService.showModal({
            screen: (
              <View style={[styles.modal_root, navigationState?.fullScreenData?.fullScreen && styles.modalRootFullScreen]}>
                <AppImage style={styles.image} width={150} height={150} source={images.ImageTransactionProcessing} />
                <AppText style={styles.text} bold>
                  Hệ thống đang xử lý
                </AppText>
                <Spacer height={8} />
                <AppText style={styles.text}>Vui lòng kiểm tra giao dịch và thử lại nếu chưa thành công</AppText>
                <AppButton
                  testID={'close-button'}
                  buttonStyle={styles.button}
                  title={'Đóng'}
                  onPress={() => closeApp()}
                />
              </View>
            ),
            type: ModalType.MODAL,
            options: {
              transparent: false,
            },
          });
        } finally {
          hideLoading();
        }
      })();
    }
  }, [transId]);

  if (!resources) {
    return null;
  }

  return (
    <>
      <Flex
        scrollable={true}
        style={styles.root}
        tag={ScreenKey.TransactionDetail}
        scrollViewProps={{ refreshControl: <AppRefreshControl onRefresh={handleOnRefresh} refreshing={refreshing} /> }}
        navigation={navigation}>
        <TransactionMetadataCard
          style={[styles.sectionContainer, styles.metadataCard]}
          amount={resources.amount}
          description={resources.description}
          type={resources.type}
        />
        <TransactionStatusCard
          statusDescription={isFee ? '' : resources.status_description}
          type={resources.type}
          stages={resources.stages}
          style={styles.sectionContainer}
          status={resources.status}
        />
        <TransactionTimeCard
          type={resources.type}
          style={styles.sectionContainer}
          time={resources.updated_at}
          id={resources.zp_trans_id || transId || ''}
        />
      </Flex>
      {showCloseButton ? <BottomButton title={'Đóng'} onPress={() => closeApp()} style={styles.bottomBtn} /> : null}
    </>
  );
};

const styles = StyleSheet.create({
  root: {
    flex: 1,
    backgroundColor: Colors.background2,
  },
  container: {
    marginTop: -97,
  },
  contentContainer: {
    padding: 16,
    paddingBottom: 0,
  },
  sectionContainer: {
    marginBottom: 16,
    marginHorizontal: 16,
  },
  metadataCard: {
    marginTop: 50,
  },
  modal_root: {
    width: windowWidth * 0.75,
    backgroundColor: AppColors.background,
    padding: 16,
    borderRadius: 8,
    alignSelf: 'center',
  },
  modalRootFullScreen: {
    width: windowWidth * 0.65,
  },
  text: {
    textAlign: 'center',
  },
  button: {
    marginTop: 16,
    width: '100%',
  },
  image: {
    alignSelf: 'center',
  },
  bottomBtn: {
    marginHorizontal: 16,
  },
});

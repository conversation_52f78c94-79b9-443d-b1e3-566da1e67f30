import { Linking, View } from 'react-native';
import { Spacer } from 'bnpl-shared/src/components/Spacer';
import { AppImage, AppText } from 'bnpl-shared/src/shared/react-native-customized';
import { images } from 'bnpl-shared/src/res';
import { Colors } from 'bnpl-shared/src/shared/styles/StyleUtils';
import React, { FC, useEffect } from 'react';
import { StyleSheet } from 'bnpl-shared/src/shared/styles/StyleSheet';
import { getBottomSafe } from 'bnpl-shared/src/utils';
import mapPredefinedCTAtoComponent from 'bnpl-shared/src/utils/mapPredefinedCTAtoComponent';
import {
  trackEdgeCaseInfoModalCtaPress,
  trackShowBottomSheetFaceChallenge,
} from 'bnpl-shared/src/screens/onboarding-flow/OnboardingScreen/tracking';
import { AuthChallengeType, EdgeCaseInfo, PredefinedCTA, UMSource } from 'bnpl-shared/src/types';
import { CIMB_TEL, LOTTE_TEL, ZLP_SUPPORT_TEL } from 'bnpl-shared/src/constants/PublicUrls';
import { launchKycDeepLink, usePromptAuthChallengeFlow } from 'bnpl-shared/src/shared/ZaloPayModules';
import { AppColors } from 'bnpl-shared/src/constants';
import { postResetNfcApi } from 'bnpl-shared/src/api/postResetNfcApi';
import { InfoModalService, ModalType } from 'bnpl-shared/src/services';
import { launchCreditScoreDeeplink } from 'bnpl-shared/src/utils/launchCreditScoreDeeplink';
import { useAuthentication } from 'bnpl-shared/src/hooks/useAuthentication';
import { useAuthChallengeSource } from 'bnpl-shared/src/hooks/useAuthChallengeSource';
import { closeMiniApp } from 'bnpl-shared/src/utils/closeMiniApp';
import { openOAOHub } from 'bnpl-shared/src/utils/openOAOHub';
import { useAppSelector } from 'bnpl-shared/src/redux/store';
import { isAppVersionGreaterThanOrEqual } from 'bnpl-shared/src/utils/appVersion';

export const EdgeCaseModalUI: FC<{
  edgeCaseInfo: EdgeCaseInfo;
  handleCtaAction?: (cta: string) => void;
  location?: string;
}> = ({ edgeCaseInfo, handleCtaAction, location }) => {
  const { promptAuthChallengeFlow } = usePromptAuthChallengeFlow();
  const authSource = useAuthChallengeSource();
  const launchAuthChallenge = useAuthentication().launchAuthChallenge;
  const appInfo = useAppSelector(state => state.appInfo);
  const ctaComponents = edgeCaseInfo.ctas
    ? mapPredefinedCTAtoComponent({
        ctas: edgeCaseInfo.ctas,
        onCTAPress: cta => {
          trackEdgeCaseInfoModalCtaPress({ button_name: cta, location: location || '' });
          handleCtaAction?.(cta);
          switch (cta) {
            case PredefinedCTA.REDIRECT_INCREASE_CREDIT_SCORE:
              launchCreditScoreDeeplink();
              break;
            case PredefinedCTA.RESET_NFC_NEW_SOURCE:
            case PredefinedCTA.RESET_NFC:
            // TODO: Remove this after 10.12.0 is released all users
            if (appInfo?.platform === 'ZPA' && !isAppVersionGreaterThanOrEqual('10.12.0')) {
              //#region old reset NFC flow
              (async () => {
                try {
                  const ctas =
                  cta === PredefinedCTA.RESET_NFC_NEW_SOURCE
                    ? [PredefinedCTA.REDIRECT_NFC_NEW_SOURCE_PROCESS]
                    : [PredefinedCTA.REDIRECT_NFC_PROCESS];
                  await postResetNfcApi();
                  InfoModalService.showModal({
                    screen: (
                      <EdgeCaseModalUI
                        edgeCaseInfo= {{
                          title: 'Bổ sung thông tin sinh trắc học',
                          description: 'Sử dụng CCCD có chip để bổ sung thông tin.',
                          ctas,
                        }}
                        handleCtaAction = {(_: string) => {
                          InfoModalService.hideModal();
                        }}
                      />
                    ),
                    type: ModalType.BOTTOM_SHEET,
                    bottomSheetProps: {
                      title: '',
                    },
                  });
                } catch (e: any) { }
              }) ();
              //#endregion
              return;
            }
            setTimeout(async () => {
              await promptAuthChallengeFlow(Number(authSource), AuthChallengeType.ResetNFC);
            }, 500);
            break;
            case PredefinedCTA.REDIRECT_NFC_NEW_SOURCE_PROCESS:
            case PredefinedCTA.REDIRECT_NFC_PROCESS:
              setTimeout(async () => {
                await promptAuthChallengeFlow(Number(authSource), AuthChallengeType.NFC);
              }, 500);
              break;
            case PredefinedCTA.REDIRECT_CREATE_KYC_PROCESS:
              setTimeout(() => {
                launchKycDeepLink(UMSource.ADJUST_KYC_FROM_BNPL);
              }, 500);
              break;
            case PredefinedCTA.REDIRECT_KYC_PROCESS:
              setTimeout(async () => {
                await launchAuthChallenge({
                  source: Number(authSource),
                  authType: AuthChallengeType.Adjust,
                });
              }, 500);
              break;
            case PredefinedCTA.CONTACT_ZALOPAY_CS:
              Linking.openURL(`tel:${ZLP_SUPPORT_TEL}`);
              break;
            case PredefinedCTA.CALL_BANK:
              Linking.openURL(`tel:${CIMB_TEL}`);
              break;
            case PredefinedCTA.CALL_LFVN:
              Linking.openURL(`tel:${LOTTE_TEL}`);
              break;
            case PredefinedCTA.BACK_HOMEPAGE:
              closeMiniApp();
              break;
            case PredefinedCTA.REDIRECT_OAO:
              openOAOHub();
              break;
          }
        },
        buttonStyle: { width: '100%' },
      })
    : null;

  const renderImage = () => {
    switch (true) {
      case edgeCaseInfo.ctas?.includes(PredefinedCTA.REDIRECT_FACE_AUTHENTICATE_PROCESS):
        return <AppImage source={images.ImageFaceChallenge} height={200} width={343} />;
      case edgeCaseInfo.ctas?.includes(PredefinedCTA.REDIRECT_INCREASE_CREDIT_SCORE):
        return <AppImage source={images.ImageCreditScore} height={300} width={300} />;
      default:
        return <AppImage source={images.ImageContractReject} height={180} width={180} />;
    }
  };

  useEffect(() => {
    if (edgeCaseInfo.ctas?.includes(PredefinedCTA.REDIRECT_FACE_AUTHENTICATE_PROCESS)) {
      trackShowBottomSheetFaceChallenge();
    }
  }, []);

  return (
    <View testID="edge-case-modal" style={styles.content}>
      <Spacer height={16} />
      {renderImage()}
      <Spacer height={24} />
      <View style={styles.text}>
        <AppText bold size={16} height={20}>
          {edgeCaseInfo.title}
        </AppText>
        <Spacer height={8} />
        <AppText center color={Colors.text2}>
          {edgeCaseInfo.description}
        </AppText>
      </View>
      <Spacer height={20} />
      <View style={{ width: '100%' }}>{ctaComponents}</View>
    </View>
  );
};

const styles = StyleSheet.create({
  content: {
    paddingHorizontal: 16,
    alignItems: 'center',
    marginBottom: 16 + getBottomSafe(),
    backgroundColor: AppColors.background,
  },
  text: {
    paddingHorizontal: 8,
    alignItems: 'center',
  },
  button: {
    width: 340,
    marginTop: 16,
  },
});

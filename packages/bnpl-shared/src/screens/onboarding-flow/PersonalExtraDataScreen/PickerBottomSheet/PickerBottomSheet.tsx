import { Spacer } from 'bnpl-shared/src/components/Spacer';
import { AppTextInput, AppImage, AppText } from 'bnpl-shared/src/shared/react-native-customized';
import { StyleSheet } from 'bnpl-shared/src/shared/styles/StyleSheet';
import { Colors, windowHeight } from 'bnpl-shared/src/shared/styles/StyleUtils';
import React, { FC, Fragment, useState } from 'react';
import { FlatList } from 'react-native';
import { PickerListItem } from './PickerListItem';
import { images } from 'bnpl-shared/src/res';
import LocationPicker, { CurrentLocation, PermissionStatus } from 'bnpl-shared/src/features/location_picker';
import { ResourceType } from 'bnpl-shared/src/types';
import { normalizeVietnamese } from 'bnpl-shared/src/utils/normalizeVietnamese';
import { toast } from 'sonner';
import { trackAutoGetLocation } from '../../OnboardingScreen/tracking';

type Item = { id: string; value: string };

type Props = {
  items: Item[];
  onRequestClose?: () => void;
  value?: string;
  placeholder?: string;
  onChange?: (item: Item) => void;
  priorityIds?: string[];
  testID?: string;
  resourceType?: ResourceType;
};

export const PickerBottomSheet: FC<Props> = ({ placeholder, items, value, onChange, priorityIds, testID, resourceType }) => {
  const [query, setQuery] = useState('');
  let dataSource = items;
  if (dataSource.length) {
    if (priorityIds) {
      const prioritizedList: Item[] = [];
      priorityIds.forEach(id => {
        const item = items.find(i => i.id === id);
        if (item) {
          prioritizedList.push(item);
        }
      });
      const unPrioritizedList = items.filter(i => !priorityIds.includes(i.id));
      dataSource = [...prioritizedList, ...unPrioritizedList];
    }
    dataSource = dataSource.filter(({ value }) => value?.toLowerCase()?.indexOf?.(query.toLowerCase()) >= 0);
  }

  const handleSearchInputChangeText = (text: string) => {
    setQuery(text);
  };

  const handleGetDataFromLocationPicker = (data: CurrentLocation) => {
    if(resourceType !== "CITY") {
      return;
    }
    if(!items?.length) {
        toast.error("Đã có lỗi xảy ra, vui lòng thử lại sau.");
        return;
    }

    const { shouldOpenSetting, location, permissionStatus } = data;
    location ? trackAutoGetLocation({
      location: {
        lat: String(location.latitude),
        long: String(location.longitude)
        
      }
    }) : null;

    if (location?.city_name && items?.length > 0) {
      const matchedOption = items.find((option: Item) =>
        normalizeVietnamese(option.value.toLowerCase()).includes(
          normalizeVietnamese(location.city_name.toLowerCase())
        )
      );

      if (matchedOption) {
        onChange?.(matchedOption);
        toast.success(`Đã tự động chọn nơi sống ${matchedOption.value} cho bạn.`);
        return;
      } 
    }

    if (!location?.city_name && shouldOpenSetting) {
      //reset focus trap of Drawer
      document.body.style.pointerEvents = "auto";
      toast("Mở cài đặt để bật quyền truy cập vị trí.", {
        action: {
          label: "Mở",
          onClick: () => {
            window?.zlpSdk?.Device?.openSettings();
          },
        },
        duration: 8000,
      });
      return;
    }
    if ([PermissionStatus.PERMISSION_DENIED, PermissionStatus.PERMISSION_NOT_GRANTED].includes(permissionStatus)) {
      toast.error("Vui lòng bật quyền truy cập vị trí để sử dụng tính năng này.");
      return;
    }
  }; 

  return (
    <Fragment>
      <AppTextInput
        testID="picker-text-input"
        placeholder={placeholder}
        leftIcon={<AppImage source={images.IconSearch} height={22} width={22} />}
        value={query}
        clearable
        containerStyle={styles.input}
        onChangeText={handleSearchInputChangeText}
      />
      <Spacer height={12} />
      {resourceType === "CITY" ? <LocationPicker title="Sử dụng vị trí hiện tại của tôi"  onLocationChange={handleGetDataFromLocationPicker} /> : null}
      <Spacer height={12} />
      <FlatList
        data={dataSource}
        testID={testID}
        contentContainerStyle={styles.list}
        ListEmptyComponent={
          <AppText size={16} height={20} color={Colors.text2}>
            Không có kết quả bạn tìm
          </AppText>
        }
        keyboardShouldPersistTaps="handled"
        renderItem={({ item, index }) => (
          <PickerListItem
            index={index}
            testID={`picker-item-${index}`}
            item={item}
            selected={value === item.id}
            onSelect={onChange}
          />
        )}
        keyExtractor={({ id }) => id}
      />
    </Fragment>
  );
};

//#region
const styles = StyleSheet.create({
  input: {
    marginHorizontal: 16,
    marginTop: 16,
  },
  list: {
    marginHorizontal: 16,
  },
  bodyStyle: {
    height: windowHeight - 150,
  },
});
//#endregion

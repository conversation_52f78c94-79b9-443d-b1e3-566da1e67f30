// @ts-ignore
import { AuthenticationProvider } from 'auth_challenge/AuthenticationContext';
import React, { useEffect } from 'react';
import { BrowserRouter, Redirect, Route, RouteComponentProps, Switch } from 'react-router-dom';
import { NavigatorConfig, NavigatorOptions } from './types';
import { alertModalRef, infoModalRef, InfoModalService, loadingModalRef } from 'bnpl-shared/src/services';
import InfoModal from 'bnpl-shared/src/components/InfoModal';
import { AlertModal } from 'bnpl-shared/src/components/InfoModal/AlertModal';
import { LoadingIndicator } from 'bnpl-shared/src/components/InfoModal/LoadingIndicator';
import { useDispatch } from 'react-redux';
import { useAppSelector } from 'bnpl-shared/src/redux/store';
import { setCurrentPath, setPreviousPath } from 'bnpl-shared/src/redux/routingReducer';
import ZpiSurveyModal, { SurveyModalParams } from 'bnpl-shared/src/components/InfoModal/ZpiSurveyModal';
import { setSurvey } from 'bnpl-shared/src/redux/surveyModalReducer';
import { NavigationWrapper, useNavigationContext } from "bnpl-shared/src/components/NavigationWrapper";
import { AppColors, ScreenKey } from "bnpl-shared/src/constants";
import { AppToast } from 'bnpl-shared/src/components/AppToast/AppToast';

const ComponentWrapper = ({
  screenKey,
  config,
  ...rest
}: {
  screenKey: string;
  config: NavigatorConfig;
  } & RouteComponentProps<{}, {}, unknown>) => {
  const { dispatch: dispatchNavigation } = useNavigationContext();
  const title = config[screenKey].title;
  const Component = config[screenKey].component;

  useEffect(() => {
    dispatchNavigation({
      type: 'SET_TITLE',
      payload: title || 'Tài khoản trả sau',
    });
  }, [title]);

  useDocumentTitle(title);

  const dispatch = useDispatch();
  useEffect(() => {
    InfoModalService.init();
    const currentPath = rest.location?.pathname?.replace('/', '') || '';
    dispatch(setCurrentPath(currentPath));
    return () => {
      dispatch(setPreviousPath(currentPath));
    };
  }, []);

  return <Component {...rest} />;
};

export const buildNavigator = <T extends NavigatorConfig>(config: T, options: NavigatorOptions<T>) => {
  return function Navigator() {
    const dispatch = useDispatch();
    const survey: SurveyModalParams | null = useAppSelector(state => state?.surveyModal?.survey);

    return (
      <AuthenticationProvider>
        <BrowserRouter basename={options.zpiBaseUrl}>
          <NavigationWrapper
            customNavigationOptions={{
              [ScreenKey.MiniRepaymentHistory]: {
                background: "white",
                color: AppColors.dark[500],
              },
            }}
          >
            <Switch>
              {Object.keys(config).map(key => (
                <Route
                  exact
                  key={key}
                  path={`/${key}`}
                  render={props => <ComponentWrapper {...props} config={config} screenKey={key} />}
                />
              ))}
              <Redirect to={`/${options.default.toString()}`} />
            </Switch>
            <AppToast />
          </NavigationWrapper>
          <InfoModal ref={infoModalRef} />
          <AlertModal ref={alertModalRef} />
          <LoadingIndicator ref={loadingModalRef} />
          {survey && (
            <ZpiSurveyModal
              {...survey}
              onCancel={survey?.onCancel ? survey.onCancel : () => dispatch(setSurvey(null))}
            />
          )}
        </BrowserRouter>
      </AuthenticationProvider>
    );
  };
};

//#region
const useDocumentTitle = (initTitle?: string) => {
  useEffect(() => {
    initTitle && (document.title = initTitle);
  }, [initTitle]);
};
//#endregion

import { MiniBNPLOnboardingNextStep } from './MultiPartnerTypes';

export interface RepaymentItem {
  billId:number;
  title: string;
  description: string;
  iconURL: string;
  dueTime: string;
  orderTime: string;
  paidTime: string;
  amount: number;
  zpTransID: string;
  appID: number | string;
  displayStatus: RepaymentDisplayStatus;
  dueDateDescription?: string;
  statusDescription: string;
  onRepayBtnClick?: () => void;
}

export interface HistoryItem {
  id: number;
  full_repaid_date: string;
  repayable_date: string;
  due_date: string;
  zp_trans_id: string;
  service_name: string;
  service_icon: string;
  description: string;
  amount: string;
  repaid_amount: string;
  app_id: number | string;
  status: "INIT" | "CREATED" | "FAILED" | "REFUND_FULL" | "SETTLED"
}

export type RepaymentDisplayStatus = "paid" | "upcoming" | "pending" | "overdue" | "refunded";

export interface HistoryItemExtend {
  displayStatus: RepaymentDisplayStatus;
  dueDateDescription?: string;
  statusDescription: string;
}

export type TotalOutstandingData = {
  due_status: number;
  total_due_amount: string;
  due_distance: string;
};

export interface MiniBnplTotalOutstandingState {
  dueStatus: MiniBnplTotalOutstandingDueStatus;
  totalDueAmount: number;
  dueDistance: number;
}

export enum MiniBnplTotalOutstandingDueStatus {
  NOT_DUE = 0,
  DUE = 1,
  OVERDUE = 2,
}

export type MiniBnplStatementConditions = {
  isExistOverDue: boolean;
  isExistOnDue: boolean;
  isInvalid: boolean;
  isLoading: boolean;
};

export type MiniBnplOnboardingInfo = {
  fullName: string;
  phone: string;
  permanentAddress: string;
};

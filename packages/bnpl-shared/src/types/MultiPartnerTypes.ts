import { AccountStatus, MPOnboardingStatus, PartnerCode } from 'bnpl-shared/src/constants';
import { PredefinedCTA } from 'bnpl-shared/src/types/index';
import { NavigationScreenProp } from 'react-navigation';

/**
 * Description:
 * - MP: Multi Partner
 */
export type MPOnboardingInfo = {
  basic_profile: MPBasicProfile;
  detail_profile: MPDetailProfile;
  binding_log_status: MPOnboardingStatus;
};

export type MPBasicProfile = {
  full_name: string;
  phone_number: string;
  id_number: string;
};

export type MPDetailProfile = {
  birthday: string;
  id_issued_date: string;
  id_expired_date: string;
  id_issued_location: string;
  permanent_address: string;
  temp_residence_address: string;
  gender: string;
  education: string;
  occupation: string;
  job_title: string;
  employment_status: string;
  source_of_fund: string;
  monthly_income: string;
  living_city: string;
  loan_purpose: string;
  nationality: string;
  avatar: string;
};

export type RoutingInfo = {
  partner_code: PartnerCode;
  partner_name: string;
  status: MPOnboardingStatus;
  is_created: boolean;
  request_id?: string;
  is_eligible_for_onboarding: boolean;
  has_submit_to_partner: boolean;
  is_approved: boolean;
  account_id: string;
  fee: string;
  descriptions: string[];
  process_bar: {
    total_step: number;
    current_step: number;
  };
  onboarding_next_step: string;
};

export type PartnerData = Pick<
  RoutingInfo,
  'partner_code' | 'partner_name' | 'status' | 'request_id' | 'account_id' | 'fee' | 'onboarding_next_step'
>;

export interface ScreenProps<ParamType> {
  navigation: NavigationScreenProp<any, ParamType>;
}

export interface ConfigurableScreenProps<ParamType, UIConfigType> extends ScreenProps<ParamType> {
  uiConfig?: UIConfigType;
}

export type BindingUIData = {
  occupation_code: string;
  monthly_income_code: string;
  statement_date: string;
  job_title_code: string;
  old_id_number?: string;
};

export type AccountInfo = {
  id: string;
  zalopay_id: string;
  partner_code: string;
  status: AccountStatus;
  total_limit: string;
  created_at: string;
  statement_date: string;
  statement_grace_full_date: string;
  benefits: {
    code: AccountBenefit;
    start_time: string;
    end_time: string;
    description: string;
  }[];
};

export enum AccountBenefit {
  FREE_TRIAL = 'new_user_waive_fee',
}

export type RequestOTPResp = {
  expired_time: number;
  resend_time: number;
  request_id: string;
};

export type MetadataNotice = {
  title: string;
  desc: string;
  ctas: { type: PredefinedCTA; metadata: any }[];
};

export type ErrorResponse = {
  code: number;
  message: string;
  reason: string;
  metadata: {
    notice?: MetadataNotice;
  };
};

export enum RepaymentStatus {
  NO_OUTSTANDING = 'STATEMENT_REPAYMENT_STATUS_NO_OUTSTANDING',
  INCOMING = 'STATEMENT_REPAYMENT_STATUS_INCOMING',
  ON_DUE = 'STATEMENT_REPAYMENT_STATUS_ON_DUE',
  OVERDUE = 'STATEMENT_REPAYMENT_STATUS_OVERDUE',
}

/**
 * [updated at 12/06/2024]
 * - UNKNOWN_STATEMENT: fallback, default if encountering unexpected errors.
 * - NO_SPEND: statement outstanding = 0, no outstanding balance in the statement period.
 * - NO_STATEMENT_DEBT: statement has debt but it has been paid off. (Currently LFVN does not update the remaining amount,
 * so if the statement has debt, this status will not be available. In the future, if they update or something, this will be available.)
 * - STATEMENT_DEBT_AVAILABLE: statement has debt and not fully paid, still has outstanding balance for the period.
 */
export enum StatementStatus {
  UNKNOWN_STATEMENT = 'NO_STATEMENT_DEBT',
  NO_SPEND = 'NO_SPEND',
  NO_STATEMENT_DEBT = 'NO_STATEMENT_DEBT',
  STATEMENT_DEBT_AVAILABLE = 'STATEMENT_DEBT_AVAILABLE',
}

export type AccountStatementType = {
  statement_min_outstanding_balance: string;
  statement_total_outstanding_balance: string;
  statement_remaining_total_outstanding_balance: string;
  statement_remaining_min_outstanding_balance: string;
  repayment_status: RepaymentStatus;
  statement_status: StatementStatus;
  days_to_maturity: number;
  statement_billing_date: string;
  statement_due_date: string;
  statement_due_date_with_hour: string;
  partner_code: PartnerCode;
};

export enum MiniBNPLOnboardingNextStep {
  ONBOARDING = 'onboarding',
  HOME = 'home',
  QUERY_STATUS = 'query_status',
  VERIFY_FACE = 'verify_face',
  FINISH = 'finish',
  REJECT = 'reject',
}

export type MiniBNPLOnboardingStatusResp = {
  onboarding_next_step: MiniBNPLOnboardingNextStep;
  status: MPOnboardingStatus;
};

export type MiniBNPLCreateRepayOrderResp = {
  app_id: number;
  app_trans_id: string;
  order_no: string;
  transaction_id: string;
  user_id: string;
  zp_trans_token: string;
};

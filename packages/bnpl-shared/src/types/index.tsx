import { ImageSourcePropType } from 'react-native';
import { ImageSource } from '../shared/types';
import { PartnerCode } from 'bnpl-shared/src/constants';
import { RepayMethodId } from 'bnpl-shared/src/constants';
export * from './RenewOverdraftTypes';
export * from './UtilitiesTypes';
export * from './MultiPartnerTypes';
export * from './UpdateNfcTypes';
export * from './MiniBnpl';

export enum ZlpPlatform {
  ZPA = 'ZPA',
  ZPI = 'ZPI',
}

export enum BindingStatus {
  new = 'new',
  processing = 'processing',
  open = 'open',
  close = 'close',
  reject = 'reject',
  suspended = 'suspended',
  locked = 'locked',
}

export enum UserIdType {
  Unknown = 'Unknown',
  IDCard = 'IDCard',
  Passport = 'Passport',
  CitizenID = 'CitizenID', // Căn cước công dân
  OfficerCert = 'OfficerCert', // Chứng minh sĩ quan
  NewCitizenID = 'NewCitizenID', // CCCD gắn chip
}

export enum TransactionType {
  PAYMENT = 'PAYMENT',
  REPAYMENT = 'REPAYMENT',
  REVERT_FOR_PAYMENT = 'REVERT_FOR_PAYMENT',
  REFUND_FOR_PAYMENT = 'REFUND_FOR_PAYMENT',
  REFUND_FOR_REPAYMENT = 'REFUND_FOR_REPAYMENT',
  FEE_SERVICE = 'FEE_SERVICE',
  FEE_LATE = 'FEE_LATE',
  FEE = 'FEE',
}

export enum TransactionFilterType {
  PAYMENT = 'PAYMENT',
  REPAYMENT = 'REPAYMENT',
  FEE = 'FEE',
  REFUND_FOR_PAYMENT = 'REFUND_FOR_PAYMENT',
}

export enum TransactionStatus {
  INIT = 'INIT',
  PROCESSING = 'PROCESSING',
  PENDING = 'PENDING',
  SUCCEEDED = 'SUCCEEDED',
  FAILED = 'FAILED',
}

/**
 * For some kinds of transactions, we have to show a progress bar.
 * Each item of that progress bar is called a node.
 * This is the status enum of that node.
 */
export enum TransactionNodeStatus {
  succeeded = 'succeeded',
  pending = 'pending',
  failed = 'failed',
}

export type Transaction = {
  id: string;
  type: TransactionType;
  status: TransactionStatus;
  amount: number;
  description: string;
  updated_at: string;
  created_at: string;
  metadata?: {
    label?: string;
  };
};

export enum Bod1SubmitStatus {
  PROCESSING = 'PROCESSING',
  CAN_ONBOARD = 'CAN_ONBOARD',
  REJECTED = 'REJECTED',
  EXISTED = 'EXISTED',
}

export enum OnboardingStatus {
  WAITING_CONTRACT = 'OS_WAITING_CONTRACT',
  READY_TO_SIGN = 'OS_READY_TO_SIGN',
  WAITING_APPROVE = 'OS_WAITING_APPROVE',
  MANUAL_APPROVE = 'OS_MANUAL_APPROVAL',
  APPROVE = 'OS_APPROVED',
  DATA_ERROR = 'OS_DATA_ERROR',
  REJECTED = 'OS_REJECTED',
}

export enum BindingStep {
  //FE-defined status
  _BOD_RESUBMIT = -2,
  _FACE_CHALLENGE_COMPLETED = -1,
  _BOD2_FACE_CHALLENGE = -3,
  //BE-defined status
  BOD1_SUCCEEDED = 0,
  BOD1_REJECTED = 1,
  BOD2_PROCESSING = 2,
  BOD2_SUCCEEDED = 3,
  BOD2_REJECTED = 4,
  OTP_VERIFIED = 5,
  WAITING_APPROVE = 6,
  APPROVE = 7,
  REJECTED = 8,
}

export type UserBalance = {
  total_limit: number;
  available_balance: number;
  over_repayment_balance: number;
  outstanding_balance: number;
  user_service_fee?: number;
};

export enum CashierCode {
  SUCCESS = 1,
  CANCEL = 4,
  FAIL = -1,
  PROCESSING = -1, //don't have a specific status code for processing
}

/**
 * Reasons that user can see LockOutScreen
 */
export enum LockOutReason {
  maintain = 'maintain',
  rejected = 'rejected',
  blacklist = 'blacklist',
  non_whitelisted = 'non_whitelisted',
  passport_officer = 'passport_officer',
  risk_rejected = 'risk_rejected',
  non_accepted = 'non_accepted',
}

export type SubjectItemType = {
  testId?: string;
  title: string;
  icon: ImageSourcePropType;
  tag?: string;
};

export type StatementSummaryType = {
  approved_limit: number;
  available_limit: number;
  limit_expire_date: string;
  //date when statement is created
  statement_date: string;
  //due date of the statement
  repayment_grace_end_date: string;
  //fixed repaid amount of this statement not changed when user has repaid
  min_pay_amount: number;
  total_outstanding_amount: number;
  total_outstanding_debt_obligation_amount: number;
  total_new_od_transaction_amount: number;
  statement_status: 'NO_SPEND' | 'NO_STATEMENT_DEBT' | 'STATEMENT_DEBT_AVAILABLE';
  /**
   *  day to due date:
   *     > 0: before due date,
   *     = 0: on due date,
   *     < 0: over due date
   **/
  days_to_maturity: number;
  repaid_amount: number;
  max_repayment_amount: number;
  /**
   * dynamic repay amount which is calculated when user has repaid
   * @deprecated: use <suggested_min_amount> instead which is included fee & charge amount
   *              that is calculated from bank when statement is overdue,
   */
  min_repayment_amount: number;
  //same as <min_repayment_amount> but included fee & penalty from bank when statement is overdue
  suggested_min_amount: number;
};

export enum RepaymentState {
  /*
    Total debt of the customer is equal to 0.
   */
  NO_OUTSTANDING = 'no_outstanding',
  /*
    - Total debt of the customer is greater than 0.
    - No transactions occurred during the period (no statement).
   */
  STATEMENT_NOT_AVAILABLE = 'not_available',
  /*
    - Total debt of the customer is greater than 0.
    - Customers have transactions in the period but have paid all the transactions before the statement date.
   */
  STATEMENT_NO_DEBT = 'no_debt',
  /*
    - Total debt of the customer is greater than 0.
    - Customers who have statement but have already repaid
   */
  STATEMENT_ALREADY_REPAID = 'repaid',
  /*
    - Total debt of the customer is greater than 0
    - Customers have transactions during the period.
    - Not yet due for payment.
   */
  BEFORE_DUE = 'before_due',
  /*
    - Total debt of the customer is greater than 0
    - Customers have transactions during the period.
    - Payment due
   */
  DUE = 'due',
  /*
    - Total debt of the customer is greater than 0
    - Customers have transactions during the period.
    - Payment overdue
   */
  OVER_DUE = 'over_due',
  UNDEFINED = 'undefined', // Self-define state
}

export enum UMSource {
  SET_KYC_FROM_BNPL = '40',
  ADJUST_KYC_FROM_BNPL = '43',
  CHANGE_PHONE_FROM_BNPL = 'bnpl',
  BNPL_ONBOARDING = '3',
  BNPL_NEW_SOURCE = '19',
}

export type VerifyProfile = {
  kyc_valid: boolean;
  issue_existed: boolean;
  issue_details?: {
    code: VerifyIssueCode;
    name: string;
    description: string;
  };
};

export enum VerifyIssueCode {
  OLD_KYC = 'OLD_KYC',
  OLD_IC = 'OLD_IC',
  ID_USED = 'ID_USED',
  ID_EXPIRED = 'ID_EXPIRED',
  AGE_NOT_ALLOWED = 'AGE_NOT_ALLOWED',
  NAME_WITHOUT_ACCENT = 'NAME_WITHOUT_ACCENT',
  LACK_OF_EKYC_INFO = 'LACK_OF_EKYC_INFO',
  INVALID_PROFILE = 'INVALID_PROFILE',
  INFO_USED = 'INFO_USED',
  NOT_LIVENESS_AND_ZPA = 'NOT_LIVENESS_AND_ZPA',
  TONE_ACCENT_INVALID = 'TONE_ACCENT_INVALID',
  WRONG_IMAGE_ORIENTATION = 'WRONG_IMAGE_ORIENTATION',
  LACK_OF_NFC_INFO = 'LACK_OF_NFC_INFO',
  NFC_NOT_VERIFIED = 'NFC_NOT_VERIFIED',
  INVALID_NFC = 'INVALID_NFC',
  NOT_ENOUGH_CREDIT_SCORE = 'NOT_ENOUGH_CREDIT_SCORE',
  LACK_OF_IC_IMAGE = 'LACK_OF_IC_IMAGE',
  INVALID_NFC_VERIFY_SOURCE = 'INVALID_NFC_VERIFY_SOURCE',
}

export type VerifyIssue = {
  title: string;
  description: string;
  cta: VerifyIssueCta[];
  code: string;
  image?: {
    source: ImageSource;
    width: number;
    height: number;
  };
};

export type VerifyIssueCta = {
  name: string;
  action:
    | 'update_kyc'
    | 'back'
    | 'continue'
    | 'use_full_name_suggest'
    | 'liveness_detection'
    | 'nfc'
    | 'retry'
    | 'reset_nfc'
    | 'credit_score'
    | 'close'
    | 'reset_nfc_new_source';
  variant: 'outlined' | 'contained' | 'naked';
};

export enum ServiceResponseSubcode {
  SUCCESS = 0,
  INVALID_USER_PROFILE = 105,
  TO0_MANY_REQUESTS = 401,
  INVALID_CORP_BALANCE = 508,
}

export type UTMParams = {
  utm_source?: string;
  utm_campaign?: string;
  utm_toast?: string;
  partner?:string;
};

export enum UtmCampaign {
  Repayment = 'repayment',
  AutoRepayBinding = 'auto_repay_binding',
  RenewOverdraft = 'renew_od',
  RenewOverdraftApproved = 'renew_od_approved',
  TransactionHistory = 'history',
  Account = 'account',
  BankRegister = 'bank_register',
  BankOwnershipVerify = 'bank-verify-ownership',
  UpdateNfc = 'update_nfc',
  InternalRedirectBod2 = 'internal_redirect_bod2',
  EmbeddedOnboarding = 'embedded_onboarding',
}

export enum ResourceState {
  INIT = -1,
  LOADING,
  READY,
  FAIL,
}

export type StateResource<T> = {
  data?: T;
  state: ResourceState;
};

export enum UTMParamsKey {
  UTM_SOURCE = 'utm_source',
  UTM_CAMPAIGN = 'utm_campaign',
  UTM_TOAST = 'utm_toast',
}

export type SourceOfFundsResponse = {
  sofs: SourceOfFund[];
};

export type SourceOfFund = {
  type: string;
  id: string;
  display_image: string;
  display_name: string;
  is_default: boolean;
  is_enabled: boolean;
};

export type AutoRepaymentBindingStatus = {
  status: 'active' | 'inactive';
  repay_option: RepayOptions;
};

export type RequestSourceOfFund = Omit<SourceOfFund, 'display_image' | 'is_default' | 'display_name'>;

export enum ExperimentName {
  LANDING_PAGE = 'landing_page',
  AUTO_REPAYMENT = 'auto_repayment',
  TODO_LIST = 'todo_list',
  RENEW_OVERDRAFT = 'renew_overdraft',
  UPDATE_RENEW_INFO = 'update_renew_info',
  REPAY_VIETQR = 'repay_vietqr',
  ONBOARD_VIETQR = 'onboard_vietqr',
  SUBMIT_UPDATE_NFC_RENEW = 'submit_update_nfc_renew',
  UPDATE_NFC = 'update_nfc',
  NEW_REPAYMENT_SCREEN = 'new_repayment_screen',
  INSTALLMENT_REGISTRATION = 'installment_registration',
  LOTTE_FREE_TRIAL = 'lotte_free_trial',
  CIMB_INFO_BANNER = 'cimb_info_banner',
}

export enum ABTestingGroup {
  Control_Group = 'Control Group',
  Variation_1 = 'Variation 1',
  Variation_2 = 'Variation 2',
}

export type PromptPinResult = {
  code: PROMPT_PIN_STATUS;
  message?: string;
};

export enum PROMPT_PIN_STATUS {
  SUCCESS = 1,
  FAILED = -1,
  REJECT = 0,
}

export type ApprovalAccountInfo = {
  bank_account_number: string;
  approved_limit: string;
  valid_from: string;
  title: string;
  approval_at: string;
};

export type ApprovalAccountInfoResp = {
  approval_infos: ApprovalAccountInfo[];
  bank_account_number: string;
  approved_limit: string;
  valid_from: string;
  title: string;
};

export type AdBanner = {
  id: number;
  title: string;
  banner_url: string;
  redirect_url: string;
};

export type AdBannerResource = {
  data?: AdBanner[];
  template: string;
  template_info: { width: number; height: number };
};

export type AdPopupButton = {
  name: string;
  zpiActionLink: string;
  zpaActionLink: string;
  zpiActionType: string;
  zpaActionType: string;
};

export type AdPopup = {
  popupContentId: number;
  title: string;
  description: string;
  background: string;
  mainButton: AdPopupButton;
  subButton: AdPopupButton;
  hasCloseButton: boolean;
};

export type AdPopupResource = {
  infos: { data: AdPopup; template: string }[];
};

export type SliderResource = {
  id: number;
  url: string;
  image: string;
};

export enum AdBannerTemplate {
  PRODUCT_PAGE_FULL_IMAGE_SLIDER = 'PRODUCT_PAGE_FULL_IMAGE_SLIDER',
  PRODUCT_PAGE_FULL_IMAGE_SLIDER_41 = 'PRODUCT_PAGE_FULL_IMAGE_SLIDER_41',
  PRODUCT_PAGE_MULTI_IMAGE_SLIDER = 'PRODUCT_PAGE_MULTI_IMAGE_SLIDER',
}

export enum HashPINResult {
  SUCCESS = 1,
  FAIL = 0,
}

export enum TimeUnit {
  SECONDS = 'seconds',
  MINUTES = 'minutes',
  HOURS = 'hours',
  DAYS = 'days',
  WEEKS = 'weeks',
  MONTHS = 'months',
  YEARS = 'years',
}

export enum LockAccountUseCase {
  UNSPECIFIED = 'UNSPECIFIED',
  RISK = 'RISK',
  CE = 'CE',
  UM = 'UM',
  FS_ADMIN = 'FS_ADMIN',
  USER = 'USER',
  PAY_LATER = 'PAY_LATER',
  PARTNER_DPD = 'PARTNER_DPD',
  PARTNER_FRAUD_DETECTION = 'PARTNER_FRAUD_DETECTION',
  PARTNER_LOCK = 'PARTNER_LOCK',
  ACCOUNT = 'ACCOUNT',
}

export type LockAction = {
  type: LockAccountAction;
  useCaseCode: string;
  order: number;
};

export enum LockAccountAction {
  PAY_DEBT,
  CONTACT_CIMB_SUPPORT,
  CONTACT_ZALOPAY_SUPPORT,
  PAY_DEBT_AND_CONTACT_CIMB_SUPPORT,
  UNLOCK_ACCOUNT,
}

export type LockInfo = {
  client_code: string;
  use_case_code: LockAccountUseCase;
  reason: string;
  updated_by: string;
};

export type LockAccountInfo = {
  is_lock: boolean;
  locks: LockInfo[];
};

export type DetailProfile = {
  full_name: string;
  request_id: string;
  birthday: string;
  id_issued_date: string;
  id_issued_location: string;
  permanent_address: string;
  temp_residence_address: string;
  gender: 'MALE' | 'FEMALE';
  occupation: string;
  employment_status: string;
  job_title: string;
  source_of_fund: string;
  monthly_income: string;
  education: string;
  living_city: string;
  loan_purpose: string;
};

export enum PredefinedCTA {
  RE_REGISTER = 'RE_REGISTER',
  CALL_BANK = 'CALL_BANK',
  BACK_HOMEPAGE = 'BACK_HOMEPAGE',
  RENEW = 'RENEW',
  RETRY = 'RETRY',
  CALL_LFVN = 'CALL_LFVN',
  CONTACT_ZALOPAY_CS = 'CONTACT_ZALOPAY_CS',
  REDIRECT_KYC_PROCESS = 'REDIRECT_KYC_PROCESS',
  RESTART = 'RESTART',
  REDIRECT_CREATE_KYC_PROCESS = 'REDIRECT_CREATE_KYC_PROCESS',
  REDIRECT_FACE_AUTHENTICATE_PROCESS = 'REDIRECT_FACE_AUTHENTICATE_PROCESS',
  REDIRECT_NFC_PROCESS = 'REDIRECT_NFC_PROCESS',
  REDIRECT_NFC_NEW_SOURCE_PROCESS = 'REDIRECT_NFC_NEW_SOURCE_PROCESS',
  RESET_NFC = 'RESET_NFC',
  REDIRECT_INCREASE_CREDIT_SCORE = 'REDIRECT_INCREASE_CREDIT_SCORE',
  RESET_NFC_NEW_SOURCE = 'RESET_NFC_NEW_SOURCE',
  REDIRECT_OAO = 'REDIRECT_OAO',
}

export enum RepayOptions {
  MIN = 'MIN_STM_AMOUNT',
  FULL = 'FULL_STM_AMOUNT',
}

export enum AdsInventoryId {
  BANNER_SLIDER = 'Product_TKTS',
  BANNER_SLIDER_LOTTE = 'Product_TKTS_LFVN',
  HEADER_SLIDER = 'Product_TKTS_Header',
  HEADER_SLIDER_LOTTE = 'Product_TKTS_Header_LFVN',
  POPUP_HOME = 'Popup_TKTS_Home',
  POPUP_HOME_LOTTE = 'Popup_TKTS_Home_LFVN',
  LANDING_PAGE = 'paylater_landingpage',
  BOD_2 = 'paylater_bod2',
  LANDING_PAGE_LOTTE = 'paylater_lfvn_landingpage',
  BOD_2_LOTTE = 'paylater_lfvn_bod2',
  ASSET_INVENTORY_ID_CIMB = 'Paylater_Asset_CIMB',
  ASSET_INVENTORY_ID_LOTTE = 'Paylater_Asset_LFVN',
  RENEW_APPLICATION = 'paylater_cimb_renew_application',
  RENEW_FACE_AUTHEN = 'paylater_cimb_renew_face_authen',
}

export enum RiskAction {
  FACE_CHALLENGE = 'FACE_CHALLENGE',
  UPDATE_KYC = 'UPDATE_KYC',
  CONTACT_CS = 'CONTACT_CS',
  REJECT = 'REJECT',
}

export enum NativeFeature {
  NewSurveyDeeplink = 'NewSurveyDeeplink',
  PromptPinHash = 'PromptPinHash',
  CalendarSDK = 'CalendarSDK',
  SwipeBackIOS = 'SwipeBackIOS',
}

export enum EventTrackingId {
  RepaymentScreen = '6719',
  HomeScreen = '6718',
  HomeUnbindScreen = '6715',
  HomePreviewScreen = '6714',
  ErrorScreen = '6728',
  FaqScreen = '6725',
  ProductInfoScreen = '6726',
  LockoutScreen = '6717',
  ResubmitOnboardingScreen = '6727',
  TransactionDetailScreen = '6722',
  TransactionHistoryScreen = '6721',
  UserGuideScreen = '6724',
  SubmitOtpScreen = '6712',
  AfterSubmitOtpModal = '6713',
  OnboardingScreen = '6700',
  OnboardingChurnModal = '6702',
  OnboardingResultModal = '6705',
  SignSubmitScreen = '6711',
  AfterBod2ReadyToSign = '6709',
  AfterBod2Rejected = '6710',
  Bod2ChurningModal = '6702',
  Bod2Screen = '6706',
  Bod2SubmitErrorModal = '6707',
  Bod2SubmitResultModal = '6708',
  ApplicationReviewScreen = '6716',
  RenewInfoModal = '6730',
  RenewSignSubmitScreen = '6731',
  RenewOTPVerifyScreen = '6732',
  RenewBannerStatus = '6733',
  UtilitySection = '6734',
  FaceChallengeScreen = '6735',
  RenewFaceChallengeScreen = '6736',
  NonWhitelistOnboardingScreen = '6737',
  UpdateNfcDataModal = '6738',
  SplashScreen = '6740',
  EmbeddedOnboardingWaitingApprovalScreen = '6741',
}

export type EdgeCaseInfo = {
  title?: string;
  description?: string;
  ctas?: PredefinedCTA[];
};

export enum AutoRepayBindingVersion {
  AS_MUCH_AS_POSSIBLE = 'REPAYMENT_AS_MUCH_AS_POSSIBLE',
}

export const AUTO_PLAY_DELAY = 3000;

export enum DebtRepayType {
  FIXED = 'fixed',
  INPUT = 'input',
  REPAID = 'repaid',
}

export enum DebtRepaySelectionId {
  MAX = 'max_repayment',
  MIN = 'min_repayment',
  INPUT = 'input_amount',
  OD_OUTSTANDING = 'od_outstanding',
}

export type TypeValueObj = {
  type: string;
  value: string;
};

export type DebtRepayMetadataRaw = {
  title?: TypeValueObj;
  display_amount?: TypeValueObj;
  repay_value?: TypeValueObj;
  message?: TypeValueObj;
  fee_amount?: TypeValueObj;
  min_amount?: TypeValueObj;
  description?: TypeValueObj;
};

export type DebtRepayMetadata = {
  title: string;
  display_amount: string;
  repay_value: string;
  message: string;
  fee_amount: string;
  min_amount: string;
  description: string;
};

export type DebtRepayOptions<MetadataType> = {
  selection_id: string;
  type: DebtRepayType;
  metadata: MetadataType;
};

export type DebtObligationResp = {
  zalopay_id: string;
  partner_code: PartnerCode;
  repayment_options: DebtRepayOptions<DebtRepayMetadataRaw>[];
  service_fee?: string;
  info_message?: InfoMessageType;
};

export type DebtObligation = {
  zalopay_id: string;
  partner_code: PartnerCode;
  repayment_options: DebtRepayOptions<DebtRepayMetadata>[];
  service_fee?: string;
  info_message?: InfoMessageType;
};

export type InfoMessageType = {
  message: string;
  type: MessageType;
};

export enum MessageType {
  WARNING = 'WARNING',
  SUCCESS = 'SUCCESS',
  ERROR = 'ERROR',
  INFO = 'INFO',
  UNKNOWN = 'UNKNOWN',
}

export type UserBalanceResp = {
  total_limit: number;
  available_balance: number;
  over_repayment_balance: number;
  outstanding_balance: number;
  user_fee: number;
  user_fee_date: number;
  maintenance_fee: number;
  maintenance_fee_date: number;
  user_service_fee: number;
};

export type DetailProfileResp = {
  partner_account_exist: boolean;
  issue_existed?: boolean;
  issue_details?: {
    code: VerifyIssueCode;
    name: string;
    description: string;
  };
  edge_case_info?: EdgeCaseInfo;
};

export type DetailProfileRequest = {
  full_name: string;
  request_id: string;
  birthday: string;
  id_issued_date: string;
  id_issued_location: string;
  permanent_address: string;
  temp_residence_address: string;
  gender: string;
  occupation: string;
  employment_status: string;
  job_title: string;
  source_of_fund: string;
  monthly_income: string;
  education: string;
  living_city: string;
  loan_purpose: string;
  agreements?: {
    installment_registration?: boolean;
  };
};

export type LatestSignature = {
  has_valid_signature: boolean;
  signature_zpa_url: string;
  signature_zpi_url: string;
};

export type SuggestPrioritySOFConfig = {
  enable: boolean;
  title: string;
  message: string;
  action: string;
  image: string;
  hidden_time_in_hour: number;
};

export type UtilityConfig = {
  title: string;
  message: string;
  action: string;
  image: string;
  onAction: () => void;
};

export type TabConfig = {
  config: TabItemConfig[]; //configs to render tabbar
  active: string; //indicate active tab by screenKey
  indicator?: boolean;
  activeColor?: string;
  inactiveColor?: string;
  tabbarHeight?: number;
};

export type TabItemConfig = {
  id: string | number;
  title: string;
  path: string;
  matchPath: string[];
  icon: { src: ImageSource; size: number; srcActive: ImageSource };
};

export type ResourceType =
  | 'OCCUPATION'
  | 'EMPLOYMENT_STATUS'
  | 'JOB_TITLE'
  | 'SOURCE_OF_FUND'
  | 'EDUCATION'
  | 'CITY'
  | 'INCOME'
  | 'LOAN_PURPOSE'
  | 'STATEMENT_DATE';

export type Resource = {
  type: ResourceType;
  data: { code: string; vietnamese: string }[];
};

export type ResourceResponse = {
  resources: Resource[];
};

export type Item = { id: string; value: string };

export type SubmitRepayRequest = {
  amount: number;
  account_id?: string;
  generate_qr_code?: boolean;
  service_fee?: number;
  callback?: (arg?: any[]) => void;
};

export type SubmitSuccess = {
  transaction_id: string;
  zp_trans_token: string;
  app_id: number;
  app_trans_id: string;
  user_id: number;
  qr_code?: string;
};

export type RepayMethod = {
  id: RepayMethodId;
  title: string;
  icon: string;
  description: {
    text: string;
    icon: 'check' | 'cross' | undefined;
  }[];
};

export enum Gender {
  MALE = 1,
  FEMALE = 2,
}

export type UserKycInfo = {
  display_name: string;
  zalopay_id: string;
  gender: Gender;
  profile_level: number;
  birth_day: string;
  phone_number: string;
  email: string;
  zalo_id: string;
  avatar: string;
};

export type BalanceConditions = {
  isLoading: boolean;
  isInvalid: boolean;
  hasOutstanding: boolean;
  hasServiceFee?: boolean;
};

/**
 * Stm = Statement
 * Conditions that defined from statement data
 */
export type CIMBStatementConditions = {
  minPayIsRepaid: boolean;
  isOverDue: boolean;
  isOnDue: boolean;
  isBeforeDue: boolean;
  isRepaid: boolean;
  noSpendInPeriod: boolean;
  isInvalid: boolean;
  hasDebt: boolean;
  isLoading: boolean;
};

export type LotteStatementConditions = {
  isOverDue: boolean;
  isOnDue: boolean;
  isBeforeDue: boolean;
  isRepaid: boolean; // [10/2024] Lotte statement provide repaid field
  noSpendInPeriod: boolean;
  isInvalid: boolean;
  hasDebt: boolean;
  isLoading: boolean;
};

export type AuthChallengeResult = {
  status: UMStatus;
  resultData: string;
};

export enum UMStatus {
  Invalid = -1,
  Cancel = 0,
  Success = 1,
  Fail = 2,
  Pending = 3,
}

export enum AuthChallengeType {
  Specified = 0,
  PIN = 1,
  Bio = 2,
  PinBio = 3,
  FaceAuth = 4,
  KYC = 5,
  Adjust = 6,
  NFC = 7,
  KYC_NFC = 8,
  Adjust_NFC = 9,
  ResetNFC = 14,
}

export enum SignMethod {
  PIN = 'pin',
  FACE = 'PARTNER_SELFIE',
}

export type InitSignContractResp = {
  authentication_type: SignMethod;
};

export type MerchantConfig = {
  name: string;
  reject_url: string;
  approve_url: string;
  icon: string;
  ad_id: string;
  approve_message: string;
};

export type MerchantConfigs = {
  [key: string]: MerchantConfig;
};

export type RewardBannerConfigs = {
  [key: string]: {
    enable: boolean;
    onboarding?: RewardBannerAdConfigs;
    bod2?: RewardBannerAdConfigs;
    renew_application?: RewardBannerAdConfigs;
    renew_face_authen?: RewardBannerAdConfigs;
  };
};
export type RewardBannerAdConfigs = {
  ad_id: string;
  content_part_1: string;
  content_part_2: string;
  content_part_3: string;
  cta: string;
  background: string;
  animation: string;
};

export enum RewardBannerKey {
  Onboarding = 'onboarding',
  Bod2 = 'bod2',
  RenewApplication = 'renew_application',
  RenewFaceAuthen = 'renew_face_authen',
}

export type RewardVoucherConfigs = {
  [key: string]: string;
};

export enum CashierResultStatus {
  Fail = 0,
  Sucess = 1,
  Processing = 2,
}

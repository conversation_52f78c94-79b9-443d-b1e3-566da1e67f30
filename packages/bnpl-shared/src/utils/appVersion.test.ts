import { normalizeVersion, compareVersion } from './appVersion';

describe(normalizeVersion.name, () => {
  describe('normalizeVersion', () => {
    it('should remove v prefix', () => {
      expect(normalizeVersion('v1.2.3')).toBe('1.2.3');
      expect(normalizeVersion('V1.2.3')).toBe('1.2.3');
    });

    it('should remove v prefix', () => {
      expect(normalizeVersion('v1.2.3')).toBe('1.2.3');
      expect(normalizeVersion('V1.2.3')).toBe('1.2.3');
    });

    it('should trim whitespace', () => {
      expect(normalizeVersion(' 1.2.3 ')).toBe('1.2.3');
      expect(normalizeVersion('  1.2.3  ')).toBe('1.2.3');
    });

    it('should handle version without prefix', () => {
      expect(normalizeVersion('1.2.3')).toBe('1.2.3');
    });
  });

  describe(compareVersion.name, () => {
    it('should return 0 for equal versions', () => {
      expect(compareVersion('1.2.3', '1.2.3')).toBe(0);
      expect(compareVersion('v1.2.3', '1.2.3')).toBe(0);
      expect(compareVersion('1.2.3', 'V1.2.3')).toBe(0);
    });

    it('should return positive number when first version is greater', () => {
      expect(compareVersion('2.0.0', '1.9.9')).toBeGreaterThan(0);
      expect(compareVersion('1.3.0', '1.2.9')).toBeGreaterThan(0);
      expect(compareVersion('1.2.4', '1.2.3')).toBeGreaterThan(0);
      expect(compareVersion('2.0.0', '1.999.999')).toBeGreaterThan(0);
    });

    it('should return negative number when first version is less', () => {
      expect(compareVersion('1.9.9', '2.0.0')).toBeLessThan(0);
      expect(compareVersion('1.2.9', '1.3.0')).toBeLessThan(0);
      expect(compareVersion('1.2.3', '1.2.4')).toBeLessThan(0);
      expect(compareVersion('1.999.999', '2.0.0')).toBeLessThan(0);
    });

    it('should handle versions with different number of parts', () => {
      expect(compareVersion('1.2', '1.2.0')).toBe(0);
      expect(compareVersion('1.2', '1.2.1')).toBeLessThan(0);
      expect(compareVersion('1.2.1', '1.2')).toBeGreaterThan(0);
      expect(compareVersion('2', '1.9.9')).toBeGreaterThan(0);
      expect(compareVersion('1', '1.0.0')).toBe(0);
    });

    it('should handle single digit versions', () => {
      expect(compareVersion('1', '2')).toBeLessThan(0);
      expect(compareVersion('2', '1')).toBeGreaterThan(0);
      expect(compareVersion('1', '1')).toBe(0);
      expect(compareVersion('10', '9')).toBeGreaterThan(0);
    });

    it('should handle leading zeros', () => {
      expect(compareVersion('1.01.0', '1.1.0')).toBe(0);
      expect(compareVersion('1.02.0', '1.1.0')).toBeGreaterThan(0);
      expect(compareVersion('1.00.1', '1.0.1')).toBe(0);
      expect(compareVersion('01.02.03', '1.2.3')).toBe(0);
    });

    it('should handle complex version comparisons', () => {
      expect(compareVersion('10.2.3', '9.10.20')).toBeGreaterThan(0);
      expect(compareVersion('2.1.0', '2.0.9')).toBeGreaterThan(0);
      expect(compareVersion('1.10.0', '1.9.0')).toBeGreaterThan(0);
      expect(compareVersion('1.10.0', '1.2.0')).toBeGreaterThan(0);
    });

    it('should handle v prefixes correctly', () => {
      expect(compareVersion('v2.1.0', 'V2.0.0')).toBeGreaterThan(0);
      expect(compareVersion('v2.1.0', 'V2.1.0')).toBe(0);
      expect(compareVersion('v2.1.0', '2.1.0')).toBe(0);
    });

    it('should handle whitespace in versions', () => {
      expect(compareVersion(' 2.1.0 ', '2.1.0')).toBe(0);
      expect(compareVersion(' 2.1.0 ', ' 2.0.0 ')).toBeGreaterThan(0);
    });

    it('should throw error for invalid version format', () => {
      expect(() => compareVersion('1.2.abc', '1.2.3')).toThrow('Invalid version format');
      expect(() => compareVersion('1.2.3', 'invalid')).toThrow('Invalid version format');
      expect(() => compareVersion('abc.def.ghi', '1.2.3')).toThrow('Invalid version format');
      expect(() => compareVersion('1.2.3', 'x.y.z')).toThrow('Invalid version format');
    });

    it('should handle edge cases', () => {
      expect(compareVersion('0.0.0', '0.0.0')).toBe(0);
      expect(compareVersion('0.0.1', '0.0.0')).toBeGreaterThan(0);
      expect(compareVersion('0.1.0', '0.0.9')).toBeGreaterThan(0);
      expect(compareVersion('0.0.0', '0.0.1')).toBeLessThan(0);
    });

    it('should handle versions with many parts', () => {
      expect(compareVersion('1.2.3.4.5', '1.2.3.4.4')).toBeGreaterThan(0);
      expect(compareVersion('1.2.3.4', '1.2.3.4.0')).toBe(0);
      expect(compareVersion('1.2.3', '1.2.3.0.0.1')).toBeLessThan(0);
      expect(compareVersion('1.2.3.4.5.6', '1.2.3.4.5.6')).toBe(0);
    });

    it('should handle basic version patterns', () => {
      expect(compareVersion('2.0.0', '1.9.9')).toBeGreaterThan(0);
      expect(compareVersion('1.0.0', '0.9.9')).toBeGreaterThan(0);
      expect(compareVersion('1.1.0', '1.0.9')).toBeGreaterThan(0);
    });
  });
});

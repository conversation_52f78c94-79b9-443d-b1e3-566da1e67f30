import { store } from '../redux/store';

export function normalizeVersion(version: string): string {
  return version.replace(/^(v|V|version\s*)/i, '').trim();
}

export function compareVersion(version1: string, version2: string) {
  const v1 = normalizeVersion(version1).split('.');
  const v2 = normalizeVersion(version2).split('.');
  const maxParts = Math.max(v1.length, v2.length);

  for (let i = 0; i < maxParts; i++) {
    const num1 = Number(v1[i] || 0);
    const num2 = Number(v2[i] || 0);

    if (isNaN(num1) || isNaN(num2)) {
      throw new Error(`Invalid version format: "${version1}" or "${version2}"`);
    }

    const diff = num1 - num2;
    if (diff !== 0) {
      return diff;
    }
  }
  return 0;
}

export function isAppVersionGreaterThan(version: string) {
  const appVersion = store.getState().appInfo?.appVersion;
  return appVersion && compareVersion(appVersion, version) > 0;
}

export function isAppVersionGreaterThanOrEqual(version: string) {
  const appVersion = store.getState().appInfo?.appVersion;
  return appVersion && compareVersion(appVersion, version) >= 0;
}

export function isAppVersionLessThan(version: string) {
  const appVersion = store.getState().appInfo?.appVersion;
  return appVersion && compareVersion(appVersion, version) < 0;
}

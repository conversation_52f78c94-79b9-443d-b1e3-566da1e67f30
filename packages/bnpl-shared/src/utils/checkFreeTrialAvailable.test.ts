import { checkFreeTrialAvailable } from './checkFreeTrialAvailable';
import { AccountBenefit, AccountInfo } from "bnpl-shared/src/types";

describe(checkFreeTrialAvailable.name, () => {
  // Use dynamic dates relative to a fixed point in time for consistent CI/CD behavior
  const mockCurrentTime = new Date('2024-06-15T12:00:00Z'); // Fixed mock current time
  const mockStartTime = new Date(mockCurrentTime.getTime() - 30 * 24 * 60 * 60 * 1000); // 30 days before
  const mockEndTime = new Date(mockCurrentTime.getTime() + 30 * 24 * 60 * 60 * 1000); // 30 days after

  let originalDate: DateConstructor;

  beforeAll(() => {
    originalDate = global.Date;
    // Mock the Date constructor completely
    (global as any).Date = class extends originalDate {
      constructor(dateValue?: any) {
        if (arguments.length === 0) {
          // new Date() - return mock current time
          super(mockCurrentTime.getTime());
        } else {
          // new Date(dateString) or new Date(timestamp) - use original behavior
          super(dateValue);
        }
      }

      static UTC = originalDate.UTC;
      static parse = originalDate.parse;
      static now = () => mockCurrentTime.getTime();
    };
  });

  afterAll(() => {
    global.Date = originalDate;
  });

  describe('when accountInfo is null or undefined', () => {
    it('should return false with BENEFIT_NOT_FOUND reason for undefined accountInfo', () => {
      const result = checkFreeTrialAvailable(undefined);
      expect(result).toEqual({
        available: false,
        reason: 'BENEFIT_NOT_FOUND',
      });
    });

    it('should return false with BENEFIT_NOT_FOUND reason for null accountInfo', () => {
      const result = checkFreeTrialAvailable(null as any);
      expect(result).toEqual({
        available: false,
        reason: 'BENEFIT_NOT_FOUND',
      });
    });
  });

  describe('when accountInfo has no benefits', () => {
    it('should return false with BENEFIT_NOT_FOUND reason for empty benefits array', () => {
      const accountInfo: AccountInfo = {
        id: '123',
        zalopay_id: '123',
        partner_code: 'LFVN',
        status: 'ACTIVE' as any,
        total_limit: '100000',
        created_at: '1',
        statement_date: '1',
        statement_grace_full_date: '1',
        benefits: [],
      };

      const result = checkFreeTrialAvailable(accountInfo);
      expect(result).toEqual({
        available: false,
        reason: 'BENEFIT_NOT_FOUND',
      });
    });

    it('should return false with BENEFIT_NOT_FOUND reason for undefined benefits', () => {
      const accountInfo: AccountInfo = {
        id: '123',
        zalopay_id: '123',
        partner_code: 'LFVN',
        status: 'ACTIVE' as any,
        total_limit: '100000',
        created_at: '1',
        statement_date: '1',
        statement_grace_full_date: '1',
        benefits: undefined as any,
      };

      const result = checkFreeTrialAvailable(accountInfo);
      expect(result).toEqual({
        available: false,
        reason: 'BENEFIT_NOT_FOUND',
      });
    });
  });

  describe('when free trial benefit is not found', () => {
    it('should return false with BENEFIT_NOT_FOUND reason when no free trial benefit exists', () => {
      const accountInfo: AccountInfo = {
        id: '123',
        zalopay_id: '123',
        partner_code: 'LFVN',
        status: 'ACTIVE' as any,
        total_limit: '100000',
        created_at: '1',
        statement_date: '1',
        statement_grace_full_date: '1',
        benefits: [
          {
            code: 'other_benefit' as AccountBenefit,
            start_time: '2024-01-01T00:00:00Z',
            end_time: '2024-01-31T23:59:59Z',
            description: 'Other benefit',
          },
        ],
      };

      const result = checkFreeTrialAvailable(accountInfo);
      expect(result).toEqual({
        available: false,
        reason: 'BENEFIT_NOT_FOUND',
      });
    });
  });

  describe('when free trial benefit exists but time validation fails', () => {
    it('should return false with NOT_IN_TIME reason when current time is before start time', () => {
      const futureStartTime = new Date(mockCurrentTime.getTime() + 7 * 24 * 60 * 60 * 1000); // 7 days after current
      const futureEndTime = new Date(mockCurrentTime.getTime() + 37 * 24 * 60 * 60 * 1000); // 37 days after current

      const accountInfo: AccountInfo = {
        id: '123',
        zalopay_id: '123',
        partner_code: 'LFVN',
        status: 'ACTIVE' as any,
        total_limit: '100000',
        created_at: '1',
        statement_date: '1',
        statement_grace_full_date: '1',
        benefits: [
          {
            code: AccountBenefit.FREE_TRIAL,
            start_time: futureStartTime.toISOString(),
            end_time: futureEndTime.toISOString(),
            description: 'Free trial benefit',
          },
        ],
      };

      const result = checkFreeTrialAvailable(accountInfo);
      expect(result).toEqual({
        available: false,
        reason: 'NOT_IN_TIME',
      });
    });

    it('should return false with NOT_IN_TIME reason when current time is after end time', () => {
      const pastStartTime = new Date(mockCurrentTime.getTime() - 60 * 24 * 60 * 60 * 1000); // 60 days before current
      const pastEndTime = new Date(mockCurrentTime.getTime() - 30 * 24 * 60 * 60 * 1000); // 30 days before current

      const accountInfo: AccountInfo = {
        id: '123',
        zalopay_id: '123',
        partner_code: 'LFVN',
        status: 'ACTIVE' as any,
        total_limit: '100000',
        created_at: '1',
        statement_date: '1',
        statement_grace_full_date: '1',
        benefits: [
          {
            code: AccountBenefit.FREE_TRIAL,
            start_time: pastStartTime.toISOString(),
            end_time: pastEndTime.toISOString(),
            description: 'Free trial benefit',
          },
        ],
      };

      const result = checkFreeTrialAvailable(accountInfo);
      expect(result).toEqual({
        available: false,
        reason: 'NOT_IN_TIME',
      });
    });
  });

  describe('when free trial is available', () => {
    it('should return true with benefit details when current time is within the valid period', () => {
      const accountInfo: AccountInfo = {
        id: '123',
        zalopay_id: '123',
        partner_code: 'LFVN',
        status: 'ACTIVE' as any,
        total_limit: '100000',
        created_at: '1',
        statement_date: '1',
        statement_grace_full_date: '1',
        benefits: [
          {
            code: AccountBenefit.FREE_TRIAL,
            start_time: mockStartTime.toISOString(),
            end_time: mockEndTime.toISOString(),
            description: 'Free trial benefit',
          },
        ],
      };

      const result = checkFreeTrialAvailable(accountInfo);
      expect(result).toEqual({
        available: true,
        benefit: {
          code: AccountBenefit.FREE_TRIAL,
          startTime: mockStartTime,
          endTime: mockEndTime,
          description: 'Free trial benefit',
        },
      });
    });

    it('should return true when current time equals start time', () => {
      // Temporarily override Date for this test
      (global as any).Date = class extends originalDate {
        constructor(dateValue?: any) {
          if (arguments.length === 0) {
            super(mockStartTime.getTime());
          } else {
            super(dateValue);
          }
        }

        static UTC = originalDate.UTC;
        static parse = originalDate.parse;
        static now = () => mockStartTime.getTime();
      };

      const accountInfo: AccountInfo = {
        id: '123',
        zalopay_id: '123',
        partner_code: 'LFVN',
        status: 'ACTIVE' as any,
        total_limit: '100000',
        created_at: '1',
        statement_date: '1',
        statement_grace_full_date: '1',
        benefits: [
          {
            code: AccountBenefit.FREE_TRIAL,
            start_time: mockStartTime.toISOString(),
            end_time: mockEndTime.toISOString(),
            description: 'Free trial benefit',
          },
        ],
      };

      const result = checkFreeTrialAvailable(accountInfo);
      expect(result).toEqual({
        available: true,
        benefit: {
          code: AccountBenefit.FREE_TRIAL,
          startTime: mockStartTime,
          endTime: mockEndTime,
          description: 'Free trial benefit',
        },
      });

      // Reset to mock current time for other tests
      (global as any).Date = class extends originalDate {
        constructor(dateValue?: any) {
          if (arguments.length === 0) {
            super(mockCurrentTime.getTime());
          } else {
            super(dateValue);
          }
        }

        static UTC = originalDate.UTC;
        static parse = originalDate.parse;
        static now = () => mockCurrentTime.getTime();
      };
    });

    it('should return true when current time equals end time', () => {
      // Temporarily override Date for this test
      (global as any).Date = class extends originalDate {
        constructor(dateValue?: any) {
          if (arguments.length === 0) {
            super(mockEndTime.getTime());
          } else {
            super(dateValue);
          }
        }

        static UTC = originalDate.UTC;
        static parse = originalDate.parse;
        static now = () => mockEndTime.getTime();
      };

      const accountInfo: AccountInfo = {
        id: '123',
        zalopay_id: '123',
        partner_code: 'LFVN',
        status: 'ACTIVE' as any,
        total_limit: '100000',
        created_at: '1',
        statement_date: '1',
        statement_grace_full_date: '1',
        benefits: [
          {
            code: AccountBenefit.FREE_TRIAL,
            start_time: mockStartTime.toISOString(),
            end_time: mockEndTime.toISOString(),
            description: 'Free trial benefit',
          },
        ],
      };

      const result = checkFreeTrialAvailable(accountInfo);
      expect(result).toEqual({
        available: true,
        benefit: {
          code: AccountBenefit.FREE_TRIAL,
          startTime: mockStartTime,
          endTime: mockEndTime,
          description: 'Free trial benefit',
        },
      });

      // Reset to mock current time for other tests
      (global as any).Date = class extends originalDate {
        constructor(dateValue?: any) {
          if (arguments.length === 0) {
            super(mockCurrentTime.getTime());
          } else {
            super(dateValue);
          }
        }

        static UTC = originalDate.UTC;
        static parse = originalDate.parse;
        static now = () => mockCurrentTime.getTime();
      };
    });
  });

  describe('parseDate function behavior', () => {
    it('should handle numeric timestamps correctly', () => {
      const testStartTime = new Date(mockCurrentTime.getTime() - 15 * 24 * 60 * 60 * 1000); // 15 days before current
      const testEndTime = new Date(mockCurrentTime.getTime() + 15 * 24 * 60 * 60 * 1000); // 15 days after current
      const numericTimestamp = testStartTime.getTime().toString();
      const expectedDate = new Date(testStartTime.getTime());

      const accountInfo: AccountInfo = {
        id: '123',
        zalopay_id: '123',
        partner_code: 'LFVN',
        status: 'ACTIVE' as any,
        total_limit: '100000',
        created_at: '1',
        statement_date: '1',
        statement_grace_full_date: '1',
        benefits: [
          {
            code: AccountBenefit.FREE_TRIAL,
            start_time: numericTimestamp,
            end_time: testEndTime.getTime().toString(),
            description: 'Free trial benefit',
          },
        ],
      };

      const result = checkFreeTrialAvailable(accountInfo);
      expect(result.available).toBe(true);
      if (result.available) {
        expect(result.benefit.startTime).toEqual(expectedDate);
      }
    });

    it('should handle ISO string dates correctly', () => {
      const testStartTime = new Date(mockCurrentTime.getTime() - 10 * 24 * 60 * 60 * 1000); // 10 days before current
      const testEndTime = new Date(mockCurrentTime.getTime() + 10 * 24 * 60 * 60 * 1000); // 10 days after current
      const isoString = testStartTime.toISOString();
      const expectedDate = new Date(isoString);

      const accountInfo: AccountInfo = {
        id: '123',
        zalopay_id: '123',
        partner_code: 'LFVN',
        status: 'ACTIVE' as any,
        total_limit: '100000',
        created_at: '1',
        statement_date: '1',
        statement_grace_full_date: '1',
        benefits: [
          {
            code: AccountBenefit.FREE_TRIAL,
            start_time: isoString,
            end_time: testEndTime.toISOString(),
            description: 'Free trial benefit',
          },
        ],
      };

      const result = checkFreeTrialAvailable(accountInfo);
      expect(result.available).toBe(true);
      if (result.available) {
        expect(result.benefit.startTime).toEqual(expectedDate);
      }
    });
  });

  describe('multiple benefits scenario', () => {
    it('should find free trial benefit among multiple benefits', () => {
      const accountInfo: AccountInfo = {
        id: '123',
        zalopay_id: '123',
        partner_code: 'LFVN',
        status: 'ACTIVE' as any,
        total_limit: '100000',
        created_at: '1',
        statement_date: '1',
        statement_grace_full_date: '1',
        benefits: [
          {
            code: 'other_benefit_1' as AccountBenefit,
            start_time: mockStartTime.toISOString(),
            end_time: mockEndTime.toISOString(),
            description: 'Other benefit 1',
          },
          {
            code: AccountBenefit.FREE_TRIAL,
            start_time: mockStartTime.toISOString(),
            end_time: mockEndTime.toISOString(),
            description: 'Free trial benefit',
          },
          {
            code: 'other_benefit_2' as AccountBenefit,
            start_time: mockStartTime.toISOString(),
            end_time: mockEndTime.toISOString(),
            description: 'Other benefit 2',
          },
        ],
      };

      const result = checkFreeTrialAvailable(accountInfo);
      expect(result).toEqual({
        available: true,
        benefit: {
          code: AccountBenefit.FREE_TRIAL,
          startTime: mockStartTime,
          endTime: mockEndTime,
          description: 'Free trial benefit',
        },
      });
    });
  });
}); 

import { AccountBenefit, AccountInfo } from "bnpl-shared/src/types";

export function checkFreeTrialAvailable(accountInfo?: AccountInfo): FreeTrialResult {
  if (!accountInfo?.benefits?.length) {
    return { available: false, reason: 'BENEFIT_NOT_FOUND' };
  }
  const freeTrialBenefit = accountInfo.benefits.find(benefit => benefit.code === AccountBenefit.FREE_TRIAL);
  if (!freeTrialBenefit) {
    return { available: false, reason: 'BENEFIT_NOT_FOUND' };
  }
  const now = new Date();
  const startTime = parseDate(freeTrialBenefit.start_time);
  const endTime = parseDate(freeTrialBenefit.end_time);

  if (now < startTime || now > endTime) {
    return { available: false, reason: 'NOT_IN_TIME' };
  }

  return {
    available: true,
    benefit: {
      code: freeTrialBenefit.code,
      startTime: startTime,
      endTime: endTime,
      description: freeTrialBenefit.description,
    },
  };
}

type FreeTrialResult = {
  available: true;
  benefit: {
    code: string;
    startTime: Date;
    endTime: Date;
    description: string;
  };
} | {
  available: false;
  reason: 'BENEFIT_NOT_FOUND' | 'NOT_IN_TIME' | 'NOT_STARTED';
}

export function parseDate(date: string): Date {
  if (isNaN(Number(date))) {
    return new Date(date);
  }
  return new Date(Number(date));
}

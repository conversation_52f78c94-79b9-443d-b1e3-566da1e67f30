export function createFakeElement(value: string) {
  const isRTL = document.documentElement.getAttribute('dir') === 'rtl';
  const fakeElement = document.createElement('textarea');
  // Prevent zooming on iOS
  fakeElement.style.fontSize = '12pt';
  // Reset box model
  fakeElement.style.border = '0';
  fakeElement.style.padding = '0';
  fakeElement.style.margin = '0';
  // Move element out of screen horizontally
  fakeElement.style.position = 'absolute';
  fakeElement.style[isRTL ? 'right' : 'left'] = '-9999px';
  // Move element to the same position vertically
  let yPosition = window.pageYOffset || document.documentElement.scrollTop;
  fakeElement.style.top = `${yPosition}px`;

  fakeElement.setAttribute('readonly', '');
  fakeElement.value = value;

  return fakeElement;
}

export function command(type: string): boolean {
  try {
    return document.execCommand(type);
  } catch (err) {
    return false;
  }
}

export function select(element: HTMLTextAreaElement): string {
  let selectedText: string;

  const isReadOnly = element.hasAttribute('readonly');

  if (!isReadOnly) {
    element.setAttribute('readonly', '');
  }

  element.select();
  element.setSelectionRange(0, element.value.length);

  if (!isReadOnly) {
    element.removeAttribute('readonly');
  }

  selectedText = element.value;

  return selectedText;
}

const ClipboardActionCopy = (target: string, options = { container: document.body }) => {
  let selectedText = '';
  const fakeElement = createFakeElement(target);
  options.container.appendChild(fakeElement);
  selectedText = select(fakeElement);
  command('copy');
  fakeElement.remove();
  return selectedText;
};

export default ClipboardActionCopy;

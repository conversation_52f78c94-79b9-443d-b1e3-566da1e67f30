import ClipboardActionCopy from './clipboardActionCopy';

function canUseClipboard(): Promise<boolean> {
  if (navigator.permissions && navigator.clipboard) {
    return navigator.permissions
      .query({ name: 'clipboard-write' as PermissionName })
      .then(result => {
        return result.state === 'granted';
      })
      .catch(err => false);
  }

  return Promise.resolve(false);
}

export function writeText(data: string): Promise<void> {
  return canUseClipboard().then(isAvailable => {
    // Clipboard API available
    if (isAvailable) {
      return navigator.clipboard.writeText(data);
    }
    // Old way - Borrow from https://github.com/zenorocha/clipboard.js/blob/master/src/actions/copy.js
    ClipboardActionCopy(data);
    return Promise.resolve();
  });
}

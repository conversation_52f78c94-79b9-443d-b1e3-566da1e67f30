import { ExperimentName } from 'bnpl-shared/src/types';
import { Environment, environment } from 'bnpl-shared/src/shared/environment';

export const getABTestExperimentKeyByName = (name: string): string => {
  switch (name) {
    case ExperimentName.AUTO_REPAYMENT:
      return getAutoRepaymentExperimentKey();
    case ExperimentName.LANDING_PAGE:
      return getLandingPageExperimentKey();
    case ExperimentName.TODO_LIST:
      return getCalendarReminderExperimentKey();
    case ExperimentName.RENEW_OVERDRAFT:
      return getRenewOverDraftExperimentKey();
    case ExperimentName.UPDATE_RENEW_INFO:
      return getUpdateRenewInfoExperimentKey();
    case ExperimentName.REPAY_VIETQR:
      return getRepayVietQRExperimentKey();
    case ExperimentName.ONBOARD_VIETQR:
      return getOnboardVietQRExperimentKey();
    case ExperimentName.SUBMIT_UPDATE_NFC_RENEW:
      return getSubmitUpdateNfcRenewExperimentKey();
    case ExperimentName.UPDATE_NFC:
      return getUpdateNfcExperimentKey();
    case ExperimentName.NEW_REPAYMENT_SCREEN:
      return getNewRepaymentScreenExperimentKey();
    case ExperimentName.INSTALLMENT_REGISTRATION:
      return getInstallmentRegistrationExperimentKey();
    case ExperimentName.LOTTE_FREE_TRIAL:
      return getLotteFreeTrialExperimentKey();
    case ExperimentName.CIMB_INFO_BANNER:
      return getCIMBInfoBannerExperimentKey();
    default:
      return '';
  }
};

const getAutoRepaymentExperimentKey = (): string => {
  switch (environment) {
    case Environment.QC_SANDBOX:
      return 'UG15_525';
    case Environment.STAGING:
      return 'UG9_163';
    default:
    case Environment.PRODUCTION:
      return 'UG7_1733';
  }
};

const getLandingPageExperimentKey = (): string => {
  switch (environment) {
    case Environment.QC_SANDBOX:
      return 'UG15_532';
    case Environment.STAGING:
      return 'UG9_184';
    default:
    case Environment.PRODUCTION:
      return 'UG7_1906';
  }
};

const getCalendarReminderExperimentKey = (): string => {
  switch (environment) {
    case Environment.QC_SANDBOX:
      return 'UG15_684';
    case Environment.STAGING:
      return 'UG9_193';
    default:
    case Environment.PRODUCTION:
      return 'UG7_2359';
  }
};

const getRenewOverDraftExperimentKey = (): string => {
  switch (environment) {
    case Environment.QC_SANDBOX:
      return 'UG15_720';
    case Environment.STAGING:
      return 'UG9_200';
    default:
    case Environment.PRODUCTION:
      return 'UG7_2533';
  }
};

const getUpdateRenewInfoExperimentKey = (): string => {
  switch (environment) {
    case Environment.QC_SANDBOX:
      return 'UG15_753';
    case Environment.STAGING:
      return 'UG9_207';
    default:
    case Environment.PRODUCTION:
      return 'UG7_2828';
  }
};

const getRepayVietQRExperimentKey = (): string => {
  switch (environment) {
    case Environment.QC_SANDBOX:
      return 'UG15_772';
    case Environment.STAGING:
      return 'UG9_209';
    default:
    case Environment.PRODUCTION:
      return 'UG7_3039';
  }
};

const getOnboardVietQRExperimentKey = (): string => {
  switch (environment) {
    case Environment.DEV_SANDBOX:
    case Environment.QC_SANDBOX:
      return 'UG15_854';
    case Environment.STAGING:
      return 'UG9_233';
    default:
    case Environment.PRODUCTION:
      return 'UG7_3880';
  }
};

const getSubmitUpdateNfcRenewExperimentKey = (): string => {
  switch (environment) {
    case Environment.DEV_SANDBOX:
    case Environment.QC_SANDBOX:
      return 'UG15_872';
    case Environment.STAGING:
      return 'UG9_239';
    default:
    case Environment.PRODUCTION:
      return 'UG7_4116';
  }
};

const getUpdateNfcExperimentKey = (): string => {
  switch (environment) {
    case Environment.DEV_SANDBOX:
    case Environment.QC_SANDBOX:
      return 'UG15_876';
    case Environment.STAGING:
      return 'UG9_243';
    default:
    case Environment.PRODUCTION:
      return 'UG7_4184';
  }
};

const getNewRepaymentScreenExperimentKey = (): string => {
  switch (environment) {
    case Environment.DEV_SANDBOX:
    case Environment.QC_SANDBOX:
      return 'UG1_891';
    case Environment.STAGING:
      return 'UG9_250';
    default:
    case Environment.PRODUCTION:
      return 'UG7_4240';
  }
};

const getInstallmentRegistrationExperimentKey = (): string => {
  switch (environment) {
    case Environment.DEV_SANDBOX:
    case Environment.QC_SANDBOX:
      return 'UG13_913';
    case Environment.STAGING:
      return 'UG9_264';
    default:
    case Environment.PRODUCTION:
      return 'UG7_4632';
  }
};

const getLotteFreeTrialExperimentKey = (): string => {
  switch (environment) {
    case Environment.DEV_SANDBOX:
    case Environment.QC_SANDBOX:
      return 'UG15_927';
    case Environment.STAGING:
      return 'UG9_272';
    default:
    case Environment.PRODUCTION:
      return 'UG7_4724';
  }
};

const getCIMBInfoBannerExperimentKey = (): string => {
  switch (environment) {
    case Environment.DEV_SANDBOX:
    case Environment.QC_SANDBOX:
      return 'UG15_942';
    case Environment.STAGING:
      return 'UG9_276';
    default:
    case Environment.PRODUCTION:
      return 'UG7_4748';
  }
};

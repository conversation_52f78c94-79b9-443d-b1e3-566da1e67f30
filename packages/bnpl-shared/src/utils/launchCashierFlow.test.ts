import { launchCashierFlow } from 'bnpl-shared/src/utils/launchCashierFlow';
import { Platform } from 'react-native';
import { waitFor } from '@testing-library/react-native';
import { ApiError } from 'bnpl-shared/src/shared/api';
import { payOrder } from 'bnpl-shared/src/shared/ZaloPayModules';

const mockPayload = { app_id: 123, zp_trans_token: '1234' };

describe(launchCashierFlow.name, () => {
  beforeEach(() => {
    (payOrder as jest.Mock).mockClear();
  });

  it('handle success ZPA', async () => {
    Platform.OS = 'android';
    (payOrder as jest.Mock).mockResolvedValue({ code: 1 });
    const result = await launchCashierFlow(mockPayload);
    expect(payOrder).toBeCalledTimes(1);
    await waitFor(() => expect(result).toBeTruthy());
  });

  it('handle payOrder return code != SUCCESS ZPA', async () => {
    Platform.OS = 'android';
    (payOrder as jest.Mock).mockResolvedValue({ code: -1, message: 'fail' });
    const result = launchCashierFlow(mockPayload);
    await expect(result).rejects.toThrowError(new ApiError(-1, 'fail'));
  });

  it('handle unexpect error with payOrder ZPA', async () => {
    Platform.OS = 'android';
    (payOrder as jest.Mock).mockResolvedValue({});
    const result = launchCashierFlow(mockPayload);
    await expect(result).rejects.toThrowError(new Error('error from cashier: unexpected'));
  });

  it('payOrder request with expected callback on ZPI', async () => {
    Platform.OS = 'web';
    (payOrder as jest.Mock).mockResolvedValue({ code: 1 });
    await launchCashierFlow(mockPayload);
    expect(payOrder).toHaveBeenCalledWith(
      { appid: mockPayload.app_id, zptranstoken: mockPayload.zp_trans_token, autoCheckoutVietQr: false },
      'https://socialdev.zalopay.vn/spa/v2/tai-khoan-tra-sau',
    );
  });
});

import { Platform } from 'react-native';
import { getLocationEnv, payOrder } from 'bnpl-shared/src/shared/ZaloPayModules';
import { CashierCode } from 'bnpl-shared/src/types';
import { ApiError } from 'bnpl-shared/src/shared/api';

export const launchCashierFlow = async (params: { app_id: number; zp_trans_token: string }) => {
  let callbackURL;
  if (Platform.OS === 'web') {
    const { appOrigin, zpiBaseUrl } = getLocationEnv();
    callbackURL = new URL(appOrigin + zpiBaseUrl);
    callbackURL = callbackURL.toString();
  }
  const orderSubmitResult = await payOrder(
    { appid: params.app_id, zptranstoken: params.zp_trans_token, autoCheckoutVietQr: false },
    callbackURL,
  );
  if (orderSubmitResult.code) {
    if (orderSubmitResult.code !== CashierCode.SUCCESS) {
      throw new ApiError(orderSubmitResult.code, `${orderSubmitResult?.message || 'Thất bại!'}`);
    }
  } else {
    if (orderSubmitResult.data?.reason !== 'USER_CANCEL') {
      throw new Error(`error from cashier: ${orderSubmitResult.data?.reason || 'unexpected'}`);
    }
  }
  return true;
};

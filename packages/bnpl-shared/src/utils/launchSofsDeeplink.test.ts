//write unit test for launchSofsDeeplink
import { launchSofsDeeplink } from './launchSofsDeeplink';
import { launchDeeplink } from 'bnpl-shared/src/lib/ZalopaySDK/launchDeeplink';
import { Environment, setEnvironment } from 'bnpl-shared/src/shared/environment';

jest.mock('bnpl-shared/src/lib/ZalopaySDK/launchDeeplink', () => ({
  launchDeeplink: jest.fn(),
}));

describe('launchSofsDeeplink', () => {
  it('launch app deeplink as expected on SBQC', () => {
    setEnvironment(Environment.DEV_SANDBOX);
    launchSofsDeeplink();
    expect(launchDeeplink).toBeCalledWith({
      zpa: `zalopay://launch/app/731`,
      zpi: window.__BASE_NAME__ + '/agreement-authorize',
    });
  });

  it('launch app deeplink as expected on Staging', () => {
    setEnvironment(Environment.STAGING);
    launchSofsDeeplink();
    expect(launchDeeplink).toBeCalledWith({
      zpa: `zalopay://launch/app/730`,
      zpi: window.__BASE_NAME__ + '/agreement-authorize',
    });
  });

  it('launch app deeplink as expected on Prod', () => {
    setEnvironment(Environment.PRODUCTION);
    launchSofsDeeplink();
    expect(launchDeeplink).toBeCalledWith({
      zpa: `zalopay://launch/app/730`,
      zpi: window.__BASE_NAME__ + '/agreement-authorize',
    });
  });
});

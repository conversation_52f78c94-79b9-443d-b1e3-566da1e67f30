import { environment, Environment } from 'bnpl-shared/src/shared/environment';
import { launchDeeplink } from 'bnpl-shared/src/lib/ZalopaySDK/launchDeeplink';

export const launchSofsDeeplink = () => {
  let appId = 731;
  switch (environment) {
    case Environment.STAGING:
    case Environment.PRODUCTION:
      appId = 730;
      break;
    default:
      appId = 731;
      break;
  }
  launchDeeplink({
    zpa: `zalopay://launch/app/${appId}`,
    zpi: window.__BASE_NAME__ + '/agreement-authorize',
  });
};

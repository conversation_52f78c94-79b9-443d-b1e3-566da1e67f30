import { useDispatch } from 'react-redux';
import { useAppSelector } from '../../redux/store';
import { usePartnerData } from 'bnpl-shared/src/features/multipartner_integration/helpers';
import { INVALID_ACCOUNT_ID, PartnerCode } from 'bnpl-shared/src/constants';
import { ResourceState, MiniBnplTotalOutstandingDueStatus, MiniBnplTotalOutstandingState, MiniBnplStatementConditions } from "bnpl-shared/src/types";
import { setMiniBnplTotalOutstanding } from "bnpl-shared/src/redux/miniBnplTotalOutstandingReducer";
import { getTotalOutstandingApi } from "bnpl-shared/src/api/mini-bnpl/getTotalOutstandingApi";

export const useMiniBnplStatementController = (): {
  fetchUserStatement: () => void;
  totalOutstandingData?: MiniBnplTotalOutstandingState | null;
  resourceState: ResourceState;
  stmConditions: MiniBnplStatementConditions;
} => {
  const dispatch = useDispatch();
  const { getPartnerDataByCode } = usePartnerData();
  const partnerData = getPartnerDataByCode(PartnerCode.MINI_BNPL);
  const totalOutstanding = useAppSelector(state => state.miniBnplTotalOutstanding.data);
  const totalOutstandingData = totalOutstanding.data;
  const isLoading = totalOutstanding.state === ResourceState.LOADING || totalOutstanding.state === ResourceState.INIT;
  const isExistOverDue: boolean = totalOutstandingData?.dueStatus === MiniBnplTotalOutstandingDueStatus.OVERDUE;
  const isExistOnDue: boolean = totalOutstandingData?.dueStatus === MiniBnplTotalOutstandingDueStatus.DUE;
  const isInvalid: boolean = !isLoading && !totalOutstandingData;

  const fetchUserStatement = async () => {
    if (partnerData.account_id && partnerData.account_id !== INVALID_ACCOUNT_ID) {
      try {
        dispatch(setMiniBnplTotalOutstanding({ state: ResourceState.LOADING, data: totalOutstandingData }));
        const statement = await getTotalOutstandingApi(partnerData.account_id);
        dispatch(setMiniBnplTotalOutstanding({
          state: ResourceState.READY,
          data: {
            dueStatus: statement.due_status,
            totalDueAmount: Number(statement.total_due_amount),
            dueDistance: Number(statement.due_distance),
          }
        }));
      } catch (err) {
        dispatch(setMiniBnplTotalOutstanding({ state: ResourceState.FAIL, data: null }));
      }
    }
  };

  return {
    fetchUserStatement,
    totalOutstandingData,
    resourceState: totalOutstanding.state,
    stmConditions: {
      isExistOverDue,
      isExistOnDue,
      isInvalid,
      isLoading,
    },
  };
};

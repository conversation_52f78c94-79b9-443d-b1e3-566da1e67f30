import { trackEvent } from 'bnpl-shared/src/utils/tracking/trackEvent';
import { EventTrackingId } from 'bnpl-shared/src/types';
export { useTouchTracker } from './hook';
export * from './pubsub';
export * from './type';

export const trackHomeScreenEvent = (eventId: string, payload?: any) =>
  trackEvent(`${EventTrackingId.HomeScreen}.${eventId}`, payload);

export const trackHomeUnbindScreenEvent = (eventId: string, payload?: any) =>
  trackEvent(`${EventTrackingId.HomeUnbindScreen}.${eventId}`, payload);

export const trackHomePreviewScreenEvent = (eventId: string, payload?: any) =>
  trackEvent(`${EventTrackingId.HomePreviewScreen}.${eventId}`, payload);

export const trackRepaymentScreenEvent = (eventId: string, payload?: any) =>
  trackEvent(`${EventTrackingId.RepaymentScreen}.${eventId}`, payload);

export const trackLockoutScreenEvent = (eventId: string, payload?: any) =>
  trackEvent(`${EventTrackingId.LockoutScreen}.${eventId}`, payload);

export const trackResubmitOnboardingScreenEvent = (eventId: string, payload?: any) =>
  trackEvent(`${EventTrackingId.ResubmitOnboardingScreen}.${eventId}`, payload);

export const trackTransactionDetailScreenEvent = (eventId: string, payload?: any) =>
  trackEvent(`${EventTrackingId.TransactionDetailScreen}.${eventId}`, payload);

export const trackTransactionHistoryScreenEvent = (eventId: string, payload?: any) =>
  trackEvent(`${EventTrackingId.TransactionHistoryScreen}.${eventId}`, payload);

export const trackUserGuideScreenEvent = (eventId: string, payload?: any) =>
  trackEvent(`${EventTrackingId.UserGuideScreen}.${eventId}`, payload);

export const trackSubmitOtpScreenEvent = (eventId: string, payload?: any) =>
  trackEvent(`${EventTrackingId.SubmitOtpScreen}.${eventId}`, payload);

export const trackAfterSubmitOtpModalEvent = (eventId: string, payload?: any) =>
  trackEvent(`${EventTrackingId.AfterSubmitOtpModal}.${eventId}`, payload);

export const trackOnboardingScreenEvent = (eventId: string, payload?: any) =>
  trackEvent(`${EventTrackingId.OnboardingScreen}.${eventId}`, payload);

export const trackOnboardingChurnModalEvent = (eventId: string, payload?: any) =>
  trackEvent(`${EventTrackingId.OnboardingChurnModal}.${eventId}`, payload);

export const trackOnboardingResultModalEvent = (eventId: string, payload?: any) =>
  trackEvent(`${EventTrackingId.OnboardingResultModal}.${eventId}`, payload);

export const trackSignSubmitScreenEvent = (eventId: string, payload?: any) =>
  trackEvent(`${EventTrackingId.SignSubmitScreen}.${eventId}`, payload);

export const trackBod2ChurnModalEvent = (eventId: string, payload?: any) =>
  trackEvent(`${EventTrackingId.Bod2ChurningModal}.${eventId}`, payload);

export const trackBod2ScreenEvent = (eventId: string, payload?: any) =>
  trackEvent(`${EventTrackingId.Bod2Screen}.${eventId}`, payload);

export const trackBod2SubmitResultModalEvent = (eventId: string, payload?: any) =>
  trackEvent(`${EventTrackingId.Bod2SubmitResultModal}.${eventId}`, payload);

export const trackApplicationReviewScreenEvent = (eventId: string, payload?: any) =>
  trackEvent(`${EventTrackingId.ApplicationReviewScreen}.${eventId}`, payload);

export const trackUtilitySectionEvent = (eventId: string, payload?: any) =>
  trackEvent(`${EventTrackingId.UtilitySection}.${eventId}`, payload);

export const trackFaceChallengeScreenEvent = (eventId: string, payload?: any) =>
  trackEvent(`${EventTrackingId.FaceChallengeScreen}.${eventId}`, payload);

export const trackRenewFaceChallengeScreenEvent = (eventId: string, payload?: any) =>
  trackEvent(`${EventTrackingId.RenewFaceChallengeScreen}.${eventId}`, payload);

export const trackNonWhitelistOnboardingScreenEvent = (eventId: string, payload?: any) =>
  trackEvent(`${EventTrackingId.NonWhitelistOnboardingScreen}.${eventId}`, payload);

export const trackUpdateNfcDataModalEvent = (eventId: string, payload?: any) =>
  trackEvent(`${EventTrackingId.UpdateNfcDataModal}.${eventId}`, payload);

export const trackSplashScreenEvent = (eventId: string, payload?: any) =>
  trackEvent(`${EventTrackingId.SplashScreen}.${eventId}`, payload);

export const trackEmbeddedOnboardingWaitingApprovalScreenEvent = (eventId: string, payload?: any) =>
  trackEvent(`${EventTrackingId.EmbeddedOnboardingWaitingApprovalScreen}.${eventId}`, payload);

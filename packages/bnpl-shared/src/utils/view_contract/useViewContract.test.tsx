import { useViewContract } from 'bnpl-shared/src/utils/view_contract/useViewContract';
import { getBindingContractApi } from 'bnpl-shared/src/api/getBindingContractApi';
import { getBindingContractApiBuilder } from 'bnpl-shared/src/api/__mocks__/getBindingContractApi';
import { renderReduxWithNavigationWrapper } from 'bnpl-shared/src/jest/renderWithRedux';
import { fireEvent, waitFor } from '@testing-library/react-native';
import React, { FC } from 'react';
import InfoModal from 'bnpl-shared/src/components/InfoModal';
import { infoModalRef, InfoModalService } from 'bnpl-shared/src/services';
import { Button } from 'react-native';
import ContractModal from 'bnpl-shared/src/screens/HomeScreen/components/UtilitySection/ContractModal';
import { PartnerCode } from 'bnpl-shared/src/constants';

jest.mock('bnpl-shared/src/utils/verify_pin', () => ({
  useVerifyPIN: () => ({
    verifyPIN: jest.fn().mockResolvedValue({ code: 1 }),
  }),
}));

jest.mock('bnpl-shared/src/shared/ZaloPayModules', () => ({
  promptPINRN: jest.fn().mockReturnValue({ code: 1 }),
  showLoading: jest.fn(),
  hideLoading: jest.fn(),
}));

const Subject: FC<{ request_id: string }> = ({ request_id }) => {
  const { handleViewContract, setContractVisible, contractInfo, contractVisible } = useViewContract(PartnerCode.CIMB);
  return (
    <>
      <Button title={'show'} onPress={() => handleViewContract(request_id)} />
      <ContractModal
        onRequestClose={() => setContractVisible(false)}
        contractUrl={contractInfo?.url || ''}
        password={contractInfo?.password}
        visible={contractVisible}
      />
      <InfoModal ref={infoModalRef} />
    </>
  );
};

describe(useViewContract.name, () => {
  beforeEach(() => {
    InfoModalService.hideModal();
    (getBindingContractApi as jest.Mock).mockClear();
  });

  it('show contract modal when contract url is ready', async () => {
    (getBindingContractApi as jest.Mock).mockResolvedValue(getBindingContractApiBuilder.build());
    const { getByText, queryByText, getByTestId } = renderReduxWithNavigationWrapper(<Subject request_id={'1234'} />);
    fireEvent.press(getByText('show'));
    await waitFor(() => expect(getBindingContractApi).toHaveBeenCalledWith({ request_id: '1234' }));
    await waitFor(() => expect(queryByText('Xem hợp đồng')).toBeTruthy());
    fireEvent.press(getByTestId('close-bottom-sheet-button'));
  });

  it('show empty contract modal when contract url is empty', async () => {
    (getBindingContractApi as jest.Mock).mockResolvedValue(getBindingContractApiBuilder.build('empty_contract_url'));
    const { getByText, queryByText } = renderReduxWithNavigationWrapper(<Subject request_id={'1234'} />);
    fireEvent.press(getByText('show'));
    await waitFor(() =>
      expect(queryByText('Hợp đồng của bạn đang được xử lý. Bạn vui lòng chờ trong giây lát nhé.')).toBeTruthy(),
    );
  });

  it('show error modal when fetching binding contract api is failed', async () => {
    (getBindingContractApi as jest.Mock).mockRejectedValue(new Error('error'));
    const { getByText, queryByText } = renderReduxWithNavigationWrapper(<Subject request_id={'1234'} />);
    fireEvent.press(getByText('show'));
    await waitFor(() => expect(queryByText('Hệ thống đang gặp gián đoạn')).toBeTruthy());
  });

  it('only fetching binding contract api when contract url is not valid', async () => {
    (getBindingContractApi as jest.Mock).mockResolvedValue(getBindingContractApiBuilder.build());
    const { getByText, getByTestId, queryByText } = renderReduxWithNavigationWrapper(<Subject request_id={'1234'} />);
    fireEvent.press(getByText('show'));
    await waitFor(() => expect(queryByText('Xem hợp đồng')).toBeTruthy());
    fireEvent.press(getByTestId('close-bottom-sheet-button'));
    expect(queryByText('Xem hợp đồng')).toBeFalsy();
    fireEvent.press(getByText('show'));
    expect(getBindingContractApi).toHaveBeenCalledWith({ request_id: '1234' });
  });
});

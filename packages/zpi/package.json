{"name": "bnpl-zpi", "version": "4.12.0", "private": true, "dependencies": {"@testing-library/jest-dom": "^5.14.1", "@testing-library/react": "^12.0.0", "@testing-library/user-event": "^13.2.1", "@types/jest": "^27.0.1", "@types/node": "^16.7.13", "@types/react": "17.0.2", "@types/react-dom": "^16.9.14", "@zpi/single-spa-react": "1.0.1-alpha.2", "mobile-detect": "^1.4.5", "react": "17.0.2", "react-app-polyfill": "^3.0.0", "react-dom": "17.0.2", "react-native-elements": "^3.4.2", "react-native-web": "^0.17.6", "react-native-web-linear-gradient": "^1.1.2", "react-native-web-webview": "^1.0.2", "react-router-dom": "5.2.0", "react-scripts": "5.0.0", "signature_pad": "^4.1.4", "swiper": "^8.0.6", "typescript": "^4.4.2", "web-vitals": "^2.1.0"}, "scripts": {"start": "craco start", "build": "craco build", "serve": "serve -s build -l 8080", "test": "craco test", "eject": "react-scripts eject", "ma-start": "micro-app-scripts start", "wp-build": "craco build", "version-dev": "npm version prerelease --preid=alpha --no-git-tag-version --no-commit-hooks", "version-stg": "npm version prerelease --preid=beta --no-git-tag-version --no-commit-hooks"}, "eslintConfig": {"extends": ["react-app", "react-app/jest"]}, "browserslist": {"production": ["Android >= 4", "iOS >= 8", "ChromeAndroid >= 1", "not dead", "not op_mini all"], "development": ["last 1 chrome version", "last 1 firefox version", "last 1 safari version"]}, "devDependencies": {"@babel/plugin-syntax-jsx": "^7.16.7", "@babel/preset-env": "^7.16.11", "@babel/preset-react": "^7.16.7", "@craco/craco": "7.0.0-alpha.3", "@zpi/micro-app-cli": "1.0.5", "babel-plugin-react-native-web": "^0.17.5", "craco-babel-loader": "^1.0.3", "customize-cra": "^1.0.0", "eslint-plugin-testing-library": "^5.0.5", "http-proxy-middleware": "^2.0.6", "react-app-rewire-yarn-workspaces": "^1.0.3"}}